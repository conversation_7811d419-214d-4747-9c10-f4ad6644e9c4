web-ocean-wave-management
======================

web-ocean-wave-management是业务架构部创作者平台的运营管理后台项目，提供面向开发和运营人员的平台业务配置能力。

相关地址
----------------------

| 内容   | 环境 | 地址                                                                                 |
|------|----|------------------------------------------------------------------------------------|
| 飞书文档 |    | https://lizhi2021.feishu.cn/wiki/wikcn9SecUhfrtZ7SLQqyBdNDng                       |
| 单点登录 |    | https://lizhi2021.feishu.cn/wiki/W7eWwZ47yiTzGAkIrt3cHoX7nlI                       |
| 配置中心 | 测试 | http://configportalinoffice.lizhi.fm/config.html?#/appid=web_ocean_wave_management |
| 配置中心 | 线上 | https://configportalin.lizhi.fm/config.html?appid=web_ocean_wave_management        |
| 接口文档 |    | https://sextant.vico.183me.com/#/project/1840636638545866753                       |
| 域名地址 |    | https://poseidon.vico.183me.com/#/shuijingqiu/platformtools                        |
| 权限申请 |    | https://lizhi2021.feishu.cn/wiki/W7eWwZ47yiTzGAkIrt3cHoX7nlI                       |

编译部署
----------------------

+ **IDE启动服务**

  在执行入口类上

  ```
  fm.lizhi.ocean.wave.management.Application
  ```

  添加VM options（vtag即迭代标签）

  ```shell
  -Dmetadata.region=cn -Dmetadata.business.env=public -Dmetadata.deploy.env=test -Dmetadata.service.name=web_ocean_wave_management -DLOG_PATH=/data/logs/lizhi/web_ocean_wave_management -DCAT_HOME=/data/logs/cat/web_ocean_wave_management -Dvtag=common
  ```
  
  然后即可启动服务

+ **本地编译**

  提交代码之前先本地编译一遍

  ```shell
  mvn clean compile
  ```

+ **本地打包**

  灯塔依赖有问题先本地打包检查（lz.env即环境）

  ```shell
  mvn clean package -Dmaven.test.skip=true -Dlz.env=office
  ```

  打包产物是tar.gz压缩包，解压即可

  ```
  target/web-ocean-wave-management-版本号-deploy.tar.gz
  ```

+ **正式发布**

  发布前确保已修改正式版本号

  ```shell
  mvn versions:set -DnewVersion=新版本号 -DgenerateBackupPoms=false
  ```

  打印SNAPSHOT依赖树，检查是否有SNAPSHOT

  ```shell
  mvn org.apache.maven.plugins:maven-dependency-plugin:3.3.0:tree  -Dincludes=':::*-SNAPSHOT'
  ```