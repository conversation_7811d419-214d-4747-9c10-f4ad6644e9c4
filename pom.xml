<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>fm.lizhi.ocean.wave</groupId>
    <artifactId>web-ocean-wave-management</artifactId>
    <version>1.0.8</version>

    <properties>
        <!-- ====================        项目构建的属性        ==================== -->

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven-resources-plugin.version>3.3.1</maven-resources-plugin.version>
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <maven.compiler.parameters>true</maven.compiler.parameters>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <maven-surefire-plugin.version>3.1.2</maven-surefire-plugin.version>
        <maven-jar-plugin.version>3.3.0</maven-jar-plugin.version>
        <maven-source-plugin.version>3.3.0</maven-source-plugin.version>
        <maven-install-plugin.version>3.1.1</maven-install-plugin.version>
        <maven.install.skip>true</maven.install.skip>
        <maven-deploy-plugin.version>3.1.1</maven-deploy-plugin.version>
        <maven.deploy.skip>true</maven.deploy.skip>
        <maven-assembly-plugin.version>2.6</maven-assembly-plugin.version>
        <versions-maven-plugin.version>2.16.0</versions-maven-plugin.version>
        <sonar-maven-plugin.version>3.11.0.3922</sonar-maven-plugin.version>


        <!-- ====================        基础架构的属性        ==================== -->

        <lz-common-dependencies-bom.version>2.0.53</lz-common-dependencies-bom.version>


        <!-- ====================        第二方框架属性        ==================== -->

        <lamp-common.version>1.1.12</lamp-common.version>
        <wave-server-common-context.version>1.0.13</wave-server-common-context.version>


        <!-- ====================        第三方框架属性        ==================== -->

        <lombok.version>1.18.34</lombok.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <mapstruct.version>1.6.2</mapstruct.version>
        <annotations.version>24.0.0</annotations.version>
        <jakarta.validation-api.version>2.0.2</jakarta.validation-api.version>
        <hibernate-validator.version>6.0.16.Final</hibernate-validator.version>
        <commons-io.version>2.17.0</commons-io.version>
        <guava.version>19.0</guava.version>
        <jackson-module-kotlin.version>2.9.8</jackson-module-kotlin.version>
        <fastjson.version>1.2.83</fastjson.version>
        <transmittable-thread-local.version>2.14.3</transmittable-thread-local.version>
        <apm-toolkit-trace.version>5.0.1-RC1</apm-toolkit-trace.version>
        <okhttp.version>4.9.3</okhttp.version>
        <kotlin-stdlib.version>1.3.70</kotlin-stdlib.version>
        <easyexcel.version>3.3.2</easyexcel.version>
        <hutool.version>5.8.27</hutool.version>


        <!-- ====================        基础架构服务属性        ==================== -->

        <smart-sso-client.version>1.0.12</smart-sso-client.version>
        <lz-common-romefs-javasdk.version>2.5</lz-common-romefs-javasdk.version>


        <!-- ====================        第二方服务属性        ==================== -->

        <wavecenter-api.version>2.0.6-SNAPSHOT</wavecenter-api.version>
        <lz-ocean-wave-api.version>1.0.18</lz-ocean-wave-api.version>
        <lz-ocean-godzilla-api.version>1.6.3</lz-ocean-godzilla-api.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- ====================        基础架构的依赖        ==================== -->

            <!-- 当前项目是单模块项目, 只有bom使用dependencyManagement配置, 其他依赖使用dependencies配置 -->
            <!-- 基础架构bom -->
            <dependency>
                <groupId>fm.lizhi.common</groupId>
                <artifactId>lz-common-dependencies-bom</artifactId>
                <version>${lz-common-dependencies-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- ====================        基础架构的依赖        ==================== -->

        <!-- 基础架构日志 -->
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>lz-common-logging-starter</artifactId>
        </dependency>
        <!-- 基础架构微服务 -->
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-serviceclient</artifactId>
        </dependency>
        <!-- 基础架构配置 -->
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-config</artifactId>
        </dependency>
        <!-- 基础架构消息队列 -->
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>kafka-client-spring-config</artifactId>
        </dependency>
        <!-- 基础架构测试 -->
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-unit</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- 基础架构mysql -->
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>datastore-mysql-spring-boot-starter</artifactId>
        </dependency>

        <!-- ====================        第二方框架依赖        ==================== -->

        <!-- 神灯配置组件 -->
        <dependency>
            <groupId>fm.lizhi.ocean.lamp</groupId>
            <artifactId>lamp-common-config</artifactId>
            <version>${lamp-common.version}</version>
        </dependency>
        <!-- 神灯日志组件 -->
        <dependency>
            <groupId>fm.lizhi.ocean.lamp</groupId>
            <artifactId>lamp-common-logging</artifactId>
            <version>${lamp-common.version}</version>
        </dependency>
        <!-- 神灯工具组件 -->
        <dependency>
            <groupId>fm.lizhi.ocean.lamp</groupId>
            <artifactId>lamp-common-util</artifactId>
            <version>${lamp-common.version}</version>
        </dependency>
        <!-- 创作者通用上下文 -->
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-server-common-context</artifactId>
            <version>${wave-server-common-context.version}</version>
        </dependency>


        <!-- ====================        第三方框架依赖        ==================== -->

        <!-- 注解处理器, 统一声明为optional -->
        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <optional>true</optional>
        </dependency>
        <!-- mapstruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
            <optional>true</optional>
        </dependency>
        <!-- mapstruct注解处理器 -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
            <optional>true</optional>
        </dependency>
        <!-- lombok-mapstruct注解处理器 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <version>${lombok-mapstruct-binding.version}</version>
            <optional>true</optional>
        </dependency>
        <!-- jetbrains静态代码检查注解 -->
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>${annotations.version}</version>
        </dependency>
        <!-- spring配置注解处理器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- apache commons依赖 -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons-io.version}</version>
        </dependency>

        <!-- spring boot & spring依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- 其他框架依赖 -->
        <!-- 服务监控 -->
        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-client</artifactId>
        </dependency>
        <!-- 熔断限流 -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-all</artifactId>
        </dependency>
        <!-- bean校验api, 支持泛型注解 -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <version>${jakarta.validation-api.version}</version>
        </dependency>
        <!-- hibernate校验实现, 移除掉validation-api, 采用jakarta.validation-api -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>${hibernate-validator.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- jackson kotlin -->
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
            <version>${jackson-module-kotlin.version}</version>
        </dependency>
        <!-- guava -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>
        <!-- fastjson -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <!-- 线程私有变量异步传递 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>${transmittable-thread-local.version}</version>
        </dependency>
        <!-- 灯塔支持跨线程路由 -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
            <version>${apm-toolkit-trace.version}</version>
        </dependency>
        <!-- okhttp罗马要求是4.x的版本 -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
        </dependency>
        <!-- kotlin也是罗马要求的版本 -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>${kotlin-stdlib.version}</version>
        </dependency>
        <!-- easyexcel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>


        <!-- ====================        基础架构服务依赖        ==================== -->

        <!-- 单点登录 -->
        <dependency>
            <groupId>fm.lizhi.sso</groupId>
            <artifactId>smart-sso-client</artifactId>
            <version>${smart-sso-client.version}</version>
        </dependency>
        <!-- 罗马上传 -->
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>lz-common-romefs-javasdk</artifactId>
            <version>${lz-common-romefs-javasdk.version}</version>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>datastore-redis-spring-boot-starter</artifactId>
        </dependency>

        <!-- ====================        第二方服务依赖        ==================== -->

        <!-- 创作者中心api -->
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-api</artifactId>
            <version>${wavecenter-api.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>anchor-api-component</artifactId>
            <version>${wavecenter-api.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>anchor-datastore-component</artifactId>
            <version>${wavecenter-api.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>award-datastore-component</artifactId>
            <version>${wavecenter-api.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>award-api-component</artifactId>
            <version>${wavecenter-api.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>family-api-component</artifactId>
            <version>${wavecenter-api.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>family-datastore-component</artifactId>
            <version>${wavecenter-api.version}</version>
        </dependency>
        <!-- 创作者PC DC api -->
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>lz-ocean-wave-api</artifactId>
            <version>${lz-ocean-wave-api.version}</version>
        </dependency>
        <!-- Godzilla DC api -->
        <dependency>
            <groupId>fm.lizhi.ocean.godzilla</groupId>
            <artifactId>lz-ocean-godzilla-api</artifactId>
            <version>${lz-ocean-godzilla-api.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-server-push-autoconfigure</artifactId>
            <version>${wave-server-common-context.version}</version>
        </dependency>
    </dependencies>

    <build>
        <!-- 资源和过滤器声明, 将maven项目变量替换到application.properties中 -->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>application.properties</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>application.properties</exclude>
                </excludes>
                <filtering>true</filtering>
            </resource>
        </resources>
        <!-- 当前项目是单模块项目, 因此不使用pluginManagement, 直接使用plugins配置 -->
        <plugins>
            <!-- 资源插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven-resources-plugin.version}</version>
                <configuration>
                    <propertiesEncoding>ISO-8859-1</propertiesEncoding>
                </configuration>
            </plugin>
            <!-- 编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
            </plugin>
            <!-- 测试插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
            </plugin>
            <!-- jar插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven-jar-plugin.version}</version>
            </plugin>
            <!-- 源码插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven-source-plugin.version}</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- 安装插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <version>${maven-install-plugin.version}</version>
            </plugin>
            <!-- 部署插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>${maven-deploy-plugin.version}</version>
            </plugin>
            <!-- 打包插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>${maven-assembly-plugin.version}</version>
                <configuration>
                    <descriptors>
                        <descriptor>src/main/assembly/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- 版本插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>${versions-maven-plugin.version}</version>
            </plugin>
            <!-- sonar插件 -->
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>${sonar-maven-plugin.version}</version>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>central</id>
            <name>Central</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
        </repository>
        <repository>
            <id>codehaus-snapshots</id>
            <name>Codehaus Snapshots</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>central</id>
            <name>Maven Plugin Repository</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
            <layout>default</layout>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <updatePolicy>never</updatePolicy>
            </releases>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>release</id>
            <url>http://maven.lizhi.fm:8081/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://maven.lizhi.fm:8081/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>