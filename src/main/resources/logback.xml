<?xml version="1.0"?>
<configuration>
    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} traceId=%X{traceId:-N/A} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="fm.lizhi.commons.connector.async.transport" level="error" additivity="false">
        <appender-ref ref="stdout"/>
    </logger>
    <root level="DEBUG">
        <appender-ref ref="stdout"/>
    </root>
</configuration>
