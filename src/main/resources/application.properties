# spring autoconfigure
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration

# spring aop
spring.aop.proxy-target-class=true

# project version, read from pom.xml
project.version=${project.version}

# lamp logging expressions
lamp.common.logging.trace.expressions=@within(org.springframework.web.bind.annotation.RestController)
# lamp logging order, -2147483648 + 200
lamp.common.logging.trace.advisor-order=-2147483448
