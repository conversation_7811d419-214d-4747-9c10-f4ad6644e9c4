package fm.lizhi.ocean.wave.management.model.converter.activitycenter;

import fm.lizhi.ocean.wave.management.model.param.activitycenter.BatchGetDecorateParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.PageDecorateParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.DressUpResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.DecorateBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestBatchGetDecorate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestGetDecorate;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityDecorateConvert {

    ActivityDecorateConvert I = Mappers.getMapper(ActivityDecorateConvert.class);

    @Mappings({
            @Mapping(source = "appId", target = "appId")
    })
    RequestGetDecorate toRequestGetDecorate(PageDecorateParam param, Integer appId);

    PageVO<DressUpResult> toDressUpResultPageVO(PageBean<DecorateBean> target);

    List<DressUpResult> toDressUpResult(List<DecorateBean> target);

    @Mappings({
            @Mapping(source = "appId", target = "appId")
    })
    RequestBatchGetDecorate toRequestBatchGetDecorate(BatchGetDecorateParam param, Integer appId);
}
