package fm.lizhi.ocean.wave.management.model.result.utility.webp;

import com.fasterxml.jackson.annotation.JsonProperty;
import fm.lizhi.ocean.wave.platform.api.platform.webp.constant.WebpConvertErrorCodeConstants;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.beans.Transient;

/**
 * webp转换node服务统一泛型外层
 *
 * @param <T> 泛型类型
 */
@Data
public class WebpNodeResult<T> {

    public static final int SUCCESS = 0;

    /**
     * 状态码, 0表示成功, 其他表示失败
     */
    @JsonProperty("rCode")
    private Integer rCode;

    /**
     * 错误消息, 当不为成功时有值, 用于输出失败原因
     */
    private String msg;

    /**
     * 结果数据, 当成功时有值
     */
    private T data;

    public static <T> WebpNodeResult<T> failure(String msg) {
        WebpNodeResult<T> result = new WebpNodeResult<>();
        result.setRCode(WebpConvertErrorCodeConstants.CONVERT_FAILURE);
        result.setMsg(msg);
        return result;
    }

    public static <T> WebpNodeResult<T> exception(Exception e) {
        if (StringUtils.isNotBlank(e.getMessage())) {
            return WebpNodeResult.failure("转换异常" + e.getClass().getName() + ": " + e.getMessage());
        } else {
            return WebpNodeResult.failure("转换异常" + e.getClass().getName());
        }
    }

    public static <T> WebpNodeResult<T> timeout(String msg) {
        WebpNodeResult<T> result = new WebpNodeResult<>();
        result.setRCode(WebpConvertErrorCodeConstants.CONVERT_TIMEOUT);
        result.setMsg(msg);
        return result;
    }

    @Transient
    public boolean isTimeout() {
        return rCode == WebpConvertErrorCodeConstants.CONVERT_TIMEOUT;
    }

    @Transient
    public boolean isFailure() {
        return !(rCode == SUCCESS);
    }
}
