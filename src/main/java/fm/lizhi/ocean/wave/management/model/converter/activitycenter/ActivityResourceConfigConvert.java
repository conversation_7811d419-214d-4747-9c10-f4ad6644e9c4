package fm.lizhi.ocean.wave.management.model.converter.activitycenter;

import fm.lizhi.ocean.wave.management.model.param.activitycenter.PageActivityResourcesParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityResourceParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityResourceParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.GetActivityResourceResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.PageActivityResourceResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestPageActivityResources;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityResource;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityResourceConfigConvert {

    ActivityResourceConfigConvert I = Mappers.getMapper(ActivityResourceConfigConvert.class);

    @Mappings({
            @Mapping(source = "appId", target = "appId"),
            @Mapping(source = "operator", target = "operator"),
    })
    RequestSaveActivityResource toRequestSaveActivityResource(SaveActivityResourceParam param, int appId, String operator);

    @Mappings({
            @Mapping(source = "appId", target = "appId"),
            @Mapping(source = "operator", target = "operator"),
    })
    RequestUpdateActivityResource toRequestUpdateActivityResource(UpdateActivityResourceParam param, int appId, String operator);

    PageVO<PageActivityResourceResult> toPageActivityResourceResultPageVO(PageBean<ResponseActivityResource> target);

    @Mappings({
            @Mapping(source = "appId", target = "appId"),
    })
    RequestPageActivityResources toRequestPageActivityResources(int appId, PageActivityResourcesParam param);

    List<GetActivityResourceResult> toGetActivityResourceResult(List<ResponseActivityResource> target);

}
