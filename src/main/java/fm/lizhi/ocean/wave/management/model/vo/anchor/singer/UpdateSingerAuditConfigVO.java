package fm.lizhi.ocean.wave.management.model.vo.anchor.singer;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class UpdateSingerAuditConfigVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 配置编码
     */
    private String configCode;

    /**
     * 状态
     */
    private Boolean enabled;

    /**
     * 配置说明
     */
    private String explanation;

}
