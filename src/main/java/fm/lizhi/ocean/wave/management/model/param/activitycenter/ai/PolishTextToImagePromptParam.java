package fm.lizhi.ocean.wave.management.model.param.activitycenter.ai;

import fm.lizhi.ocean.godzilla.api.constant.ActivityPlanImageSceneEnum;
import fm.lizhi.ocean.godzilla.api.constant.ActivityPlanImageStyleEnum;
import fm.lizhi.ocean.wave.management.common.validation.EnumValue;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 润色文生图提示词参数
 */
@Data
public class PolishTextToImagePromptParam {

    /**
     * 会话id
     */
    @NotNull(message = "会话id不能为空")
    @Size(max = 64, message = "会话id长度不能超过{max}个字符")
    private String sessionId;

    /**
     * 图片风格
     */
    @NotNull(message = "图片风格不能为空")
    @EnumValue(value = ActivityPlanImageStyleEnum.class, message = "图片风格不合法")
    private Integer imageStyle;

    /**
     * 图片场景
     */
    @NotNull(message = "图片场景不能为空")
    @EnumValue(value = ActivityPlanImageSceneEnum.class, message = "图片场景不合法")
    private Integer imageScene;

    /**
     * 用户输入
     */
    @NotNull(message = "用户输入不能为空")
    @Size(min = 1, max = 500, message = "用户输入长度必须在{min}到{max}个字符之间")
    private String userInput;
}
