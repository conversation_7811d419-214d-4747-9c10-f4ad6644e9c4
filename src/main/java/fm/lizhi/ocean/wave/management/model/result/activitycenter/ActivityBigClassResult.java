package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 活动大类
 *
 * <AUTHOR>
 * @date 2024-10-10 06:34:31
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ActivityBigClassResult {

    /**
     * 大类ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 大类名称
     */
    private String name;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 品类列表
     */
    private List<Integer> categoryList;
}