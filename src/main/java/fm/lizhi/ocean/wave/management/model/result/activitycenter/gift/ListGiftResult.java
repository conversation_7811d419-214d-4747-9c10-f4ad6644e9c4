package fm.lizhi.ocean.wave.management.model.result.activitycenter.gift;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.api.gift.constants.GiftStatusEnum;
import lombok.Data;

/**
 * 分页查询礼物响应的结果
 */
@Data
public class ListGiftResult {

    /**
     * 礼物id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 礼物名称
     */
    private String name;

    /**
     * 礼物封面
     */
    private String cover;

    /**
     * 礼物状态
     *
     * @see GiftStatusEnum
     */
    private Integer status;
}
