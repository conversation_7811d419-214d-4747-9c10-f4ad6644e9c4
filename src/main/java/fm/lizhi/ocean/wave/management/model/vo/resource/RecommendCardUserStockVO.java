package fm.lizhi.ocean.wave.management.model.vo.resource;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/17 17:25
 */
@Data
public class RecommendCardUserStockVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    private String userBand;

    private String userName;

    private String userRole;

    private String familyName;

    /**
     * 库存
     */
    private Integer stock;

    /**
     * 获取数量
     */
    private Integer expireNum;

}
