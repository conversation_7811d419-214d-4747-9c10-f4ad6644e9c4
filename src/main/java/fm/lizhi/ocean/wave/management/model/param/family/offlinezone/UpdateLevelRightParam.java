package fm.lizhi.ocean.wave.management.model.param.family.offlinezone;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 更新等级权益参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UpdateLevelRightParam extends SaveLevelRightParam {

    /**
     * 权益ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;
}
