package fm.lizhi.ocean.wave.management.controller.family.offlinezone;

import fm.lizhi.ocean.wave.management.manager.family.offlinezone.LevelRightManager;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.AddLevelRightParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.DeleteLevelRightParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.UpdateLevelRightParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.AddLevelRightResult;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListLevelRightResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 线下专区等级权益控制器
 */
@RestController
@RequestMapping("/offline/levelRight")
@Slf4j
public class LevelRightController {

    @Autowired
    private LevelRightManager levelRightManager;

    /**
     * 新增等级权益
     *
     * @param param 新增参数
     * @return 新增结果的VO
     */
    @PostMapping("/add")
    public ResultVO<AddLevelRightResult> addLevelRight(@RequestBody @Validated AddLevelRightParam param) {
        return levelRightManager.addLevelRight(param);
    }

    /**
     * 更新等级权益
     *
     * @param param 更新参数
     * @return 更新结果的VO
     */
    @PostMapping("/update")
    public ResultVO<Void> updateLevelRight(@RequestBody @Validated UpdateLevelRightParam param) {
        return levelRightManager.updateLevelRight(param);
    }

    /**
     * 删除等级权益
     *
     * @param param 删除参数
     * @return 删除结果的VO
     */
    @PostMapping("/delete")
    public ResultVO<Void> deleteLevelRight(@RequestBody @Validated DeleteLevelRightParam param) {
        return levelRightManager.deleteLevelRight(param);
    }

    /**
     * 列出所有等级权益
     *
     * @return 等级权益列表的VO
     */
    @GetMapping("/list")
    public ResultVO<List<ListLevelRightResult>> listLevelRight() {
        return levelRightManager.listLevelRight();
    }
}
