package fm.lizhi.ocean.wave.management.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.manager.ActivityApplyManager;
import fm.lizhi.ocean.wave.management.manager.file.FileExportManager;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ActivityApplyConvert;
import fm.lizhi.ocean.wave.management.model.converter.award.family.FamilyAwardDeliverConvert;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.*;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.*;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.activitycenter.UserActivitySimpleInfoExportVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerSingHallApplyRecordSummaryExcelVO;
import fm.lizhi.ocean.wave.management.model.vo.award.family.AwardDeliverRecordV1ExcelVO;
import fm.lizhi.ocean.wave.management.model.vo.file.FileExportVO;
import fm.lizhi.ocean.wave.management.remote.service.activitycenter.ActivityApplyServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestQueryUserActivitiesBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ResponseQueryUserActivitiesBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.AwardDeliverRecordV1Bean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestAwardDeliverListRecordV1;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageParamBean;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/activity")
public class ActivityApplyController {


    @Autowired
    private ActivityApplyManager activityApplyManager;
    @Autowired
    private FileExportManager fileExportManager;
    @Autowired
    private ActivityApplyServiceRemote activityApplyServiceRemote;

    /**
     * 驳回申请
     *
     * @param param 参数
     * @return 结果
     */
    @PostMapping("/apply/reject")
    public ResultVO<Void> rejectApply(@RequestBody ActivityAuditRejectParam param) {
        param.setAppId(SessionExtUtils.getAppId());
        return activityApplyManager.rejectActivityApply(param, SessionUtils.getAccount());
    }

    @GetMapping("/apply/query")
    public ResultVO<QueryUserActivitiesResult> queryUserActivities(@Validated QueryUserActivitiesParamVO userActivitiesParamVO) {
        // 检查时间范围是否都为空
        boolean activityTimeEmpty = (userActivitiesParamVO.getStartTime() == null || userActivitiesParamVO.getStartTime() == 0)
                   && (userActivitiesParamVO.getEndTime() == null || userActivitiesParamVO.getEndTime() == 0);
        boolean applyTimeEmpty = isApplyTimeEmpty(userActivitiesParamVO);
        if (activityTimeEmpty && applyTimeEmpty) {
            return ResultVO.failure("提报时间与活动开始时间不能同时为空");
        }

        // 检查活动时间范围
        if (isInvalidTimeRange(userActivitiesParamVO.getStartTime(), userActivitiesParamVO.getEndTime())) {
            return ResultVO.failure("开始时间不能大于结束时间");
        }

        // 检查提报时间范围
        if (isInvalidApplyTimeRange(userActivitiesParamVO)) {
            return ResultVO.failure("提报开始时间不能大于结束时间");
        }

        return activityApplyManager.queryUserActivities(userActivitiesParamVO, SessionExtUtils.getAppId());
    }

    @GetMapping("/apply/query/export")
    public void exportUserActivities(QueryUserActivitiesExportParamVO userActivitiesParamVO, HttpServletResponse response) {
        String fileName = "自助提报审批";
        try {
            // 检查时间范围是否都为空
            boolean activityTimeEmpty = (userActivitiesParamVO.getStartTime() == null || userActivitiesParamVO.getStartTime() == 0)
                    && (userActivitiesParamVO.getEndTime() == null || userActivitiesParamVO.getEndTime() == 0);
            boolean applyTimeEmpty = isApplyTimeEmpty(userActivitiesParamVO);
            if (activityTimeEmpty && applyTimeEmpty) {
                fileExportManager.msgTip(response, "提报时间与活动开始时间不能同时为空");
                return;
            }

            // 检查活动时间范围
            if (isInvalidTimeRange(userActivitiesParamVO.getStartTime(), userActivitiesParamVO.getEndTime())) {
                fileExportManager.msgTip(response, "开始时间不能大于结束时间");
                return;
            }

            // 检查提报时间范围
            if (isInvalidApplyTimeRange(userActivitiesParamVO)) {
                fileExportManager.msgTip(response, "提报开始时间不能大于结束时间");
                return;
            }

            FileExportVO<UserActivitySimpleInfoExportVO> fileExportVO = new FileExportVO<>();
            fileExportVO.setHead(UserActivitySimpleInfoExportVO.class);
            fileExportVO.setFileName("自助提报审批");
            fileExportVO.setPageNo(1);
            fileExportVO.setPageSize(500);

            fileExportManager.exportToHttpResponse(fileExportVO, response, (pageNo, pageSize)-> {
                RequestQueryUserActivitiesBean request = ActivityApplyConvert.I.userActivitiesExportParamVO2Bean(userActivitiesParamVO
                        , SessionExtUtils.getAppId());
                request.setPageParam(PageParamBean.builder()
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .build());

                Result<ResponseQueryUserActivitiesBean> result = activityApplyServiceRemote.queryUserActivities(request);
                if (ResultUtils.isFailure(result)) {
                    log.warn("queryUserActivities failed. rCode:{}", result.rCode());
                    return PageVO.of(0, Collections.emptyList());
                }

                List<UserActivitySimpleInfoExportVO> userActivitySimpleInfos = ActivityApplyConvert.I.userActivitySimpleInfoBeanList2ExcelVOs(result.target().getList());
                return PageVO.of(result.target().getTotal(), userActivitySimpleInfos);
            });
        } catch (IOException e) {
            log.error("exportDeliverV1 param={},error:", userActivitiesParamVO, e);
        }
    }

    private boolean isApplyTimeEmpty(QueryUserActivitiesParam paramVO) {
        return (paramVO.getApplyStartTime() == null || paramVO.getApplyStartTime() == 0)
            && (paramVO.getApplyEndTime() == null || paramVO.getApplyEndTime() == 0);
    }

    private boolean isInvalidTimeRange(Long startTime, Long endTime) {
        return startTime != null && endTime != null && startTime >= endTime;
    }

    private boolean isInvalidApplyTimeRange(QueryUserActivitiesParam paramVO) {
        return paramVO.getApplyStartTime() != null && paramVO.getApplyStartTime() > 0
            && paramVO.getApplyEndTime() != null && paramVO.getApplyEndTime() > 0
            && paramVO.getApplyStartTime() >= paramVO.getApplyEndTime();
    }

    @GetMapping("/apply/detail")
    public ResultVO<UserActivityDetailResult> queryUserActivityDetail(@RequestParam Long activityId) {
        return activityApplyManager.queryUserActivityDetail(activityId, SessionExtUtils.getAppId());
    }

    @PostMapping("/apply/agree")
    public ResultVO<String> agreeApply(@RequestBody ActivityAuditAgreeParam param) {
        return activityApplyManager.agreeActivityApply(param, SessionExtUtils.getAppId(), SessionUtils.getAccount());
    }

    /**
     * 自主提报审批-用户家族信息
     *
     * @param param 查询参数
     * @return 结果
     */
    @GetMapping("/apply/getUserInFamily")
    public ResultVO<GetUserInFamilyResult> getUserInFamily(@Validated GetUserInFamilyParam param) {
        Integer appId = SessionExtUtils.getAppId();
        return activityApplyManager.getUserInFamily(param, appId);
    }

    /**
     * 自主提报审批-厅签约成员列表
     *
     * @param param 查询参数
     * @return 结果
     */
    @GetMapping("/apply/listRoomPlayer")
    public ResultVO<List<ListRoomPlayerResult>> listRoomPlayer(@Validated ListRoomPlayerParam param) {
        Integer appId = SessionExtUtils.getAppId();
        return activityApplyManager.listRoomPlayer(param, appId);
    }

    /**
     * 自主提报审批-运营添加活动
     *
     * @param param 参数
     * @return 结果
     */
    @PostMapping("/apply/create")
    public ResultVO<Void> create(@RequestBody CreateActivityApplyParam param) {
        Integer appId = SessionExtUtils.getAppId();
        return activityApplyManager.createActivityApply(param, appId);
    }

    /**
     * 自主提报审批-活动日历列表
     *
     * @param param 查询参数
     * @return 结果
     */
    @GetMapping("/apply/pageActivityCalendar")
    public ResultVO<PageVO<PageActivityCalendarResult>> pageActivityCalendar(@Validated PageActivityCalendarParam param) {
        Integer appId = SessionExtUtils.getAppId();
        return activityApplyManager.pageActivityCalendar(param, appId);
    }

    /**
     * 自主提报审批-活动官频位列表
     *
     * @param param 查询参数
     * @return 结果
     */
    @GetMapping("/apply/listOfficialSeat")
    public ResultVO<ListOfficialSeatResult> listOfficialSeat(@Validated ListOfficialSeatParam param) {
        Integer appId = SessionExtUtils.getAppId();
        return activityApplyManager.listOfficialSeat(param, appId);
    }

    /**
     * 运营取消活动
     *
     * @param param 参数
     * @return 结果
     */
    @PostMapping("/apply/cancel")
    public ResultVO<CancelActivityResult> cancelActivity(@RequestBody @Validated CancelActivityParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        return activityApplyManager.cancelActivityApply(param, appId, operator);
    }

    /**
     * 运营修改活动（不管是否审批）
     *
     * @param param 参数
     * @return 结果
     */
    @PostMapping("/apply/modify")
    public ResultVO<ModifyActivityApplyResult> modifyActivity(@RequestBody @Validated ModifyActivityApplyParam param) {
        return ResultVO.failure("功能升级中，请联系管理员！");
    }

    /**
     * 运营修改活动（不管是否审批）
     *
     * @param param 参数
     * @return 结果
     */
    @PostMapping("/apply/admin/modify")
    public ResultVO<ModifyActivityApplyResult> modifyActivityV2(@RequestBody @Validated ModifyActivityApplyParam param) {
        Integer appId = SessionExtUtils.getAppId();
        return activityApplyManager.modifyActivityApply(param, appId);
    }

    /**
     * 批量通过审批
     *
     * @param param 批量通过参数
     * @return 结果
     */
    @PostMapping("/apply/batchAgree")
    public ResultVO<BatchActivityAuditResult> batchAgreeApply(@RequestBody @Validated BatchActivityAuditAgreeParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        return activityApplyManager.batchAgreeActivityApply(param, appId, operator);
    }

    /**
     * 批量拒绝审批
     *
     * @param param 批量拒绝参数
     * @return 结果
     */
    @PostMapping("/apply/batchReject")
    public ResultVO<BatchActivityAuditResult> batchRejectApply(@RequestBody @Validated BatchActivityAuditRejectParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        return activityApplyManager.batchRejectActivityApply(param, appId, operator);
    }
}
