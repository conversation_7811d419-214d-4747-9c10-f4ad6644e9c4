package fm.lizhi.ocean.wave.management.remote.service.activitycenter;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.DecorateBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestBatchGetDecorate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestGetDecorate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityDecorateService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityDecorateServiceRemote {

    @Autowired
    private ActivityDecorateService activityDecorateService;


    public Result<PageBean<DecorateBean>> list(RequestGetDecorate request){
        Result<PageBean<DecorateBean>> result = activityDecorateService.getDecorateList(request);
        if (ResultUtils.isFailure(result)){
            log.warn("get activity decorate list fail, type:{}, appId:{}, name:{}, rCode:{}",
                    request.getType(), request.getAppId(), request.getName(), result.rCode());
        }

        return result;
    }


    public Result<List<DecorateBean>> batchGetDressUp(RequestBatchGetDecorate request){
        Result<List<DecorateBean>> result = activityDecorateService.batchGetDecorateList(request);
        if (ResultUtils.isFailure(result)){
            log.warn("batch get activity decorate list fail. ids:{}, rCode:{}", request.getIds(), result.rCode());
        }

        return result;

    }

}
