package fm.lizhi.ocean.wave.management.model.converter.family.offlinezone;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.management.config.apollo.CommonConfig;
import fm.lizhi.ocean.wave.management.model.converter.CommonConvert;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.AddLevelRightParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.SaveLevelRightParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.UpdateLevelRightParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.AddLevelRightResult;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListLevelRightResult;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wave.management.util.UrlUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRight;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRightIntroduction;
import fm.lizhi.sso.client.SessionUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListValuedMap;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 线下专区等级权益转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
                ConfigUtils.class,
                Date.class,
                SessionUtils.class,
                SessionExtUtils.class,
                UrlUtils.class,
        },
        uses = {
                CommonConvert.class,
        }
)
public abstract class LevelRightConvert {

    @Autowired
    protected CommonConfig commonConfig;

    /**
     * 转换为用于新增的等级权益实体
     *
     * @param param 新增参数
     * @return 等级权益实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "appId", expression = "java(SessionExtUtils.getAppId())")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "image", expression = "java(UrlUtils.removeHostOrEmpty(param.getImage()))")
    @Mapping(target = "deleted", constant = "false")
    @Mapping(target = "operator", expression = "java(SessionUtils.getAccount())")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    public abstract OfflineZoneLevelRight toCreateRightEntity(AddLevelRightParam param);

    /**
     * 转换为用于新增的等级权益介绍实体列表
     *
     * @param param   保存等级权益参数
     * @param rightId 等级权益ID
     * @return 等级权益介绍实体列表
     */
    public List<OfflineZoneLevelRightIntroduction> toCreateIntroductionEntities(SaveLevelRightParam param, Long rightId) {
        if (CollectionUtils.isEmpty(param.getIntroductions())) {
            return Collections.emptyList();
        }
        ArrayList<OfflineZoneLevelRightIntroduction> entities = new ArrayList<>(param.getIntroductions().size());
        for (int index = 0; index < param.getIntroductions().size(); index++) {
            AddLevelRightParam.Introduction introduction = param.getIntroductions().get(index);
            OfflineZoneLevelRightIntroduction entity = toCreateIntroductionEntity(introduction, rightId, index);
            entities.add(entity);
        }
        return entities;
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    protected abstract OfflineZoneLevelRightIntroduction toCreateIntroductionEntity(AddLevelRightParam.Introduction introduction, Long rightId, Integer index);

    /**
     * 转换为新增的等级权益结果
     *
     * @param id 新增的等级权益ID
     * @return 新增结果的VO
     */
    @Mapping(target = "id", source = "id")
    public abstract AddLevelRightResult toAddLevelRightResult(Long id);

    /**
     * 转换为用于更新的等级权益实体
     *
     * @param param 更新参数
     * @return 等级权益实体
     */
    @Mapping(target = "appId", ignore = true)
    @Mapping(target = "deployEnv", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "operator", expression = "java(SessionUtils.getAccount())")
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    public abstract OfflineZoneLevelRight toUpdateRightEntity(UpdateLevelRightParam param);

    /**
     * 转换成等级权益列表结果
     *
     * @param entities                   等级权益实体列表
     * @param levelRightIntroductionsMap 等级权益ID到关联的等级权益介绍实体列表的映射, key为等级权益ID, value为等级权益介绍实体列表
     * @return 等级权益列表结果
     */
    public abstract List<ListLevelRightResult> toListLevelRightResults(List<OfflineZoneLevelRight> entities, @Context ListValuedMap<Long, OfflineZoneLevelRightIntroduction> levelRightIntroductionsMap);

    @Mapping(target = "image", source = "entity.image", qualifiedByName = "addWaveCdnHost")
    @Mapping(target = "introductions", source = "entity.id", qualifiedByName = "toListLevelRightResultIntroductions")
    protected abstract ListLevelRightResult toListLevelRightResult(OfflineZoneLevelRight entity, @Context ListValuedMap<Long, OfflineZoneLevelRightIntroduction> levelRightIntroductionsMap);

    @Named("addWaveCdnHost")
    protected String addWaveCdnHost(String path) {
        return UrlUtils.addHostOrEmpty(path, commonConfig.getWaveCdn().getCdnHost());
    }

    @Named("toListLevelRightResultIntroductions")
    protected List<ListLevelRightResult.Introduction> toListLevelRightResultIntroductions(Long rightId, @Context ListValuedMap<Long, OfflineZoneLevelRightIntroduction> levelRightIntroductionsMap) {
        if (rightId == null || levelRightIntroductionsMap == null) {
            return Collections.emptyList();
        }
        List<OfflineZoneLevelRightIntroduction> introductionEntities = levelRightIntroductionsMap.get(rightId);
        return toListLevelRightResultIntroductions(introductionEntities);
    }

    protected abstract List<ListLevelRightResult.Introduction> toListLevelRightResultIntroductions(List<OfflineZoneLevelRightIntroduction> introductionEntities);
}
