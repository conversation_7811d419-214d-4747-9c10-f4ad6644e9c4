package fm.lizhi.ocean.wave.management.manager.family.offlinezone;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wave.management.datastore.dao.family.offlinezone.FamilyRatingDao;
import fm.lizhi.ocean.wave.management.datastore.dao.family.offlinezone.LevelConfigDao;
import fm.lizhi.ocean.wave.management.model.converter.family.offlinezone.FamilyRatingConvert;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.BatchUpdateFamilyRatingParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.GetFamilyRatingParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.ListFamilyRatingParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.GetFamilyRatingResult;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListFamilyRatingResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataFamilyWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneFamilyRating;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 公会评级Manager
 * <AUTHOR>
 */
@Component
@Slf4j
public class FamilyRatingManager {

    @Autowired
    private FamilyRatingDao familyRatingDao;
    @Autowired
    private LevelConfigDao levelConfigDao;

    /**
     * 查询公会评级列表
     *
     * @param param 查询参数
     * @return 查询结果
     */
    public ResultVO<PageVO<ListFamilyRatingResult>> listFamilyRating(ListFamilyRatingParam param) {
        try {

            if (param.getLevelId() != null) {
                // 如果传了评级ID，先查评级表
                PageList<OfflineZoneFamilyRating> pageList = familyRatingDao.pageRatingByParam(param);

                // 根据评级分页信息，查询公会周数据
                List<Long> familyIds = pageList.stream().map(OfflineZoneFamilyRating::getFamilyId).collect(Collectors.toList());
                List<OfflineZoneDataFamilyWeek> familyWeekList = familyRatingDao.listFamilyWeekByFamilyIds(param, familyIds);

                Set<Long> levelIds = pageList.stream().map(OfflineZoneFamilyRating::getLevelId).collect(Collectors.toSet());
                List<OfflineZoneLevelConfig> levelConfigList = levelConfigDao.getLevelConfigByIds(levelIds);

                // 转换结果
                List<ListFamilyRatingResult> resultList = FamilyRatingConvert.I.toListFamilyRatingResultPageByRating(pageList, familyWeekList, levelConfigList);
                return ResultVO.success(PageVO.of(pageList.getTotal(), resultList));

            }else {
                // 否则先查公会周数据
                PageList<OfflineZoneDataFamilyWeek> familyWeekList = familyRatingDao.pageFamilyWeekByParam(param);
                // 再根据公会ID列表查询评级数据
                List<OfflineZoneFamilyRating> ratingList = familyRatingDao.listRatingByFamilyIds(param,
                    familyWeekList.stream().map(OfflineZoneDataFamilyWeek::getFamilyId).collect(Collectors.toList())
                );

                Set<Long> levelIds = ratingList.stream().map(OfflineZoneFamilyRating::getLevelId).collect(Collectors.toSet());
                List<OfflineZoneLevelConfig> levelConfigList = levelConfigDao.getLevelConfigByIds(levelIds);
                // 转换结果
                List<ListFamilyRatingResult> resultList = FamilyRatingConvert.I.toListFamilyRatingResultPageByDataWeek(familyWeekList, ratingList, levelConfigList);
                return ResultVO.success(PageVO.of(familyWeekList.getTotal(), resultList));
            }
        } catch (Exception e) {
            log.error("查询线下厅公会评级列表失败", e);
            return ResultVO.failure("查询失败");
        }
    }

    /**
     * 批量更新公会评级
     *
     * @param param 批量更新参数
     * @param operator 操作人
     * @param appId 应用ID
     * @return 更新结果
     */
    public ResultVO<Void> batchUpdateFamilyRating(BatchUpdateFamilyRatingParam param, String operator, Integer appId) {
        try {
            if (param.getFamilyIds() == null || param.getFamilyIds().isEmpty()) {
                return ResultVO.failure("公会列表不能为空");
            }

            OfflineZoneLevelConfig level = levelConfigDao.getLevelConfigById(param.getLevelId());
            if (level == null) {
                return ResultVO.failure("等级不存在");
            }

            familyRatingDao.batchUpdateFamilyRating(
                param.getFamilyIds(),
                param.getLevelId(),
                operator,
                appId
            );
            return ResultVO.success();
        } catch (Exception e) {
            log.error("批量更新公会评级失败", e);
            return ResultVO.failure("批量更新失败");
        }
    }

    /**
     * 获取公会评级信息
     *
     * @param param 获取参数
     * @return 公会评级结果
     */
    public ResultVO<GetFamilyRatingResult> getFamilyRating(GetFamilyRatingParam param) {
        Long familyId = param.getFamilyId();
        OfflineZoneFamilyRating familyRating = familyRatingDao.getFamilyRatingByFamilyId(familyId);
        if (familyRating == null) {
            return ResultVO.success(GetFamilyRatingResult.empty());
        }
        Long levelId = familyRating.getLevelId();
        OfflineZoneLevelConfig levelConfig = levelConfigDao.getLevelConfigById(levelId);
        String levelName = levelConfig != null ? levelConfig.getLevelName() : null;
        GetFamilyRatingResult result = FamilyRatingConvert.I.toGetFamilyRatingResult(familyRating, levelName);
        return ResultVO.success(result);
    }
}