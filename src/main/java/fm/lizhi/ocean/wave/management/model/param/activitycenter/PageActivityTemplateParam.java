package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import lombok.Data;

import java.util.List;

/**
 * 分页查询活动模板参数
 */
@Data
public class PageActivityTemplateParam {

    /**
     * 分页页码
     */
    private Integer pageNo;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 模板id
     */
    private Long id;

    /**
     * 模板名称, 模糊查询
     */
    private String name;

    /**
     * 模板状态
     */
    private Integer status;

    /**
     * 是否热门推荐
     */
    private Boolean hotRec;

    /**
     * 活动分类id
     */
    private List<Long> classId;
}
