package fm.lizhi.ocean.wave.management.model.result.award.singer;

import fm.lizhi.ocean.wave.management.model.vo.award.singer.SingerDecorateFlowVO;
import fm.lizhi.ocean.wave.management.model.vo.resource.DecorateInfoVO;
import fm.lizhi.ocean.wave.management.model.vo.user.UserVO;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 歌手装扮流水响应
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SingerDecorateFlowResult {


    /**
     * 流水明细
     */
    private SingerDecorateFlowVO flow;

    /**
     * 装扮信息
     */
    private DecorateInfoVO decorateInfo;

    /**
     * 用户信息
     */
    private UserVO user;
}
