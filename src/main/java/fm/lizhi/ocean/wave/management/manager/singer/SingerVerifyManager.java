package fm.lizhi.ocean.wave.management.manager.singer;

import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerVerifyApplyDao;
import fm.lizhi.ocean.wave.management.manager.singer.handler.SingerAuditStatusHandler;
import fm.lizhi.ocean.wave.management.manager.singer.handler.SingerAuditStatusHandlerFactory;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerVerifyConvert;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerAuditParamDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerExecuteAuditDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerHallStatusResult;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.*;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.*;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerVerifyAuditLosingVO;
import fm.lizhi.ocean.wave.management.processor.singer.ISingerHallApplyProcessor;
import fm.lizhi.ocean.wave.management.remote.service.anchor.singer.SingerVerifyRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseBatchCancelBlackList;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseQueryHistoryVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseVerifyAudit;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingerVerifyManager {

    @Resource
    private SingerVerifyRemote singerVerifyRemote;

    @Autowired
    private SingerVerifyApplyDao singerVerifyApplyDao;

    @Autowired
    private SingerAuditStatusHandlerFactory handlerFactory;

    @Autowired
    private ProcessorFactory processorFactory;


    public PageVO<SingerVerifyRecordForQueryResult> getApplyList(@Valid GetApplyListParam param, Integer appId) {
        Result<PageBean<ResponseGetSingerVerifyRecord>> result = singerVerifyRemote.getSingerVerifyRecord(param, appId);
        if (ResultUtils.isFailure(result)) {
            log.error("获取歌手认证记录列表失败, param: {}, appId: {}, result: {}", param, appId, result);
            throw new RuntimeException("获取歌手认证记录列表失败");
        }
        return SingerVerifyConvert.INSTANCE.pageBean2QueryResult(result.target());
    }

    public PageVO<SingerVerifyRecordResult> getApplyListForExport(@Valid GetApplyListExcelParam param, Integer appId) {
        Result<PageBean<ResponseGetSingerVerifyRecord>> result = singerVerifyRemote.getSingerVerifyRecord(param, appId);
        if (ResultUtils.isFailure(result)) {
            log.error("获取歌手认证记录列表失败, param: {}, appId: {}, result: {}", param, appId, result);
            throw new RuntimeException("获取歌手认证记录列表失败");
        }

        // 处理歌曲信息拼接
        PageBean<ResponseGetSingerVerifyRecord> pageBean = result.target();
        if (pageBean.getList() != null) {
            pageBean.getList().forEach(record -> {
                if (record.getSingerVerifySongInfoBeans() != null && !record.getSingerVerifySongInfoBeans().isEmpty()) {
                    // 拼接audioPath
                    String audioPathStr = record.getSingerVerifySongInfoBeans().stream().map(bean -> bean.getAudioPath() != null ? bean.getAudioPath() : "").collect(Collectors.joining(","));
                    record.setAudioPath(audioPathStr);

                    // 拼接songName
                    String songNameStr = record.getSingerVerifySongInfoBeans().stream().map(bean -> bean.getSongName() != null ? bean.getSongName() : "").collect(Collectors.joining(","));
                    record.setSongName(songNameStr);

                    // 拼接songStyle
                    String songStyleStr = record.getSingerVerifySongInfoBeans().stream().map(bean -> bean.getSongStyle() != null ? bean.getSongStyle() : "").collect(Collectors.joining(","));
                    record.setSongStyle(songStyleStr);
                }
            });
        }

        return SingerVerifyConvert.INSTANCE.pageBean2PageVO(pageBean);
    }

    public boolean updateRemark(UpdateRemarkParam param, Integer appId) {
        Result<Void> result = singerVerifyRemote.updateRemark(param, appId);
        if (ResultUtils.isFailure(result)) {
            log.error("更新歌手认证备注失败, param: {}, appId: {}, result: {}", param, appId, result);
            return false;
        }
        return true;
    }

    /**
     * 歌手认证审核操作
     *
     * @param param 歌手认证审核操作参数
     * @return 歌手认证审核操作结果
     */
    public ResultVO<SingerVerifyAuditResult> auditVerify(SingerVerifyAuditParam param) {
        Result<ResponseVerifyAudit> result = singerVerifyRemote.auditVerify(param);
        if (ResultUtils.isFailure(result)) {
            log.error("歌手认证审核操作失败, param: {}, result: {}", param, result);
            return ResultVO.failure(result.rCode(), result.getMessage());
        }
        SingerVerifyAuditResult auditResult = new SingerVerifyAuditResult();
        ResponseVerifyAudit target = result.target();
        return ResultVO.success(auditResult.setLosingList(SingerVerifyConvert.INSTANCE.convertAuditRes(target.getLosingList())));
    }

    /**
     * 查询歌手历史记录
     *
     * @param param 查询歌手历史记录参数
     * @return 查询歌手历史记录结果
     */
    public ResultVO<SingerVerifyHistoryRecordResult> queryHistoryVerifyRecord(QueryHistoryVerifyRecordParam param) {
        Result<List<ResponseQueryHistoryVerifyRecord>> result = singerVerifyRemote.queryHistoryVerifyRecord(param);
        if (ResultUtils.isFailure(result)) {
            log.error("查询歌手历史记录失败, param: {}, result: {}", param, result);
            return ResultVO.failure(result.rCode(), result.getMessage());
        }

        SingerVerifyHistoryRecordResult resultVO = new SingerVerifyHistoryRecordResult();
        resultVO.setList(SingerVerifyConvert.INSTANCE.response2List(result.target()));
        return ResultVO.success(resultVO);
    }

    /**
     * 批量拉黑歌手
     *
     * @param appId   应用ID
     * @param userIds 用户ID
     * @return 结果
     */
    public ResultVO<Void> batchBlackList(Integer appId, List<Long> userIds) {
        Result<Void> result = singerVerifyRemote.batchBlackList(appId, userIds);
        if (ResultUtils.isFailure(result)) {
            log.error("批量拉黑歌手失败, appId: {}, userIds: {}, result: {}", appId, userIds, result);
            return ResultVO.failure(result.rCode(), "拉黑用户失败");
        }
        return ResultVO.success();
    }

    /**
     * 批量取消拉黑歌手
     *
     * @param appId   应用ID
     * @param userIds 用户ID
     * @return 结果
     */
    public ResultVO<SingerCancelBlackListResult> batchCancelBlackList(Integer appId, List<Long> userIds) {
        Result<ResponseBatchCancelBlackList> result = singerVerifyRemote.batchCancelBlackList(appId, userIds);
        if (ResultUtils.isFailure(result)) {
            log.error("批量取消拉黑歌手失败, appId: {}, userIds: {}, result: {}", appId, userIds, result);
            return ResultVO.failure(result.rCode(), "取消拉黑用户失败");
        }
        SingerCancelBlackListResult resultVO = new SingerCancelBlackListResult();
        resultVO.setLosingIdList(result.target().getLosingUserIds());
        return ResultVO.success(resultVO);
    }

    /**
     * 创建一个歌手认证审核V2接口
     *
     * @param request 参数
     * @return 结果
     */
    public ResultVO<SingerVerifyAuditResult> auditVerifyV2(SingerVerifyAuditV2Param request) {
        // 参数校验
        if (!SingerTypeEnum.isValid(request.getSingerType())) {
            return ResultVO.failure(CommonService.PARAM_ERROR, "歌手类型不合法");
        }

        if (!SingerAuditStatusEnum.isValid(request.getAuditStatus())) {
            return ResultVO.failure(CommonService.PARAM_ERROR, "认证状态不合法");
        }

        // 获取歌手认证配置
        List<SingerVerifyRecord> verifyRecordList = singerVerifyApplyDao.getSingerVerifyRecordByIds(request.getIds());
        if (verifyRecordList.isEmpty()) {
            return ResultVO.failure(CommonService.PARAM_ERROR, "歌手认证记录不存在");
        }
        //获取认证ID列表
        List<Long> verifyIds = verifyRecordList.stream().map(SingerVerifyRecord::getId).collect(Collectors.toList());
        Map<Long, String> songStyleMap = singerVerifyApplyDao.getApplySongStyleMap(verifyIds);

        // 批量查询点唱厅审核状态
        List<Long> njIds = verifyRecordList.stream().map(SingerVerifyRecord::getNjId).collect(Collectors.toList());
        ISingerHallApplyProcessor processor = processorFactory.getProcessor(ISingerHallApplyProcessor.class);
        SingerHallStatusResult result = processor.batchGetSingerHallStatusMap(request.getAppId(), njIds);
        // 失败的歌手认证记录ID
        List<SingerVerifyAuditLosingVO> failedIds = Lists.newArrayList();
        // 获取歌手认证记录
        Map<Long, SingerVerifyRecord> verifyRecordMap = verifyRecordList.stream().collect(Collectors.toMap(SingerVerifyRecord::getId, v -> v));
        SingerAuditParamDTO param = SingerVerifyConvert.INSTANCE.pram2SingerAuditDTO(request, result.isNeedCheck());
        //状态处理器
        SingerAuditStatusHandler handler = handlerFactory.getHandler(request.getAuditStatus());
        if (handler == null) {
            return ResultVO.failure(CommonService.PARAM_ERROR, "认证状态不合法");
        }
        for (Long id : request.getIds()) {
            SingerVerifyRecord verifyRecord = verifyRecordMap.get(id);
            if (verifyRecord == null) {
                log.warn("Cannot find singer verify record by id {}", id);
                failedIds.add(SingerVerifyAuditLosingVO.of(id, "歌手认证记录不存在"));
                continue;
            }

            Integer singerHallApplyStatus = result.getStatusMap().get(verifyRecord.getNjId());
            param.setSingerHallStatus(singerHallApplyStatus == null ? null : SingerHallApplyStatusEnum.getByStatus(singerHallApplyStatus));
            param.setPassSongStyle(getPassSongStyle(request, songStyleMap.getOrDefault(verifyRecord.getId(), "")));
            param.setOriginalSinger(request.getOriginalSinger() == null ? verifyRecord.getOriginalSinger() : request.getOriginalSinger());
            log.info("auditVerifyV2,appId:{},ids:{},paramDTO:{}", request.getAppId(), request.getIds(), JsonUtil.dumps(param));
            SingerExecuteAuditDTO executedRes = handler.executeAudit(param, verifyRecord);
            if (!executedRes.isSuccess()) {
                failedIds.add(SingerVerifyAuditLosingVO.of(id, executedRes.getReason()));
            }
        }
        log.info("auditVerifyV2.appId:{},failedInfo:{}", request.getAppId(), JsonUtil.dumps(failedIds));
        // 返回审核结果
        return ResultVO.success(new SingerVerifyAuditResult().setLosingList(failedIds));
    }

    /**
     * 获取通过的曲风
     *
     * @param request              参数
     * @param verifyApplySongStyle 认证申请的曲风
     * @return 结果
     */
    private String getPassSongStyle(SingerVerifyAuditV2Param request, String verifyApplySongStyle) {
        if (StringUtils.isEmpty(request.getPassSongStyle())) {
            //如果批量操作时没有选择曲风，则使用原来认证申请提交的曲风
            return verifyApplySongStyle;
        }
        return request.getPassSongStyle();
    }
} 
