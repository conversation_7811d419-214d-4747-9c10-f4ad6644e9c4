package fm.lizhi.ocean.wave.management.processor.singer.hy;

import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.management.config.apollo.singer.SingerConfig;
import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerHallApplyDao;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerHallStatusResult;
import fm.lizhi.ocean.wave.management.processor.singer.ISingerHallApplyProcessor;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerSingHallApplyRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class HySingerHallApplyProcessor implements ISingerHallApplyProcessor {

    @Autowired
    private SingerHallApplyDao singerHallApplyDao;

    @Autowired
    private SingerConfig singerConfig;

    @Override
    public SingerHallStatusResult batchGetSingerHallStatusMap(int appId, List<Long> ids) {
        if (singerConfig.getHy().isLessRelevanceSwitch()) {
            //关闭了弱关联，就不判断点唱厅状态了
            log.info("hy singer hall apply processor, less relevance switch is off, return empty result");
            return SingerHallStatusResult.of(false, new HashMap<>(2));
        }

        List<Integer> auditStatusList = Lists.newArrayList(SingerHallApplyStatusEnum.APPLYED.getStatus(), SingerHallApplyStatusEnum.APPLYING.getStatus());
        List<SingerSingHallApplyRecord> recordList = singerHallApplyDao.batchGetSingerHallApplyRecordList(appId, ids, auditStatusList);
        if (CollectionUtils.isEmpty(recordList)) {
            return SingerHallStatusResult.of(true, new HashMap<>(2));
        }
        Map<Long, Integer> map = recordList.stream().collect(Collectors.toMap(SingerSingHallApplyRecord::getNjId, SingerSingHallApplyRecord::getAuditStatus));
        log.info("hy.batchGetSingerHallStatusMap, map:{}", JsonUtil.dumps(map));
        return SingerHallStatusResult.of(true, map);
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }
}
