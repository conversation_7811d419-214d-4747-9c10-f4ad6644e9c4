package fm.lizhi.ocean.wave.management.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ActivityRuleConfigConvert;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityRuleParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityRuleParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityRuleConfigResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.remote.service.activitycenter.ActivityRuleConfigServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRule;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityRuleConfigManager {

    @Autowired
    private ActivityRuleConfigServiceRemote activityRuleConfigServiceRemote;

    public ResultVO<Void> save(SaveActivityRuleParam param, Integer appId, String operator){
        RequestSaveActivityRule request = ActivityRuleConfigConvert.I.toRequestSaveActivityRule(param, appId, operator);
        Result<Void> result = activityRuleConfigServiceRemote.save(request);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success();
    }

    public ResultVO<Void> update(UpdateActivityRuleParam param, Integer appId, String operator){
        RequestUpdateActivityRule request = ActivityRuleConfigConvert.I.toRequestUpdateActivityRule(param, appId, operator);
        Result<Void> result = activityRuleConfigServiceRemote.update(request);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success();
    }

    public ResultVO<Void> delete(Long id, Integer appId, String operator){
        Result<Void> result = activityRuleConfigServiceRemote.delete(id, appId, operator);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success();
    }

    public ResultVO<List<ActivityRuleConfigResult>> list(Integer appId){
        Result<List<ActivityRuleConfigBean>> result = activityRuleConfigServiceRemote.list(appId);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }

        return ResultVO.success(ActivityRuleConfigConvert.I.ActivityRuleConfigResults(result.target()));
    }

}
