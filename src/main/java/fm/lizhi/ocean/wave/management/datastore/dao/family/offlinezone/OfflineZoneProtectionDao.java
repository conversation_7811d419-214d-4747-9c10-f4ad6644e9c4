package fm.lizhi.ocean.wave.management.datastore.dao.family.offlinezone;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.ProtectionAgreeStatusEnums;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.GetProtectionHistoryParam;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtection;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtectionExample;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtectionHistory;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtectionHistoryExample;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneProtectionHistoryMapper;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneProtectionMapper;
import lombok.extern.slf4j.Slf4j;

/**
 * 跳槽保护协议历史记录数据访问对象
 * <AUTHOR>
 */
@Repository
@Slf4j
public class OfflineZoneProtectionDao {

    @Autowired
    private OfflineZoneProtectionHistoryMapper offlineZoneProtectionHistoryMapper;

    @Autowired
    private OfflineZoneProtectionMapper offlineZoneProtectionMapper;

    /**
     * 查询跳槽保护协议历史记录（不分页）
     * 先从offline_zone_protection表查询player_agree=1且archived=1的记录，再关联查询历史表
     *
     * @param param 查询参数
     * @return 历史记录列表
     */
    public List<OfflineZoneProtectionHistory> listProtectionHistoryByParam(GetProtectionHistoryParam param) {
        // 1. 先查询offline_zone_protection表中player_agree=1且archived=1的记录
        OfflineZoneProtectionExample protectionExample = new OfflineZoneProtectionExample();
        OfflineZoneProtectionExample.Criteria protectionCriteria = protectionExample.createCriteria();
        
        // 必须条件：player_agree=1 && archived=1
        protectionCriteria.andPlayerAgreeEqualTo(ProtectionAgreeStatusEnums.AGREED.getCode());
        protectionCriteria.andArchivedEqualTo(true);
        protectionCriteria.andAppIdEqualTo(param.getAppId());
        protectionCriteria.andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        // 主播ID条件
        if (param.getPlayerId() != null) {
            protectionCriteria.andPlayerIdEqualTo(param.getPlayerId());
        }
        
        // 公会ID条件
        if (param.getFamilyId() != null) {
            protectionCriteria.andFamilyIdEqualTo(param.getFamilyId());
        }
        
        // 厅主ID条件
        if (param.getNjId() != null) {
            protectionCriteria.andNjIdEqualTo(param.getNjId());
        }

        List<OfflineZoneProtection> protectionList = offlineZoneProtectionMapper.selectByExample(protectionExample);
        
        if (CollectionUtils.isEmpty(protectionList)) {
            return Collections.emptyList();
        }
        
        // 2. 根据查询到的保护协议ID列表，查询对应的历史记录
        List<Long> protectedIds = protectionList.stream()
                .map(OfflineZoneProtection::getId)
                .collect(Collectors.toList());
        
        OfflineZoneProtectionHistoryExample historyExample = new OfflineZoneProtectionHistoryExample();
        OfflineZoneProtectionHistoryExample.Criteria historyCriteria = historyExample.createCriteria();
        historyCriteria.andProtectedIdIn(protectedIds);
        
        // 设置排序：按协议开始时间倒序
        historyExample.setOrderByClause("create_time DESC");
        
        return offlineZoneProtectionHistoryMapper.selectByExample(historyExample);
    }
}