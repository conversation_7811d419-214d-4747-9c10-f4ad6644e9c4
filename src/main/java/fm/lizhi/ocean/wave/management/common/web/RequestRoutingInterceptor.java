package fm.lizhi.ocean.wave.management.common.web;

import fm.lizhi.biz.data.collector.BizDataCtx;
import fm.lizhi.biz.data.collector.DataCtxContainer;
import fm.lizhi.biz.data.collector.HttpCtx;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.lang.NonNull;

import java.io.IOException;

/**
 * 自动透传X-Real-Ip的拦截器
 */
public class RequestRoutingInterceptor implements ClientHttpRequestInterceptor {

    @NonNull
    @Override
    public ClientHttpResponse intercept(@NonNull HttpRequest request, @NonNull byte[] body, @NonNull ClientHttpRequestExecution execution) throws IOException {
        String ip = getIpFromContext();
        if (StringUtils.isNotBlank(ip)) {
            HttpHeaders headers = request.getHeaders();
            headers.add("X-Real-Ip", ip);
        }
        return execution.execute(request, body);
    }

    private String getIpFromContext() {
        BizDataCtx bizDataCtx = DataCtxContainer.get();
        if (bizDataCtx == null) {
            return null;
        }
        HttpCtx httpCtx = bizDataCtx.getHttpCtx();
        if (httpCtx == null) {
            return null;
        }
        return httpCtx.getIp();
    }
}
