package fm.lizhi.ocean.wave.management.config;

import fm.lizhi.biz.data.collector.DataCollectFilter;
import fm.lizhi.ocean.wave.management.config.apollo.SsoClientConfig;
import fm.lizhi.sso.client.SessionUtils;
import fm.lizhi.sso.client.SmartContainer;
import fm.lizhi.sso.client.SmartContainerBuilder;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

/**
 * WebMvc配置
 */
@Configuration
public class WebMvcConfiguration {

    /**
     * 跨域过滤器
     *
     * @return 跨域过滤器
     */
    @Bean
    public FilterRegistrationBean<CorsFilter> corsFilter() {
        FilterRegistrationBean<CorsFilter> registrationBean = new FilterRegistrationBean<>();
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        corsConfiguration.addAllowedOrigin("*");
        corsConfiguration.addAllowedHeader("*");
        corsConfiguration.addAllowedMethod("*");
        corsConfiguration.setAllowCredentials(true);
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfiguration);
        registrationBean.setFilter(new CorsFilter(source));
        registrationBean.setOrder(0);
        return registrationBean;
    }

    /**
     * sso客户端过滤器
     *
     * @param ssoClientConfig sso客户端配置
     * @return sso客户端过滤器
     * @throws Exception 构造异常
     */
    @Bean
    public FilterRegistrationBean<SmartContainer> ssoClientFilter(SsoClientConfig ssoClientConfig) throws Exception {
        SmartContainer smartContainer = new SmartContainerBuilder()
                .setServiceName(ssoClientConfig.getServiceName())
                .setExcludeLoginUrlPattern(ssoClientConfig.getExcludeLoginUrlPattern())
                .setExcludeUrlPattern(ssoClientConfig.getExcludeUrlPattern())
                .setLogoutPath(ssoClientConfig.getLogoutPath())
                .setLogoutBackPath(ssoClientConfig.getLogoutBackPath())
                .setEnableOpAudit(ssoClientConfig.isEnableOpAudit())
                .setAlwaysCheckPermission(ssoClientConfig.isAlwaysCheckPermission())
                .setSameSite(ssoClientConfig.getSameSite())
                .setBackUrlScheme(ssoClientConfig.getBackUrlScheme())
                .build();
        FilterRegistrationBean<SmartContainer> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(smartContainer);
        registrationBean.setOrder(10);
        return registrationBean;
    }

    /**
     * 灯塔BizDataCtx过滤器
     *
     * @return 灯塔BizDataCtx过滤器
     */
    @Bean
    public FilterRegistrationBean<DataCollectFilter> dataCollectFilter() {
        FilterRegistrationBean<DataCollectFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new DataCollectFilter(request -> {
            Integer userId = SessionUtils.getCurrentTokenInfo().getUserId();
            return userId != null ? (long) userId : NumberUtils.LONG_ZERO;
        }));
        registrationBean.addUrlPatterns("/*");
        registrationBean.setOrder(20);
        return registrationBean;
    }
}
