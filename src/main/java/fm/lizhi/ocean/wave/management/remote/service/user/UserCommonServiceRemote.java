package fm.lizhi.ocean.wave.management.remote.service.user;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.common.bean.ContextRequest;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.PlayerSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.QueryGuildPlayerBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.constants.SingStatusEnum;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;

/**
 * 用户公共服务远程调用
 */
@Component
@Slf4j
public class UserCommonServiceRemote {

    @Autowired
    private UserCommonService userCommonService;

    public Result<UserBean> getUserById(int appId, long userId){
        return userCommonService.getUserById(appId, userId);
    }

    /**
     * 根据波段号获取用户信息
     *
     * @param appId 应用id
     * @param band  波段号
     * @return 用户信息
     */
    public Result<UserBean> getUserByBand(int appId, String band) {
        log.info("getUserByBand appId: {}, band: {}", appId, band);
        Result<UserBean> result = userCommonService.getUserByBand(appId, band);
        if (ResultUtils.isSuccess(result)) {
            log.info("getUserByBand success, target: {}", result.target());
        } else {
            int rCode = result.rCode();
            if (rCode == UserCommonService.USER_NOT_FOUND) {
                log.info("getUserByBand fail, user not found, appId: {}, band: {}", appId, band);
            } else {
                log.info("getUserByBand fail, rCode: {}, message: {}", rCode, result.getMessage());
            }
        }
        return result;
    }

    /**
     * 查询厅下所有主播，包括已解约的
     *
     * @param appId    应用id
     * @param roomId   厅id
     * @param pageNo   分页页码
     * @param pageSize 分页大小
     * @return 主播列表
     */
    public Result<PageBean<PlayerSignBean>> getAllRoomPlayers(int appId, long roomId, int pageNo, int pageSize) {
        log.info("getAllRoomPlayers appId: {}, roomId: {}, pageNo: {}, pageSize: {}", appId, roomId, pageNo, pageSize);
        Result<PageBean<PlayerSignBean>> result = userCommonService.getAllRoomPlayers(appId, roomId, pageNo, pageSize);
        if (ResultUtils.isSuccess(result)) {
            int total = result.target().getTotal();
            int size = CollectionUtils.size(result.target().getList());
            log.info("getAllRoomPlayers success, total: {}, size: {}", total, size);
        } else {
            log.info("getAllRoomPlayers fail, rCode: {}, message: {}", result.rCode(), result.getMessage());
        }
        return result;
    }

    /**
     * 查询厅下所有签约主播id列表. 这里是多次分页查询拼接而成, 因此需看场景调用.
     *
     * @param njId  厅主id
     * @param appId 应用id
     * @return 厅下所有签约主播id列表
     */
    public Result<List<Long>> getAllSignedRoomPlayerIds(long njId, int appId) {
        log.info("getAllSignedRoomPlayerIds njId: {}, appId: {}", njId, appId);
        int pageNo = 1;
        int pageSize = 500;
        boolean hasNext = true;
        LinkedHashSet<Long> playerIdSet = new LinkedHashSet<>();
        while (hasNext) {
            Result<PageBean<PlayerSignBean>> result = userCommonService.getAllRoomPlayers(appId, njId, pageNo, pageSize);
            if (ResultUtils.isFailure(result)) {
                log.info("getAllRoomPlayers fail, njId: {}, appId: {}, pageNo: {}, pageSize: {}, rCode: {}, message: {}",
                        njId, appId, pageNo, pageSize, result.rCode(), result.getMessage());
                return ResultUtils.failure(result.rCode(), result.getMessage());
            }
            List<PlayerSignBean> playerSignBeans = result.target().getList();
            for (PlayerSignBean playerSignBean : CollectionUtils.emptyIfNull(playerSignBeans)) {
                if (Objects.equals(playerSignBean.getSignStatus(), SingStatusEnum.SING.getValue())) {
                    playerIdSet.add(playerSignBean.getId());
                }
            }
            if (CollectionUtils.size(playerSignBeans) < pageSize) {
                hasNext = false;
            }
            pageNo++;
        }
        List<Long> playerIds = new ArrayList<>(playerIdSet);
        log.info("getAllSignedRoomPlayerIds success, njId: {}, appId: {}, size: {}", njId, appId, playerIds.size());
        return ResultUtils.success(playerIds);
    }

    /**
     * 查询工会下所有签约主播id列表. 这里是多次分页查询拼接而成, 因此需看场景调用.
     *
     * @param familyId 工会id
     * @param appId    应用id
     * @return 工会下所有签约主播id列表
     */
    public Result<List<Long>> getAllSignedGuildPlayerIds(long familyId, int appId) {
        log.info("getAllSignedGuildPlayerIds familyId: {}, appId: {}", familyId, appId);
        int pageNo = 1;
        int pageSize = 500;
        boolean hasNext = true;
        LinkedHashSet<Long> playerIdSet = new LinkedHashSet<>();
        while (hasNext) {
            QueryGuildPlayerBean query = QueryGuildPlayerBean.builder()
                    .familyId(familyId).appId(appId).status(SingStatusEnum.SING.getValue()).build();
            Result<PageBean<PlayerSignBean>> result = userCommonService.getAllGuildPlayer(query, pageNo, pageSize);
            if (ResultUtils.isFailure(result)) {
                log.info("getAllGuildPlayer fail, familyId: {}, appId: {}, pageNo: {}, pageSize: {}, rCode: {}, message: {}",
                        familyId, appId, pageNo, pageSize, result.rCode(), result.getMessage());
                return ResultUtils.failure(result.rCode(), result.getMessage());
            }
            int total = result.target().getTotal();
            List<PlayerSignBean> playerSignBeans = result.target().getList();
            for (PlayerSignBean playerSignBean : CollectionUtils.emptyIfNull(playerSignBeans)) {
                playerIdSet.add(playerSignBean.getId());
            }
            if (playerIdSet.size() >= total || CollectionUtils.size(playerSignBeans) < pageSize) {
                hasNext = false;
            }
            pageNo++;
        }
        List<Long> playerIds = new ArrayList<>(playerIdSet);
        log.info("getAllSignedGuildPlayerIds success, familyId: {}, appId: {}, size: {}", familyId, appId, playerIds.size());
        return ResultUtils.success(playerIds);
    }

    public Result<UserBean> getUserByKeyword(String keyword, int appId) {
        log.info("getUserByKeyword keyword: {}, appId: {}", keyword, appId);
        ContextRequest contextRequest = new ContextRequest().setAppId(appId);
        Result<UserBean> result = userCommonService.getUserByKeyWord(contextRequest, keyword);
        if (ResultUtils.isFailure(result)) {
            log.info("getUserByKeyword fail, keyword: {}, appId: {}, rCode: {}, message: {}", keyword, appId, result.rCode(), result.getMessage());
            return ResultUtils.failure(result.rCode(), result.getMessage());
        }
        return ResultUtils.success(result.target());
    }
}
