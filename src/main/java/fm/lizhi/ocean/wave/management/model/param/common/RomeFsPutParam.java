package fm.lizhi.ocean.wave.management.model.param.common;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.common.romefs.javasdk.config.RomeFsConfig;
import lombok.Data;

import java.nio.file.Path;

/**
 * 罗马文件上传参数
 */
@Data
public class RomeFsPutParam {

    /**
     * 访问权限: public
     */
    public static final String ACCESS_MODIFIER_PUBLIC = "public";
    /**
     * 访问权限: private
     */
    public static final String ACCESS_MODIFIER_PRIVATE = "private";

    /**
     * 罗马文件系统配置
     */
    private RomeFsConfig romeFsConfig;
    /**
     * 访问权限
     */
    private String accessModifier;
    /**
     * 本地文件路径
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Path localPath;
    /**
     * 上传后的文件名, 可包含路径(以斜杆开头), 如果为空将使用本地文件名(不包含路径)
     */
    private String fileName;
    /**
     * 文件类型, 如果是gif或jpeg或png或webp, 将会自动推断, 其他类型需要指定.
     */
    private String contentType;
    /**
     * 重试次数, 小于1时不重试.
     */
    private int retries = 1;
    /**
     * 分片大小, 默认为1MB, 大于该值的文件将被分片上传.
     */
    private int partBytes = 1024 * 1024;
}
