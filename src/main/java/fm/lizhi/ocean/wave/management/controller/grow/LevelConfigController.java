package fm.lizhi.ocean.wave.management.controller.grow;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.grow.FamilyLevelConfigConvert;
import fm.lizhi.ocean.wave.management.model.param.family.SaveLevelConfigParam;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.family.FamilyLevelConfigVO;
import fm.lizhi.ocean.wave.management.remote.service.grow.FamilyLevelConfigServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigAwardBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestDeleteFamilyLevelConfig;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestGetFamilyLevelConfigList;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestSaveFamilyLevelConfig;
import fm.lizhi.ocean.wavecenter.api.grow.level.service.FamilyLevelConfigService;
import fm.lizhi.sso.client.SessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/14 16:52
 */
@RestController
@RequestMapping("/grow/family/level")
public class LevelConfigController {

    @Autowired
    private FamilyLevelConfigServiceRemote familyLevelConfigServiceRemote;

    /**
     * 等级配置-保存
     * @param param
     * @return
     */
    @PostMapping("saveConfig")
    public ResultVO<Void> saveConfig(@RequestBody @Validated SaveLevelConfigParam param) {
        Result<Void> result = familyLevelConfigServiceRemote.save(new RequestSaveFamilyLevelConfig()
                .setId(param.getId())
                .setAppId(SessionExtUtils.getAppId())
                .setLevelName(param.getLevelName())
                .setMinIncome(param.getMinIncome())
                .setLevelIcon(param.getLevelIcon())
                .setLevelMedal(param.getLevelMedal())
                .setAwardImgs(param.getAwardImgs())
                .setThemColor(param.getThemColor())
                .setBackgroundColor(param.getBackgroundColor())
                .setOperator(SessionUtils.getAccount())
        );
        if (result.rCode() == FamilyLevelConfigService.SAVE_LEVEL_EXIST) {
            return ResultVO.failure("等级已存在");
        }
        if (result.rCode() == FamilyLevelConfigService.SAVE_LEVEL_INCOME_COVER) {
            return ResultVO.failure("流水区间已存在");
        }
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure("系统异常");
        }
        return ResultVO.success();
    }

    /**
     * 等级配置-删除
     * @param id
     * @return
     */
    @PostMapping("deleteConfig/{id}")
    public ResultVO<Void> deleteConfig(@PathVariable Long id) {
        Result<Void> result = familyLevelConfigServiceRemote.delete(new RequestDeleteFamilyLevelConfig()
                .setId(id)
                .setAppId(SessionExtUtils.getAppId())
        );
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure("系统异常");
        }
        return ResultVO.success();
    }

    /**
     * 等级配置-查询列表
     * @return
     */
    @GetMapping("list")
    public ResultVO<List<FamilyLevelConfigVO>> list(){
        Result<List<FamilyLevelConfigAwardBean>> result = familyLevelConfigServiceRemote.list(new RequestGetFamilyLevelConfigList()
                .setAppId(SessionExtUtils.getAppId()));
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure("系统异常");
        }
        return ResultVO.success(FamilyLevelConfigConvert.I.awardBeans2ConfigVOs(result.target()));
    }

}
