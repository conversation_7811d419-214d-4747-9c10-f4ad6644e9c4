package fm.lizhi.ocean.wave.management.model.result.anchor.singer;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.OriginalSingerInfoVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerFamilyInfoVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerUserInfoVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerVerifySongInfoVO;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SingerVerifyRecordForQueryResult extends SingerVerifyRecordResult {

    /**
     * 歌曲信息列表
     */
    private List<SingerVerifySongInfoVO> songInfoList = Lists.newArrayList();

}
