package fm.lizhi.ocean.wave.management.model.converter.anchor.singer;

import fm.lizhi.ocean.wave.management.model.dto.singer.SingerAuditParamDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.UpdateSingerVerifyStatusParamDTO;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.*;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.SingerVerifyRecordForQueryResult;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.SingerVerifyRecordResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerHistoryRecordVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerVerifyAuditLosingVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerVerifyRecordExcelVO;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerLosingBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestGetSingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestModifyVerifyApplyRemark;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestQueryHistoryVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestVerifyAudit;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseQueryHistoryVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
imports = {
    java.text.SimpleDateFormat.class,
    fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum.class
})
public interface SingerVerifyConvert {
    
    SingerVerifyConvert INSTANCE = Mappers.getMapper(SingerVerifyConvert.class);

    @Named("listToString")
    default String mapSocialVerifyImageList(List<String> list) {
        if (list == null || list.isEmpty()) {
            return "";
        }
        return String.join(",", list);
    }

    RequestModifyVerifyApplyRemark param2RemarkRequest(UpdateRemarkParam param, Integer appId);

    @Mapping(target = "inBlackList", source = "inBlackList")
    @Mapping(source = "singerUserInfo", target = "userInfo")
    SingerVerifyRecordResult response2Result(ResponseGetSingerVerifyRecord response);

    PageVO<SingerVerifyRecordResult> pageBean2PageVO(PageBean<ResponseGetSingerVerifyRecord> pageBean);

    @Mapping(target = "userInfo", source = "bean.singerUserInfo")
    @Mapping(target = "songInfoList", source = "bean.singerVerifySongInfoBeans")
    SingerVerifyRecordForQueryResult toSingerVerifyRecordForQueryResult(ResponseGetSingerVerifyRecord bean);

    PageVO<SingerVerifyRecordForQueryResult> pageBean2QueryResult(PageBean<ResponseGetSingerVerifyRecord> pageBean);

    RequestGetSingerVerifyRecord param2Request(GetApplyListParam param, Integer appId);

    RequestVerifyAudit param2OperateRequest(SingerVerifyAuditParam param);

    RequestQueryHistoryVerifyRecord param2QueryHistoryVerifyRecordRequest(QueryHistoryVerifyRecordParam param);
    
    List<SingerHistoryRecordVO> response2List(List<ResponseQueryHistoryVerifyRecord> response);

    List<SingerVerifyAuditLosingVO> convertAuditRes(List<SingerLosingBean> losingList);

    @Mappings({
        @Mapping(source = "userInfo.id", target = "singerUserId"),
        @Mapping(source = "userInfo.name", target = "name"),
        @Mapping(source = "userInfo.band", target = "band"),
        @Mapping(target = "gender", expression = "java(result.getUserInfo().getGender() == 1 ? \"女\" : \"男\")"),
        @Mapping(source = "familyInfo.familyName", target = "familyName"),
        @Mapping(source = "njInfo.id", target = "njId"),
        @Mapping(source = "njInfo.name", target = "njName"),
        @Mapping(source = "njInfo.band", target = "njBand"),
        @Mapping(target = "originalSinger", expression = "java(result.getOriginalSingerInfo().getOriginalSinger() ? \"是\" : \"否\")"),
        @Mapping(source = "originalSingerInfo.originalSongUrl", target = "originalSongUrl"),
        @Mapping(source = "originalSingerInfo.socialVerifyImageList", target = "socialVerifyImageList", qualifiedByName = "listToString"),
        @Mapping(target = "createTime", expression = "java(result.getCreateTime() != null ? new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\").format(new java.util.Date(result.getCreateTime())) : \"\")"),
        @Mapping(target = "auditTime", expression = "java(result.getAuditTime() != null ? new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\").format(new java.util.Date(result.getAuditTime())) : \"\")"),
        @Mapping(target = "auditStatus", expression = "java(result.getInBlackList() ? \"拉黑\" : SingerAuditStatusEnum.getByType(result.getAuditStatus()).getName())"),
        @Mapping(target = "rejectReason", expression = "java((result.getRejectReason() != null ? result.getRejectReason() : \"\") + (result.getPreAuditRejectReason() != null ? \"\\n\" + result.getPreAuditRejectReason() : \"\"))"),
        @Mapping(target = "contactNumber", expression = "java((result.getContactNumber() != null ? result.getContactNumber() : \"\"))")
    })
    SingerVerifyRecordExcelVO singerVerifyRecordResult2ExcelVO(SingerVerifyRecordResult result);

    List<SingerVerifyRecordExcelVO> singerVerifyRecordResult2ExcelVO(List<SingerVerifyRecordResult> result);

    @Mapping(target = "targetAuditStatus", source = "param.auditStatus")
    @Mapping(target = "operator", source = "param.operator")
    @Mapping(target = "rejectReason", source = "param.rejectedCause")
    @Mapping(target = "singerHallStatus", ignore = true)
    @Mapping(target = "needCheckSingerHallStatus", source = "needCheck")
    @Mapping(target = "passSongStyle", ignore = true)
    SingerAuditParamDTO pram2SingerAuditDTO(SingerVerifyAuditV2Param param, boolean needCheck);

    /**
     * 构建更新参数
     *
     * @param verifyRecord       审核记录
     * @param targetAuditStatus  目标审核状态
     * @param targetSingerStatus 目标歌手状态
     * @return 更新参数DTO
     */
    default UpdateSingerVerifyStatusParamDTO buildUpdateParam(SingerVerifyRecord verifyRecord, SingerAuditParamDTO param, Integer targetAuditStatus,
                                                              Integer targetSingerStatus, boolean needUpdateSingerInfo, Integer passSingerType) {
        UpdateSingerVerifyStatusParamDTO paramDTO = new UpdateSingerVerifyStatusParamDTO();
        paramDTO.setId(verifyRecord.getId());
        paramDTO.setCurrentAuditStatus(verifyRecord.getAuditStatus());
        paramDTO.setTargetAuditStatus(targetAuditStatus);
        paramDTO.setTargetSingerStatus(targetSingerStatus);
        paramDTO.setOperator(param.getOperator());
        paramDTO.setRejectReason(param.getRejectReason());
        paramDTO.setNeedUpdateSingerInfo(needUpdateSingerInfo);
        paramDTO.setPassSingerType(passSingerType);
        paramDTO.setPassSongStyle(param.getPassSongStyle());
        paramDTO.setOriginalSinger(param.isOriginalSinger());
        return paramDTO;
    }
}