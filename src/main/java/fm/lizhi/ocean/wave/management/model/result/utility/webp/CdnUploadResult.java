package fm.lizhi.ocean.wave.management.model.result.utility.webp;

import lombok.Data;

import java.beans.Transient;

/**
 * CDN上传结果
 */
@Data
public class CdnUploadResult {

    /**
     * 成功
     */
    public static final int CODE_SUCCESS = 0;
    /**
     * 失败
     */
    public static final int CODE_FAILURE = 1;

    /**
     * 状态码
     */
    private int code;

    /**
     * 消息, 失败时有值
     */
    private String msg;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件SHA256值
     */
    private String fileSha;

    /**
     * 文件字节数
     */
    private long fileSize;

    /**
     * 构造上传成功的结果
     *
     * @param filePath 文件路径
     * @param fileSha  文件SHA256值
     * @param fileSize 文件字节数
     * @return 成功的结果
     */
    public static CdnUploadResult success(String filePath, String fileSha, long fileSize) {
        CdnUploadResult result = new CdnUploadResult();
        result.setCode(CODE_SUCCESS);
        result.setFilePath(filePath);
        result.setFileSha(fileSha);
        result.setFileSize(fileSize);
        return result;
    }

    /**
     * 构造上传失败的结果
     *
     * @param msg 错误消息
     * @return 失败的结果
     */
    public static CdnUploadResult failure(String msg) {
        CdnUploadResult result = new CdnUploadResult();
        result.setCode(CODE_FAILURE);
        result.setMsg(msg);
        return result;
    }

    /**
     * 是否成功
     *
     * @return 是否成功
     */
    @Transient
    public boolean isSuccess() {
        return code == CODE_SUCCESS;
    }

    /**
     * 是否失败
     *
     * @return 是否失败
     */
    @Transient
    public boolean isFailure() {
        return !isSuccess();
    }
}
