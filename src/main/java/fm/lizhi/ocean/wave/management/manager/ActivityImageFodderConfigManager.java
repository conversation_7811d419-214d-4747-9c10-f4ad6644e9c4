package fm.lizhi.ocean.wave.management.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ActivityImageFodderConfigConvert;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.PageActivityImageFodderParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityImageFodderParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityImageFodderParam;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityImageFodderResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.SaveActivityImageFodderResult;
import fm.lizhi.ocean.wave.management.remote.service.activitycenter.ActivityImageFodderConfigServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityImageFodderBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestPageActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseSaveActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityImageFodderConfigManager {

    @Autowired
    private ActivityImageFodderConfigServiceRemote activityImageFodderConfigServiceRemote;


    public ResultVO<SaveActivityImageFodderResult> save(SaveActivityImageFodderParam param, Integer appId, String operator){
        RequestSaveActivityImageFodder request = ActivityImageFodderConfigConvert.I.toRequestSaveActivityImageFodder(param, appId, operator);
        Result<ResponseSaveActivityImageFodder> result = activityImageFodderConfigServiceRemote.save(request);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }

        SaveActivityImageFodderResult saveActivityImageFodderResult = ActivityImageFodderConfigConvert.I.toSaveActivityImageFodderResult(result.target());

        return ResultVO.success(saveActivityImageFodderResult);
    }

    public ResultVO<Void> update(UpdateActivityImageFodderParam param, Integer appId, String operator){
        RequestUpdateActivityImageFodder request = ActivityImageFodderConfigConvert.I.toRequestUpdateActivityImageFodder(param, appId, operator);
        Result<Void> result = activityImageFodderConfigServiceRemote.update(request);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success();
    }

    public ResultVO<Void> delete(Long id, Integer appId, String operator){
        Result<Void> result = activityImageFodderConfigServiceRemote.delete(id, appId, operator);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success();
    }

    public ResultVO<PageVO<ActivityImageFodderResult>> list(PageActivityImageFodderParam param, Integer appId){
        RequestPageActivityImageFodder request = ActivityImageFodderConfigConvert.I.toRequestPageActivityImageFodder(param, appId);
        Result<PageBean<ActivityImageFodderBean>> result = activityImageFodderConfigServiceRemote.list(request);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }

        return ResultVO.success(ActivityImageFodderConfigConvert.I.toActivityImageFodderResultPageVO(result.target()));

    }



}
