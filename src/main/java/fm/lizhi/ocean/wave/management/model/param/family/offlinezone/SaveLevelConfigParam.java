package fm.lizhi.ocean.wave.management.model.param.family.offlinezone;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import lombok.Data;
import org.apache.commons.collections4.ListUtils;

import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.beans.Transient;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 保存等级配置参数, 作为新增和更新的基类
 */
@Data
public abstract class SaveLevelConfigParam {

    /**
     * 等级key, 用于前端做UI关联
     */
    @NotEmpty(message = "等级key不能为空")
    @Size(max = 50, message = "等级key长度不能超过{max}个字")
    private String levelKey;

    /**
     * 等级名称
     */
    @NotEmpty(message = "等级名称不能为空")
    @Size(max = 50, message = "等级名称长度不能超过{max}个字")
    private String levelName;

    /**
     * 等级顺序（数字越高等级越高）
     */
    @NotNull(message = "等级顺序不能为空")
    private Integer levelOrder;

    /**
     * 等级权益关联列表
     */
    @JsonSetter(nulls = Nulls.SKIP)
    @Valid
    private List<Right> rights = Collections.emptyList();

    @AssertTrue(message = "等级权益关联列表有重复的权益ID")
    @Transient
    protected boolean isRightsValid() {
        List<Long> rightIds = ListUtils.emptyIfNull(rights).stream()
                .filter(Objects::nonNull).map(Right::getRightId).collect(Collectors.toList());
        return new HashSet<>(rightIds).size() == rightIds.size();
    }

    /**
     * 等级权益关联信息
     */
    @Data
    public static class Right {

        /**
         * 权益ID
         */
        @NotNull(message = "权益ID不能为空")
        private Long rightId;

        /**
         * 是否解锁, true表示当前等级可以获得该权益
         */
        @NotNull(message = "是否解锁不能为空")
        private Boolean unlocked;

        /**
         * 解锁等级ID, 当unlocked为false时必填
         */
        @NotNull(message = "解锁等级ID不能为空")
        @JsonSetter(nulls = Nulls.SKIP)
        private Long unlockedLevelId = 0L;
    }
}
