package fm.lizhi.ocean.wave.management.controller.activitycenter.ai;

import fm.lizhi.ocean.wave.management.manager.activitycenter.ai.ActivityAiResultRateManager;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.ai.ActivityAiResultRateParam;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;

import fm.lizhi.sso.client.SessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/activity/ai")
public class ActivityAiResultRateController {
    @Autowired
    private ActivityAiResultRateManager activityAiResultRateManager;

    @PostMapping("/rateAiResult")
    public ResultVO<Void> rateAiResult(@RequestBody @Validated ActivityAiResultRateParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String creator = SessionUtils.getAccount();
        return activityAiResultRateManager.saveActivityAiResultRate(param, appId, creator);
    }
} 