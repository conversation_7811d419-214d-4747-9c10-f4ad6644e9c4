package fm.lizhi.ocean.wave.management.model.converter.resource;

import java.util.List;

import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wave.management.model.vo.resource.MedalVO;
import fm.lizhi.ocean.wave.management.model.vo.resource.MountVO;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;


@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface DecorateConvert {

    DecorateConvert INSTANCE = Mappers.getMapper(DecorateConvert.class);
    
    @Mapping(target = "id", source = "decorateId")
    @Mapping(target = "name", source = "decorateName")
    @Mapping(target = "image", source = "decorateImage")
    MountVO toMountVO(DecorateInfoBean decorateInfoBean);

    List<MountVO> toMountVOList(List<DecorateInfoBean> decorateInfoBeans);

    @Mapping(target = "id", source = "decorateId")
    @Mapping(target = "name", source = "decorateName")
    @Mapping(target = "image", source = "decorateImage")
    MedalVO toMedalVO(DecorateInfoBean decorateInfoBean);

    List<MedalVO> toMedalVOList(List<DecorateInfoBean> decorateInfoBeans);
}
