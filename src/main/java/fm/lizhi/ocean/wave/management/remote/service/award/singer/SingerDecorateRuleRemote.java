package fm.lizhi.ocean.wave.management.remote.service.award.singer;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.award.singer.SingerDecorateRuleConvert;
import fm.lizhi.ocean.wave.management.model.param.award.singer.PageSingerDecorateFlowParam;
import fm.lizhi.ocean.wave.management.model.param.award.singer.PageSingerDecorateRuleParam;
import fm.lizhi.ocean.wave.management.model.param.award.singer.SaveSingerDecorateRuleParam;
import fm.lizhi.ocean.wave.management.model.param.award.singer.UpdateSingerDecorateRuleParam;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestPageSingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestPageSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestSaveSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestUpdateSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.response.ResponseSingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.api.award.singer.response.ResponseSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.serivce.SingerDecorateFlowService;
import fm.lizhi.ocean.wavecenter.api.award.singer.serivce.SingerDecorateRuleService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SingerDecorateRuleRemote {

    @Autowired
    private SingerDecorateRuleService singerDecorateRuleService;

    @Autowired
    private SingerDecorateFlowService singerDecorateFlowService;

    /**
     * 分页获取歌手装扮规则配置列表
     */
    public Result<PageBean<ResponseSingerDecorateRule>> pageSingerDecorateRule(PageSingerDecorateRuleParam param, Integer appId) {
        RequestPageSingerDecorateRule request = SingerDecorateRuleConvert.INSTANCE.convertPageRequest(param, appId);
        Result<PageBean<ResponseSingerDecorateRule>> result = singerDecorateRuleService.pageSingerDecorateRule(request);
        if (ResultUtils.isFailure(result)) {
            log.warn("pageSingerDecorateRule is fail. appId: {}", appId);
            return result;
        }
        return result;
    }

    /**
     * 保存歌手装扮规则配置
     */
    public Result<Void> saveSingerDecorateRule(SaveSingerDecorateRuleParam param, Integer appId, String operator) {
        RequestSaveSingerDecorateRule request = SingerDecorateRuleConvert.INSTANCE.convertSaveRequest(param, appId, operator);
        Result<Void> result = singerDecorateRuleService.saveSingerDecorateRule(request);
        if (ResultUtils.isFailure(result)) {
            log.warn("saveSingerDecorateRule is fail. appId: {}, operator: {}", appId, operator);
            return result;
        }
        return result;
    }

    /**
     * 更新歌手装扮规则配置
     */
    public Result<Void> updateSingerDecorateRule(UpdateSingerDecorateRuleParam param, Integer appId, String operator) {
        RequestUpdateSingerDecorateRule request = SingerDecorateRuleConvert.INSTANCE.convertUpdateRequest(param, appId, operator);
        Result<Void> result = singerDecorateRuleService.updateSingerDecorateRule(request);
        if (ResultUtils.isFailure(result)) {
            log.warn("updateSingerDecorateRule is fail. appId: {}, operator: {}", appId, operator);
            return result;
        }
        return result;
    }

    /**
     * 分页查询歌手装扮流水
     */
    public Result<PageBean<ResponseSingerDecorateFlow>> pageSingerDecorateFlow(PageSingerDecorateFlowParam param, Integer appId) {
        RequestPageSingerDecorateFlow request = SingerDecorateRuleConvert.INSTANCE.convertRequestPageSingerDecorateFlow(param, appId);
        Result<PageBean<ResponseSingerDecorateFlow>> result = singerDecorateFlowService.pageSingerDecorateFlow(request);
        if (ResultUtils.isFailure(result)) {
            log.warn("pageSingerDecorateFlow is fail. appId: {}", appId);
            return result;
        }
        return result;
    }
}
