package fm.lizhi.ocean.wave.management.controller.permission;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.permission.PermissionConvert;
import fm.lizhi.ocean.wave.management.model.param.permission.GetStatementListParam;
import fm.lizhi.ocean.wave.management.model.param.permission.SaveStatementParam;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.permission.StatementVO;
import fm.lizhi.ocean.wave.management.remote.service.permission.StatementServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wave.platform.api.common.bean.PageResult;
import fm.lizhi.ocean.wave.platform.api.permission.bean.StatementBean;
import fm.lizhi.ocean.wave.platform.api.permission.constants.StatementStatusEnum;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestGetStatementList;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestSaveStatement;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestUpdateStatementStatus;
import fm.lizhi.ocean.wave.platform.api.permission.service.StatementService;
import fm.lizhi.sso.client.SessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/20 15:31
 */
@RestController
@RequestMapping("/permission/statement")
public class StatementController {

    @Autowired
    private StatementServiceRemote statementServiceRemote;

    /**
     * 查询策略列表
     * @param param
     * @return
     */
    @GetMapping("list")
    public ResultVO<PageVO<StatementVO>> list(GetStatementListParam param){
        Result<PageResult<StatementBean>> result = statementServiceRemote.getStatementList(new RequestGetStatementList()
                .setPageNo(param.getPageNo())
                .setPageSize(param.getPageSize())
                .setResourceType(param.getResourceType())
                .setAppId(SessionExtUtils.getAppId())
                .setResourceCodes(param.getResourceCodes())
                .setStatus(param.getStatus())
        );
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure("服务异常");
        }

        List<StatementVO> statementVOS = PermissionConvert.I.statementBeans2VOs(result.target().getList());
        return ResultVO.success(PageVO.of(result.target().getTotal(), statementVOS));
    }

    /**
     * 修改策略状态
     * @return
     */
    @PostMapping("updateStatus/{id}/{status}")
    public ResultVO<Void> updateStatus(@PathVariable Long id, @PathVariable Integer status){
        Result<Void> result = statementServiceRemote.updateStatementStatus(new RequestUpdateStatementStatus()
                .setId(id)
                .setStatus(StatementStatusEnum.getByValue(status))
                .setOperator(SessionUtils.getAccount())
        );
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure("系统异常");
        }
        return ResultVO.success();
    }

    /**
     * 保存策略
     * @return
     */
    @PostMapping("save")
    public ResultVO<Void> save(@RequestBody @Validated SaveStatementParam param){
        Result<Void> result = statementServiceRemote.saveStatement(new RequestSaveStatement()
                .setId(param.getId())
                .setOperator(SessionUtils.getAccount())
                .setAppId(SessionExtUtils.getAppId())
                .setStatementDsl(param.getStatementDsl().toString())
        );
        if (result.rCode() == StatementService.SAVE_STATEMENT_STATEMENT_NOT_EXIST) {
            return ResultVO.failure("策略不存在");
        }
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure("系统异常");
        }
        return ResultVO.success();
    }

}
