package fm.lizhi.ocean.wave.management.model.param.permission;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/2/20 15:34
 */
@Data
public class SaveStatementParam {

    /**
     * ID
     * 编辑的时候不可为空
     */
    private Long id;

    /**
     * 策略dsl
     */
    @NotNull(message = "策略DSL不可为空")
    private JSONObject statementDsl;

}
