package fm.lizhi.ocean.wave.management.config;

import fm.lizhi.ocean.wave.management.common.validation.BeanValidator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 验证配置
 */
@Configuration
public class ValidationConfiguration {

    /**
     * Bean校验器, 供手动校验Bean使用
     *
     * @return Bean校验器
     */
    @Bean
    public BeanValidator beanValidator() {
        return new BeanValidator();
    }
}
