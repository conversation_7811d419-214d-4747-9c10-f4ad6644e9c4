package fm.lizhi.ocean.wave.management.model.vo.award.family;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/26 11:26
 */
@Data
public class AwardDeliverRecordV2ExcelVO {
    /**
     * 奖励发放记录ID
     */
    @ExcelProperty("奖励发放记录ID")
    private String id;

    /**
     * 公会ID
     */
    @ExcelProperty("公会ID")
    private String familyId;

    /**
     * 公会名称
     */
    @ExcelProperty("公会名称")
    private String familyName;

    /**
     * 家族长用户ID
     */
    @ExcelProperty("家族长用户ID")
    private String familyUserId;

    /**
     * 家族长用户名
     */
    @ExcelProperty("家族长用户名")
    private String familyUserName;

    /**
     * 发放周期开始时间, 毫秒时间戳, 周一的00:00:00.000
     */
    @ExcelProperty("发放周期开始时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private Date awardStartTime;

    /**
     * 发放周期结束时间, 毫秒时间戳, 周日的23:59:59.999
     */
    @ExcelProperty("发放周期结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private Date awardEndTime;

    /**
     * 发放时间, 毫秒时间戳
     */
    @ExcelProperty("发放时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private Date deliverTime;

    /**
     * 发放状态, 1-发放中, 2-发放成功, 3-发放失败, 4-部分失败
     */
    @ExcelProperty("发放状态")
    private String statusStr;
    @ExcelIgnore
    private Integer status;

    /**
     * 记录生成时间, 毫秒时间戳
     */
    @ExcelProperty("记录生成时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * (PP)推荐卡总数
     */
    @ExcelProperty("推荐卡总数")
    private Integer totalRecommendCardNumber;

    /**
     * (PP)等级推荐卡数量
     */
    @ExcelProperty("等级推荐卡数量")
    private Integer levelRecommendCardNumber;

    /**
     * (PP)流水增长推荐卡数量
     */
    @ExcelProperty("流水增长推荐卡数量")
    private Integer flowGrowthRecommendCardNumber;

    /**
     * (PP)新厅留存推荐卡数量
     */
    @ExcelProperty("新厅留存推荐卡数量")
    private Integer newRoomRetainRecommendCardNumber;

    /**
     * (PP)0流失厅推荐卡数量
     */
    @ExcelProperty("0流失厅推荐卡数量")
    private Integer zeroLostRoomRecommendCardNumber;

    /**
     * (PP)特殊推荐卡数量
     */
    @ExcelProperty("特殊推荐卡数量")
    private Integer specialRecommendCardNumber;

    /**
     * (PP)新厅名额总数
     */
    @ExcelProperty("新厅名额总数")
    private Integer totalNewRoomNumber;

    /**
     * (PP)等级新厅名额数量
     */
    @ExcelProperty("等级新厅名额数量")
    private Integer levelNewRoomNumber;

    /**
     * (PP)流水涨幅新厅名额数量
     */
    @ExcelProperty("流水涨幅新厅名额数量")
    private Integer flowGrowthNewRoomNumber;

    /**
     * (PP)流失厅新厅名额数量
     */
    @ExcelProperty("流失厅新厅名额数量")
    private Integer lostRoomNewRoomNumber;

    /**
     * (PP)新厅留存新厅名额数量
     */
    @ExcelProperty("新厅留存新厅名额数量")
    private Integer newRoomRetainNewRoomNumber;
}