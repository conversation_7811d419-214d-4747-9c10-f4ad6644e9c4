package fm.lizhi.ocean.wave.management.model.result.activitycenter.ai;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 保存业务素材图片结果
 */
@Data
public class SaveBusinessImageResult {

    /**
     * 素材id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 素材预览图片地址, 业务侧的域名
     */
    private String previewUrl;
}
