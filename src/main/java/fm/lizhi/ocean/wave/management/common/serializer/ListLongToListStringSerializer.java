package fm.lizhi.ocean.wave.management.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import fm.lizhi.ocean.wave.management.util.JsonUtils;

import java.io.IOException;
import java.util.List;

/**
 * 将{@code List<Long>}序列化为{@code List<String>}的序列化器, 通常用于id列表的序列化, 避免前端处理时出现精度丢失
 */
public class ListLongToListStringSerializer extends StdSerializer<List<Long>> {

    /**
     * 构造方法, 面向{@code List<Long>}类型
     */
    public ListLongToListStringSerializer() {
        super(JsonUtils.constructListType(Long.class));
    }

    @Override
    public void serialize(List<Long> value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.setCurrentValue(value);
        gen.writeStartArray(value.size());
        for (Long l : value) {
            if (l == null) {
                gen.writeNull();
            } else {
                gen.writeString(l.toString());
            }
        }
        gen.writeEndArray();
    }
}
