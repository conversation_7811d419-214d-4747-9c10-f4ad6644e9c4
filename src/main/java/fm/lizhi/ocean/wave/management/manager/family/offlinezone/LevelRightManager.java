package fm.lizhi.ocean.wave.management.manager.family.offlinezone;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.management.datastore.dao.family.offlinezone.LevelRightDao;
import fm.lizhi.ocean.wave.management.model.converter.family.offlinezone.LevelRightConvert;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.AddLevelRightParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.DeleteLevelRightParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.SaveLevelRightParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.UpdateLevelRightParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.AddLevelRightResult;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListLevelRightResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRight;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRightIntroduction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.ListValuedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 线下专区等级权益管理器
 */
@Component
@Slf4j
public class LevelRightManager {

    @Autowired
    private LevelRightConvert levelRightConvert;

    @Autowired
    private LevelRightDao levelRightDao;

    /**
     * 新增等级权益
     *
     * @param param 新增参数
     * @return 新增结果的VO
     */
    public ResultVO<AddLevelRightResult> addLevelRight(AddLevelRightParam param) {
        Long rightId = levelRightDao.addLevelRight(param);
        AddLevelRightResult result = levelRightConvert.toAddLevelRightResult(rightId);
        return ResultVO.success(result);
    }

    /**
     * 更新等级权益
     *
     * @param param 更新参数
     * @return 更新结果的VO
     */
    public ResultVO<Void> updateLevelRight(UpdateLevelRightParam param) {
        Long id = param.getId();
        OfflineZoneLevelRight oldRight = levelRightDao.getLevelRight(id);
        if (oldRight == null) {
            return ResultVO.failure("权益ID不存在: " + id);
        }
        if (!Objects.equals(oldRight.getDeployEnv(), ConfigUtils.getEnvRequired().name())) {
            return ResultVO.failure("权益ID不属于当前环境: " + id);
        }
        if (oldRight.getDeleted()) {
            return ResultVO.failure("权益ID已被删除: " + id);
        }
        boolean shouldUpdateIntroductions = shouldUpdateIntroductions(param);
        levelRightDao.updateLevelRight(param, shouldUpdateIntroductions);
        return ResultVO.success();
    }

    private boolean shouldUpdateIntroductions(UpdateLevelRightParam param) {
        Long rightId = param.getId();
        List<SaveLevelRightParam.Introduction> newIntroductions = ListUtils.emptyIfNull(param.getIntroductions());
        List<OfflineZoneLevelRightIntroduction> oldIntroductions = levelRightDao.getIntroductionsByRightId(rightId);
        if (CollectionUtils.size(newIntroductions) != CollectionUtils.size(oldIntroductions)) {
            return true;
        }
        for (int i = 0; i < newIntroductions.size(); i++) {
            SaveLevelRightParam.Introduction newIntroduction = newIntroductions.get(i);
            OfflineZoneLevelRightIntroduction oldIntroduction = oldIntroductions.get(i);
            if (!Objects.equals(newIntroduction.getTitle(), oldIntroduction.getTitle())
                || !Objects.equals(newIntroduction.getContent(), oldIntroduction.getContent())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 删除等级权益
     *
     * @param param 删除参数
     * @return 删除结果的VO
     */
    public ResultVO<Void> deleteLevelRight(DeleteLevelRightParam param) {
        Long id = param.getId();
        OfflineZoneLevelRight oldRight = levelRightDao.getLevelRight(id);
        if (oldRight == null) {
            return ResultVO.failure("权益ID不存在: " + id);
        }
        if (!Objects.equals(oldRight.getDeployEnv(), ConfigUtils.getEnvRequired().name())) {
            return ResultVO.failure("权益ID不属于当前环境: " + id);
        }
        if (oldRight.getDeleted()) {
            return ResultVO.failure("权益ID已被删除: " + id);
        }
        levelRightDao.deleteLevelRight(param);
        return ResultVO.success();
    }

    /**
     * 列出等级权益
     *
     * @return 等级权益列表的VO
     */
    public ResultVO<List<ListLevelRightResult>> listLevelRight() {
        List<OfflineZoneLevelRight> rights = levelRightDao.listLevelRights();
        List<Long> rightIds = rights.stream().map(OfflineZoneLevelRight::getId).collect(Collectors.toList());
        ListValuedMap<Long, OfflineZoneLevelRightIntroduction> levelRightIntroductionsMap = levelRightDao.getLevelRightIntroductionsMap(rightIds);
        List<ListLevelRightResult> resultList = levelRightConvert.toListLevelRightResults(rights, levelRightIntroductionsMap);
        return ResultVO.success(resultList);
    }
}
