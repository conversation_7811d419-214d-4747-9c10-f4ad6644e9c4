package fm.lizhi.ocean.wave.management.controller.anchor.singer;

import fm.lizhi.ocean.wave.management.manager.singer.SingerVerifyManager;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.SingerVerifyAuditV2Param;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.SingerVerifyAuditResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/singer")
public class SingerVerifyAdminController {

    @Autowired
    private SingerVerifyManager singerVerifyManager;

    /**
     * 歌手认证审核
     */
    @PostMapping("/verify/auditV2")
    public ResultVO<SingerVerifyAuditResult> auditVerifyV2(@RequestBody @Valid SingerVerifyAuditV2Param param) {
        param.setAppId(SessionExtUtils.getAppId());
        param.setOperator(SessionUtils.getAccount());
        return singerVerifyManager.auditVerifyV2(param);
    }
}
