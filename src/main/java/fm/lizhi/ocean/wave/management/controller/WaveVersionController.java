package fm.lizhi.ocean.wave.management.controller;

import fm.lizhi.ocean.wave.management.manager.WaveVersionManager;
import fm.lizhi.ocean.wave.management.model.param.platform.SyncGrayGroupUserParam;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 创作者版本控制器
 */
@RestController
@RequestMapping("/waveVersion")
@Slf4j
public class WaveVersionController {

    @Autowired
    private WaveVersionManager waveVersionManager;

    /**
     * 同步灰度分组成员
     *
     * @param param 同步灰度分组成员参数
     * @return 同步灰度分组成员结果
     */
    @PostMapping("/syncGrayGroupUser")
    public ResultVO<Void> syncGrayGroupUser(@RequestBody @Validated SyncGrayGroupUserParam param) {
        log.info("syncGrayGroupUser param={}", param);
        return waveVersionManager.syncGrayGroupUser(param);
    }
}
