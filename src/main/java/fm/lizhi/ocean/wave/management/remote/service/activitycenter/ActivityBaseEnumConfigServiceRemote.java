package fm.lizhi.ocean.wave.management.remote.service.activitycenter;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityCommonService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityConfigBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 基础业务配置
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityBaseEnumConfigServiceRemote {

    @Autowired
    private ActivityCommonService activityCommonService;


    /**
     * 获取一些基础的枚举配置
     */
    public Result<ResponseActivityConfigBean> getBaseEnumConfig(int appId) {
        Result<ResponseActivityConfigBean> result = activityCommonService.getBaseEnumConfig(appId);
        if (ResultUtils.isFailure(result)) {
            log.warn("getBaseEnumConfig is fail, appId:{}, rCode: {}", appId, result.rCode());
        }
        return result;
    }


}
