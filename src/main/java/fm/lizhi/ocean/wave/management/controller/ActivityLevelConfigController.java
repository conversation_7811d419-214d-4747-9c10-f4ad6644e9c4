package fm.lizhi.ocean.wave.management.controller;

import fm.lizhi.ocean.wave.management.manager.ActivityLevelConfigManager;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.DeleteActivityLevelParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityLevelParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityLevelParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityLevelConfigResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 活动等级配置
 */
@RestController
@RequestMapping("/activity/level")
@Slf4j
public class ActivityLevelConfigController {

    @Autowired
    private ActivityLevelConfigManager activityLevelConfigManager;


    /**
     * 保存活动等级
     */
    @PostMapping("/save")
    public ResultVO<Boolean> save(@Validated @RequestBody SaveActivityLevelParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("save activity level, param: {}, appId: {}, operator: {}", param, appId, operator);
        return activityLevelConfigManager.save(param, appId, operator);
    }

    /**
     * 更新活动等级
     */
    @PostMapping("/update")
    public ResultVO<Boolean> update(@Validated @RequestBody UpdateActivityLevelParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("update activity level, param: {}, appId: {}, operator: {}", param, appId, operator);
        return activityLevelConfigManager.update(param, appId, operator);
    }


    /**
     * 删除活动等级
     */
    @PostMapping("/delete")
    public ResultVO<Boolean> delete(@RequestBody DeleteActivityLevelParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("delete activity level, id: {}, appId: {}, operator: {}", param, appId, operator);
        return activityLevelConfigManager.delete(param.getId(), appId, operator);
    }

    /**
     * 列表
     */
    @GetMapping("/list")
    public ResultVO<List<ActivityLevelConfigResult>> list() {
        Integer appId = SessionExtUtils.getAppId();
        log.info("list activity level, appId: {}", appId);
        return activityLevelConfigManager.list(appId);
    }

    /**
     * 根据分类 ID 查询等级
     * @param classId
     * @return
     */
    @GetMapping("/getLevelByClassId")
    public ResultVO<ActivityLevelConfigResult> getLevelByClassId(@RequestParam("classId") Long classId) {
        Integer appId = SessionExtUtils.getAppId();
        log.info("getLevelByClassId, appId: {}", appId);
        return activityLevelConfigManager.getLevelByClassId(appId, classId);
    }

}
