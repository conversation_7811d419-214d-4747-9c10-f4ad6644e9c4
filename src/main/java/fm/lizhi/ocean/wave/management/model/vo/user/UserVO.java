package fm.lizhi.ocean.wave.management.model.vo.user;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/4/11 14:40
 */
@Data
@Accessors(chain = true)
public class UserVO {
    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 昵称
     */
    private String name;
    /**
     * 波段号
     */
    private String band;

    /**
     * 头像
     */
    private String photo;
}
