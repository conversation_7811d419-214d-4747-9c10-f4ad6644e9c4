package fm.lizhi.ocean.wave.management.model.param.family.offlinezone;

import lombok.Data;


/**
 * 查询线下主播数据列表参数
 * <AUTHOR>
 */
@Data
public class ListPlayerDataParam {

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 主播波段号，精确查询
     */
    private String playerBand;

    /**
     * 主播实名姓名，模糊查询
     */
    private String idName;

    /**
     * 厅波段号，精确查询
     */
    private String njBand;

    /**
     * 公会名称，模糊查询
     */
    private String familyName;

    /**
     * 主播ID，精确查询
     */
    private Long playerId;

    /**
     * 主播身份证，精确查询
     */
    private String idCardNumber;

    /**
     * 厅ID，精确查询
     */
    private Long njId;

    /**
     * 公会ID，精确查询
     */
    private Long familyId;

    /**
     * 主播分类：0 线下，1 线上
     */
    private Integer category;

    /**
     * 是否受保护
     */
    private Boolean protection;

    /**
     * 查询开始时间
     */
    private String startWeekDate;

    /**
     * 查询结束时间
     */
    private String endWeekDate;

    // 分页参数
    private Integer pageNo = 1;

    private Integer pageSize = 10;

}