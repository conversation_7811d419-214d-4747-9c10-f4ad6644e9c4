package fm.lizhi.ocean.wave.management.model.converter.family.offlinezone;

import java.util.ArrayList;
import java.util.List;

import fm.lizhi.ocean.wave.management.config.apollo.CommonConfig;
import fm.lizhi.ocean.wave.management.util.UrlUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wave.management.model.converter.CommonConvert;
import fm.lizhi.ocean.wave.management.model.dto.family.offlinezone.ProtectionHistoryDTO;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ProtectionHistoryResult;
import fm.lizhi.ocean.wave.management.model.vo.family.offlinezone.AgreementFileVO;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtectionHistory;

/**
 * 跳槽保护协议历史记录转换器
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = CommonConvert.class
)
public abstract class ProtectionHistoryConvert {


    @Autowired
    protected CommonConfig commonConfig;


    /**
     * 实体列表转换为DTO列表
     *
     * @param entities 实体列表
     * @return DTO列表
     */
    public abstract List<ProtectionHistoryDTO> entityListToDTOList(List<OfflineZoneProtectionHistory> entities);

    /**
     * DTO列表转换为Result列表
     *
     * @param dtos DTO列表
     * @return Result列表
     */
    public abstract List<ProtectionHistoryResult> dtoListToResultList(List<ProtectionHistoryDTO> dtos);

    @Mapping(target = "agreementFileList" , source = "dto.agreementFileJson", qualifiedByName = "jsonToFileList")
    public abstract ProtectionHistoryResult dtoToResult(ProtectionHistoryDTO dto);


    /**
     * JSON字符串转换为文件列表
     *
     * @param agreementFileJson JSON字符串
     * @return 文件列表
     */
    @Named("jsonToFileList")
    List<AgreementFileVO> jsonToFileList(String agreementFileJson) {
        if (!StringUtils.hasText(agreementFileJson)) {
            return new ArrayList<>();
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            List<AgreementFileVO> list = objectMapper.readValue(agreementFileJson, new TypeReference<List<AgreementFileVO>>() {});
            list.forEach(vo -> vo.setUrl(addWaveCdnHost(vo.getUrl())));
            return list;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    @Named("addWaveCdnHost")
    protected String addWaveCdnHost(String path) {
        return UrlUtils.addHostOrEmpty(path, commonConfig.getWaveCdn().getCdnHost());
    }

}