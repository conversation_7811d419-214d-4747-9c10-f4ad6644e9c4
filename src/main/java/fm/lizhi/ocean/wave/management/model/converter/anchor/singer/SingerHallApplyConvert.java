
package fm.lizhi.ocean.wave.management.model.converter.anchor.singer;

import fm.lizhi.ocean.wave.management.model.converter.CommonConvert;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.ImportHallApplyParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.OperateHallApplyParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.PageHallApplyParam;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.PageHallApplyResult;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerSingHallApplyRecordSummaryExcelVO;
import fm.lizhi.ocean.wave.management.model.vo.family.FamilyVO;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerSingHallApplyRecordSummaryBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplySourceEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestImportHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestOperateHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestPageHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponsePageHallApply;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {ActivityApplyTypeEnum.class, SingerHallApplyStatusEnum.class},
        uses = {CommonConvert.class}
)
public interface SingerHallApplyConvert {

    SingerHallApplyConvert I = Mappers.getMapper(SingerHallApplyConvert.class);

    RequestPageHallApply buildRequestPageHallApply(PageHallApplyParam param, Integer appId);

    RequestImportHallApply buildRequestImportHallApply(ImportHallApplyParam param, Integer appId, String operator, SingerHallApplySourceEnum source);

    PageHallApplyResult convertPageHallApplyResult(ResponsePageHallApply target);

    @Mapping(target = "name", source = "bean.familyName")
    FamilyVO convertFamilyVO(FamilyBean bean);

    @Mapping(target = "status", expression = "java(SingerHallApplyStatusEnum.getByStatus(param.getStatus()))")
    RequestOperateHallApply buildRequestOperateHallApply(OperateHallApplyParam param, Integer appId, String operator);

    @Mappings({
        @Mapping(target = "band", source = "njInfo.band"),
        @Mapping(target = "familyIconUrl", source = "familyInfo.familyIconUrl"),
        @Mapping(target = "familyName", source = "familyInfo.familyName"),
        @Mapping(target = "familyNote", source = "familyInfo.familyNote"),
        @Mapping(target = "familyType", source = "familyInfo.familyType"),
        @Mapping(target = "userId", source = "familyInfo.userId"),
        @Mapping(target = "userType", source = "familyInfo.userType"),
        @Mapping(target = "njName", source = "njInfo.name"),
        @Mapping(target = "photo", source = "njInfo.photo")
        
    })
    SingerSingHallApplyRecordSummaryExcelVO convertSingerSingHallApplyRecordSummaryExcelVO(
            SingerSingHallApplyRecordSummaryBean bean);

    List<SingerSingHallApplyRecordSummaryExcelVO> convertSingerSingHallApplyRecordSummaryExcelVOs(
            List<SingerSingHallApplyRecordSummaryBean> list);
}
