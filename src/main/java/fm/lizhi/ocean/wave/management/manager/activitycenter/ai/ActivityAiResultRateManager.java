package fm.lizhi.ocean.wave.management.manager.activitycenter.ai;

import fm.lizhi.ocean.wave.management.model.param.activitycenter.ai.ActivityAiResultRateParam;
import fm.lizhi.ocean.wave.management.remote.service.activitycenter.ai.ActivityAiResultRateRemote;
import fm.lizhi.commons.service.client.pojo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;

@Service
public class ActivityAiResultRateManager {
    @Autowired
    private ActivityAiResultRateRemote activityAiResultRateRemote;

    public ResultVO<Void> saveActivityAiResultRate(ActivityAiResultRateParam param, Integer appId, String creator) {
        Result<Void> result = activityAiResultRateRemote.saveActivityAiResultRate(param, appId, creator);
        if (result.rCode() != 0) {
            return ResultVO.failure(result.getMessage());
        }
        return ResultVO.success();
    }
} 