package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.DecorateEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestGetDecorate;
import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PageDecorateParam {


    /**
     * 类型 1：头像框，2：背景
     * @see DecorateEnum
     */
    private Integer type;

    /**
     * 装扮ID
     */
    private Long dressUpId;

    /**
     * 装扮名称
     */
    private String name;

    private int pageNo = 1;

    private int pageSize = 20;
}
