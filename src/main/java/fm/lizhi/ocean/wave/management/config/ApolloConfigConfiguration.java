package fm.lizhi.ocean.wave.management.config;

import fm.lizhi.ocean.wave.management.config.apollo.*;
import fm.lizhi.ocean.wave.management.config.apollo.singer.SingerConfig;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Apollo配置入口类. 使用{@code EnableConfigurationProperties}添加划分的配置类, 使用{@code ApolloNamespaceInclude}添加除
 * application以外的namespace.
 */
@Configuration
@EnableConfigurationProperties({
        ActivityConfig.class,
        BusinessConfig.class,
        CommonConfig.class,
        DubboClientConfig.class,
        ProjectConfig.class,
        SsoClientConfig.class,
        SingerConfig.class,
        UtilityConfig.class,
})
public class ApolloConfigConfiguration {
}
