package fm.lizhi.ocean.wave.management.controller;


import fm.lizhi.ocean.wave.management.manager.ActivityImageFodderConfigManager;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.DeleteActivityImageFodderParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.PageActivityImageFodderParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityImageFodderParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityImageFodderParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityImageFodderResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.SaveActivityImageFodderResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 图片素材
 * <AUTHOR>
 */
@RestController
@RequestMapping("/activity/imageFodder")
@Slf4j
public class ActivityImageFodderController {

    @Autowired
    private ActivityImageFodderConfigManager activityImageFodderConfigManager;

    @PostMapping("/save")
    public ResultVO<SaveActivityImageFodderResult> save(@RequestBody SaveActivityImageFodderParam param){
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("save activity image fodder, param: {}, appId: {}, operator: {}", param, appId, operator);
        return activityImageFodderConfigManager.save(param, appId, operator);
    }

    @PostMapping("/update")
    public ResultVO<Void> update(@RequestBody UpdateActivityImageFodderParam param){
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("update activity image fodder, param: {}, appId: {}, operator: {}", param, appId, operator);
        return activityImageFodderConfigManager.update(param, appId, operator);
    }

    @PostMapping("/delete")
    public ResultVO<Void> delete(@RequestBody DeleteActivityImageFodderParam param){
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("delete activity image fodder, param: {}, appId: {}, operator: {}", param, appId, operator);
        return activityImageFodderConfigManager.delete(param.getId(), appId, operator);
    }

    @GetMapping("/list")
    public ResultVO<PageVO<ActivityImageFodderResult>> list(PageActivityImageFodderParam param){
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("page activity image fodder, param: {}, appId: {}, operator: {}", param, appId, operator);
        return activityImageFodderConfigManager.list(param, appId);
    }
}
