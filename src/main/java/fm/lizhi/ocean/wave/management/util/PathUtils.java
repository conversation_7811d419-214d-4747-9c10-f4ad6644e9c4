package fm.lizhi.ocean.wave.management.util;

import org.apache.commons.codec.digest.DigestUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.UncheckedIOException;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * 路径工具类
 */
public class PathUtils {

    /**
     * 计算本地文件的sha-256值
     *
     * @param localPath 本地文件路径
     * @return 本地文件的sha-256值
     */
    public static String sha256Hex(Path localPath) {
        try (InputStream inputStream = Files.newInputStream(localPath)) {
            return DigestUtils.sha256Hex(inputStream);
        } catch (IOException e) {
            throw new UncheckedIOException(e.getMessage(), e);
        }
    }

    /**
     * 计算本地文件的字节数
     *
     * @param localPath 本地文件路径
     * @return 本地文件的字节数
     */
    public static long size(Path localPath) {
        try {
            return Files.size(localPath);
        } catch (IOException e) {
            throw new UncheckedIOException(e.getMessage(), e);
        }
    }
}
