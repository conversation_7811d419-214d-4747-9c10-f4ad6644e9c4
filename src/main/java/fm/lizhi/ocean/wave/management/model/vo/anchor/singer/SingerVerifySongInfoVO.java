package fm.lizhi.ocean.wave.management.model.vo.anchor.singer;

import lombok.Data;

/**
 * @description:
 * @author: guoyibin
 * @create: 2025/07/24 10:19
 */
@Data
public class SingerVerifySongInfoVO {

    /**
     * 申请ID
     */
    private Long applyId;

    /**
     * 音频文件地址
     */
    private String audioPath;

    /**
     * 预审核不过原因
     */
    private String preAuditRejectReason;

    /**
     * 歌曲名称
     */
    private String songName;

    /**
     * 歌曲风格
     */
    private String songStyle;

    /**
     * 预审核状态
     */
    private Integer preAuditStatus;
}
