package fm.lizhi.ocean.wave.management.model.converter.activitycenter;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.lamp.common.util.JsonUtils;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityRuleParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityRuleParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityRuleConfigResult;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRuleEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRule;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRule;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityRuleConfigConvert {

    ActivityRuleConfigConvert I = Mappers.getMapper(ActivityRuleConfigConvert.class);

    default RequestSaveActivityRule toRequestSaveActivityRule(SaveActivityRuleParam param, Integer appId, String operator) {
        RequestSaveActivityRule.RequestSaveActivityRuleBuilder builder = RequestSaveActivityRule.builder();
        builder.appId(appId).operator(operator).ruleType(param.getRuleType());

        if (param.getRuleType().equals(ActivityApplyRuleEnum.OFFICIAL_COUNT.getType())) {
            OfficialCountRuleBean rule = new OfficialCountRuleBean();
            rule.setCount(param.getCount());
            builder.ruleJson(JsonUtil.dumps(rule));
        } else if (param.getRuleType().equals(ActivityApplyRuleEnum.REPORTS_COUNT.getType())) {
            ReportCountRuleBean rule = new ReportCountRuleBean();
            rule.setCount(param.getCount());
            rule.setPeriod(param.getPeriod());
            builder.ruleJson(JsonUtil.dumps(rule));
        } else if (param.getRuleType().equals(ActivityApplyRuleEnum.BLACK_LIST.getType())) {
            String userIds = param.getUserIds();
            userIds = userIds == null ? "" : userIds.trim();
            BlackListRuleBean ruleBean = BlackListRuleBean.builder().userIds(userIds).build();
            builder.ruleJson(JsonUtil.dumps(ruleBean));
        } else if (param.getRuleType().equals(ActivityApplyRuleEnum.LAST_WEEK_HALL_INCOME_THRESHOLD.getType())) {
            Long income = param.getIncome();
            income = income == null ? 0L : income;
            LastWeekHallIncomeThresholdRuleBean ruleBean = LastWeekHallIncomeThresholdRuleBean.builder().income(income).build();
            builder.ruleJson(JsonUtil.dumps(ruleBean));
        }
        return builder.build();
    }


    default RequestUpdateActivityRule toRequestUpdateActivityRule(UpdateActivityRuleParam param, Integer appId, String operator) {
        RequestUpdateActivityRule.RequestUpdateActivityRuleBuilder builder = RequestUpdateActivityRule.builder();
        builder.appId(appId).operator(operator).ruleType(param.getRuleType()).id(param.getId());

        if (param.getRuleType().equals(ActivityApplyRuleEnum.OFFICIAL_COUNT.getType())) {
            OfficialCountRuleBean rule = new OfficialCountRuleBean();
            rule.setCount(param.getCount());
            builder.ruleJson(JsonUtil.dumps(rule));
        } else if (param.getRuleType().equals(ActivityApplyRuleEnum.REPORTS_COUNT.getType())) {
            ReportCountRuleBean rule = new ReportCountRuleBean();
            rule.setCount(param.getCount());
            rule.setPeriod(param.getPeriod());
            builder.ruleJson(JsonUtil.dumps(rule));
        } else if (param.getRuleType().equals(ActivityApplyRuleEnum.BLACK_LIST.getType())) {
            String userIds = param.getUserIds();
            userIds = userIds == null ? "" : userIds.trim();
            BlackListRuleBean ruleBean = BlackListRuleBean.builder().userIds(userIds).build();
            builder.ruleJson(JsonUtil.dumps(ruleBean));
        } else if (param.getRuleType().equals(ActivityApplyRuleEnum.LAST_WEEK_HALL_INCOME_THRESHOLD.getType())) {
            Long income = param.getIncome();
            income = income == null ? 0L : income;
            LastWeekHallIncomeThresholdRuleBean ruleBean = LastWeekHallIncomeThresholdRuleBean.builder().income(income).build();
            builder.ruleJson(JsonUtil.dumps(ruleBean));
        }
        return builder.build();

    }

    default ActivityRuleConfigResult toActivityRuleConfigResult(ActivityRuleConfigBean bean) {
        ActivityRuleConfigResult result = new ActivityRuleConfigResult();
        result.setId(bean.getId());
        result.setAppId(bean.getAppId());
        result.setRuleType(bean.getRuleType());
        result.setModifyTime(bean.getModifyTime());
        result.setOperator(bean.getOperator());

        if (bean.getRuleType().equals(ActivityApplyRuleEnum.OFFICIAL_COUNT.getType())) {
            OfficialCountRuleBean rule = (OfficialCountRuleBean) convertRule(bean);
            result.setCount(rule.getCount());
        } else if (bean.getRuleType().equals(ActivityApplyRuleEnum.REPORTS_COUNT.getType())) {
            ReportCountRuleBean rule = (ReportCountRuleBean) convertRule(bean);
            result.setCount(rule.getCount());
            result.setPeriod(rule.getPeriod());
        } else if (bean.getRuleType().equals(ActivityApplyRuleEnum.BLACK_LIST.getType())) {
            BlackListRuleBean rule = (BlackListRuleBean) convertRule(bean);
            result.setUserIds(rule.getUserIds());
        } else if (bean.getRuleType().equals(ActivityApplyRuleEnum.LAST_WEEK_HALL_INCOME_THRESHOLD.getType())) {
            LastWeekHallIncomeThresholdRuleBean rule = (LastWeekHallIncomeThresholdRuleBean) convertRule(bean);
            result.setIncome(rule.getIncome());
        }

        return result;

    }

    default ActivityRuleBaseAbstractBean convertRule(ActivityRuleConfigBean config) {
        Integer ruleType = config.getRuleType();
        String ruleJson = config.getRuleJson();
        ActivityApplyRuleEnum ruleEnum = ActivityApplyRuleEnum.getById(ruleType);
        if (null == ruleEnum || StringUtils.isBlank(ruleJson)) {
            return null;
        }

        return JsonUtils.fromJsonString(ruleJson, ruleEnum.getClazz());
    }


    List<ActivityRuleConfigResult> ActivityRuleConfigResults(List<ActivityRuleConfigBean> target);
}
