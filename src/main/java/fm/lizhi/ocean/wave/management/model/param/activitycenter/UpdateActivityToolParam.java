package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class UpdateActivityToolParam {

    /**
     * 主键
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 工具名称
     */
    private String name;

    /**
     * 类型, 1:玩法; 2: 工具
     */
    private Integer type;

    /**
     * 玩法工具描述
     */
    private String toolDesc;

    /**
     * 状态 0: 下架; 1: 上架
     */
    private Integer status;


}
