package fm.lizhi.ocean.wave.management.model.converter.family.common;

import fm.lizhi.ocean.wave.management.model.converter.CommonConvert;
import fm.lizhi.ocean.wave.management.model.param.family.common.SearchFamilyParam;
import fm.lizhi.ocean.wave.management.model.result.family.common.SearchFamilyResult;
import fm.lizhi.ocean.wave.management.model.vo.family.FamilyUserVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyUserBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.PageSearchFamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.request.RequestPageSearchFamily;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
                SessionExtUtils.class
        },
        uses = {
                CommonConvert.class
        })
public interface FamilyConvert {

    @Mapping(target = "appId", expression = "java(SessionExtUtils.getAppId())")
    RequestPageSearchFamily toRequestPageSearchFamily(SearchFamilyParam param);

    List<SearchFamilyResult> toSearchFamilyResults(List<PageSearchFamilyBean> pageSearchFamilyBeans);

    SearchFamilyResult toSearchFamilyResult(PageSearchFamilyBean pageSearchFamilyBean);

    List<FamilyUserVO> toFamilyUserVOs(List<FamilyUserBean> familyUserBeans);

    @Mapping(target = "name", source = "familyName")
    FamilyUserVO toFamilyUserVO(FamilyUserBean familyUserBean);
}
