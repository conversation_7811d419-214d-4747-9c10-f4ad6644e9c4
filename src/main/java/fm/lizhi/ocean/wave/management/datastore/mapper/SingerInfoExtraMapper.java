package fm.lizhi.ocean.wave.management.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;


/**
 * <AUTHOR>
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface SingerInfoExtraMapper {

    /**
     * 根据应用ID、用户ID、歌手类型修改歌手状态
     */
    @Update("<script>" +
            "UPDATE singer_info " +
            "SET singer_status = #{singerInfo.singerStatus}, " +
            "    elimination_reason = #{singerInfo.eliminationReason}, " +
            "    elimination_time = #{singerInfo.eliminationTime}, " +
            "    operator = #{singerInfo.operator}" +
            "    <if test='singerInfo.auditTime != null'>" +
            "        , audit_time = #{singerInfo.auditTime}" +
            "    </if>" +
            "    <if test='singerInfo.singerVerifyId != null'>" +
            "        , singer_verify_id = #{singerInfo.singerVerifyId}" +
            "    </if>" +
            "    <if test='singerInfo.songStyle != null'>" +
            "        , song_style = #{singerInfo.songStyle}" +
            "    </if>" +
            "    <if test='singerInfo.singerType != null'>" +
            "        , singer_type = #{singerInfo.singerType}" +
            "    </if>" +
            "    <if test='singerInfo.originalSinger != null'>" +
            "        , original_singer = #{singerInfo.originalSinger}" +
            "    </if>" +
            "    <if test='singerInfo.familyId != null'>" +
            "        , family_id = #{singerInfo.familyId}" +
            "    </if>" +
            "    <if test='singerInfo.njId != null'>" +
            "        , nj_id = #{singerInfo.njId}" +
            "    </if>" +
            "    <if test='singerInfo.contactNumber != null'>" +
            "        , contact_number = #{singerInfo.contactNumber}" +
            "    </if>" +
            "WHERE id = #{singerInfo.id} " +
            "AND singer_status = #{currentSingerStatus}" +
            "</script>")
    int updateSingerStatus(@Param("singerInfo") SingerInfo singerInfo,
                           @Param("currentSingerStatus") int currentSingerStatus);

}
