package fm.lizhi.ocean.wave.management.manager;

import com.alibaba.dubbo.rpc.RpcResult;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ActivityApplyConvert;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.*;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.*;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.remote.service.activitycenter.ActivityApplyServiceRemote;
import fm.lizhi.ocean.wave.management.remote.service.activitycenter.ActivityOfficialSeatTimeServiceRemote;
import fm.lizhi.ocean.wave.management.remote.service.user.UserCommonServiceRemote;
import fm.lizhi.ocean.wave.management.remote.service.user.UserFamilyServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestQueryUserActivitiesBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityInfoDetail;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestCancelActivity;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseBatchActivityAuditData;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseCancelActivity;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.PlayerSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.constants.SingStatusEnum;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class ActivityApplyManager {

    @Autowired
    private ActivityApplyServiceRemote activityApplyServiceRemote;
    @Autowired
    private ActivityOfficialSeatTimeServiceRemote activityOfficialSeatTimeServiceRemote;
    @Autowired
    private UserCommonServiceRemote userCommonServiceRemote;
    @Autowired
    private UserFamilyServiceRemote userFamilyServiceRemote;

    /**
     * 驳回申请
     *
     * @param param 参数
     * @return 结果
     */
    public ResultVO<Void> rejectActivityApply(ActivityAuditRejectParam param, String operator) {
        if (param.getActivityId() == null || param.getAppId() == null) {
            return ResultVO.failure("活动ID或者应用ID不能为空");
        }

        RequestActivityAuditReject rejectBean = ActivityApplyConvert.I.buildRejectParamBean(param, operator);
        Result<Void> result = activityApplyServiceRemote.rejectActivityApply(rejectBean);
        if (ResultUtils.isFailure(result)) {
            log.warn("ActivityApplyManager.rejectActivityApply resultCode: {},activityId:{}", result.rCode(), param.getActivityId());
            return ResultVO.failure(result.rCode(), Optional.ofNullable(result.getMessage()).orElse("驳回申请失败"));
        }
        return ResultVO.success();

    }

    public ResultVO<QueryUserActivitiesResult> queryUserActivities(QueryUserActivitiesParamVO queryUserActivitiesParamVO, Integer appId) {
        RequestQueryUserActivitiesBean requestQueryUserActivitiesBean = ActivityApplyConvert.I.userActivitiesParamVO2Bean(queryUserActivitiesParamVO, appId);

        Result<ResponseQueryUserActivitiesBean> responseQueryUserActivitiesBeanResult = activityApplyServiceRemote.queryUserActivities(requestQueryUserActivitiesBean);
        if (ResultUtils.isFailure(responseQueryUserActivitiesBeanResult)) {
            log.warn("queryUserActivities failed. rCode:{}", responseQueryUserActivitiesBeanResult.rCode());
            return ResultVO.failure(responseQueryUserActivitiesBeanResult);
        }

        List<UserActivitySimpleInfoResult> userActivitySimpleInfos = ActivityApplyConvert.I.userActivitySimpleInfoBeanList2Results(responseQueryUserActivitiesBeanResult.target().getList());
        return ResultVO.success(new QueryUserActivitiesResult()
                .setTotal(responseQueryUserActivitiesBeanResult.target().getTotal())
                .setList(userActivitySimpleInfos));

    }

    public ResultVO<UserActivityDetailResult> queryUserActivityDetail(Long activityId, Integer appId) {
        Result<ResponseActivityInfoDetail> responseActivityInfoDetailResult = activityApplyServiceRemote.queryUserActivityDetail(appId, activityId);
        if (ResultUtils.isFailure(responseActivityInfoDetailResult)) {
            log.warn("queryUserActivityDetail failed. activityId:{}, rCode:{}", activityId, responseActivityInfoDetailResult.rCode());
            return ResultVO.failure(responseActivityInfoDetailResult);
        }
        return ResultVO.success(ActivityApplyConvert.I.userActivityDetailResult2Result(responseActivityInfoDetailResult.target()));

    }

    public ResultVO<String> agreeActivityApply(ActivityAuditAgreeParam activityAuditAgreeParam, Integer appId, String operator) {
        RequestActivityAuditAgree requestActivityAuditAgreeBean = ActivityApplyConvert.I.agreeActivityApplyParam2Bean(activityAuditAgreeParam, appId, operator);
        Result<String> agreeResult = activityApplyServiceRemote.agreeActivityApply(requestActivityAuditAgreeBean);
        if (ResultUtils.isFailure(agreeResult)) {
            log.warn("agreeActivityApply failed. activityId:{}, rCode:{}", activityAuditAgreeParam.getActivityId(), agreeResult.rCode());
            return ResultVO.failure(agreeResult.rCode(), Optional.ofNullable(agreeResult.getMessage()).orElse("同意活动申请失败"));
        }
        return ResultVO.success(agreeResult.target());
    }

    /**
     * 获取用户家族信息, 包括用户信息
     *
     * @param param 查询参数
     * @param appId 应用id
     * @return 用户家族信息结果
     */
    public ResultVO<GetUserInFamilyResult> getUserInFamily(GetUserInFamilyParam param, Integer appId) {
        String band = param.getBand();
        Result<UserBean> getUserResult = userCommonServiceRemote.getUserByBand(appId, band);
        if (getUserResult.rCode() == UserCommonService.USER_NOT_FOUND) {
            return ResultVO.failure("用户不存在");
        }
        if (ResultUtils.isFailure(getUserResult)) {
            String message = StringUtils.defaultIfBlank(getUserResult.getMessage(), "查询用户失败");
            return ResultVO.failure(getUserResult.rCode(), message);
        }
        UserBean userBean = getUserResult.target();
        Long userId = userBean.getId();
        Result<UserInFamilyBean> getFamilyResult = userFamilyServiceRemote.getUserInFamily(appId, userId);
        if (ResultUtils.isFailure(getFamilyResult)) {
            String message = StringUtils.defaultIfBlank(getFamilyResult.getMessage(), "查询用户家族失败");
            return ResultVO.failure(getFamilyResult.rCode(), message);
        }
        UserInFamilyBean userInFamilyBean = getFamilyResult.target();
        GetUserInFamilyResult result = ActivityApplyConvert.I.toGetUserInFamilyResult(userBean, userInFamilyBean);
        return ResultVO.success(result);
    }

    /**
     * 查询厅签约成员列表, 底层DC接口是分页接口且仅返回已签约状态的用户. 考虑到厅签约状态的用户量不会太大, 这里做了分页拼接.
     * 后续如果数据量大了则应改为分页接口.
     *
     * @param param 查询参数
     * @param appId 应用id
     * @return 厅签约成员列表结果
     */
    public ResultVO<List<ListRoomPlayerResult>> listRoomPlayer(ListRoomPlayerParam param, Integer appId) {
        Long roomId = param.getRoomId();
        int pageNo = 1;
        int pageSize = 500;
        boolean hasNext = true;
        ArrayList<ListRoomPlayerResult> playerList = new ArrayList<>();
        while (hasNext) {
            Result<PageBean<PlayerSignBean>> result = userCommonServiceRemote.getAllRoomPlayers(appId, roomId, pageNo, pageSize);
            if (ResultUtils.isFailure(result)) {
                String message = StringUtils.defaultIfBlank(result.getMessage(), "查询厅下所有主播失败");
                return ResultVO.failure(result.rCode(), message);
            }
            List<PlayerSignBean> playerSignBeans = result.target().getList();
            for (PlayerSignBean playerSignBean : CollectionUtils.emptyIfNull(playerSignBeans)) {
                if (Objects.equals(playerSignBean.getSignStatus(), SingStatusEnum.SING.getValue())) {
                    playerList.add(ActivityApplyConvert.I.toListRoomPlayerResult(playerSignBean));
                }
            }
            if (CollectionUtils.size(playerSignBeans) < pageSize) {
                hasNext = false;
            }
            pageNo++;
        }
        return ResultVO.success(playerList);
    }

    /**
     * 创建活动提报
     *
     * @param param 活动提报参数
     * @param appId 应用id
     * @return 结果
     */
    public ResultVO<Void> createActivityApply(CreateActivityApplyParam param, Integer appId) {
        RequestActivityApplyBean requestActivityApplyBean = ActivityApplyConvert.I.toRequestActivityApplyBean(param, appId);
        Result<Void> result = activityApplyServiceRemote.activityApply(requestActivityApplyBean);
        if (ResultUtils.isFailure(result)) {
            String message = StringUtils.defaultIfBlank(result.getMessage(), "活动提报失败, 错误码: " + result.rCode());
            return ResultVO.failure(result.rCode(), message);
        }
        return ResultVO.success();
    }

    /**
     * 分页查询活动日历
     *
     * @param param 查询参数
     * @param appId 应用id
     * @return 结果
     */
    public ResultVO<PageVO<PageActivityCalendarResult>> pageActivityCalendar(PageActivityCalendarParam param, Integer appId) {
        RequestQueryActivityListBean req = ActivityApplyConvert.I.toRequestQueryActivityListBean(param, appId);
        Result<ResponseQueryActivityListBean> result = activityApplyServiceRemote.queryActivityList(req);
        if (ResultUtils.isFailure(result)) {
            String message = StringUtils.defaultIfBlank(result.getMessage(), "查询活动列表失败, 错误码: " + result.rCode());
            return ResultVO.failure(result.rCode(), message);
        }
        ResponseQueryActivityListBean resp = result.target();
        PageVO<PageActivityCalendarResult> pageVo = ActivityApplyConvert.I.toPageActivityCalendarResultPageVO(resp);
        return ResultVO.success(pageVo);
    }

    /**
     * 查询官频位列表
     *
     * @param param 查询参数
     * @param appId 应用id
     * @return 官频位列表
     */
    public ResultVO<ListOfficialSeatResult> listOfficialSeat(ListOfficialSeatParam param, Integer appId) {
        RequestGetOfficialSeatTimeBean req = ActivityApplyConvert.I.toRequestGetOfficialSeatTimeBean(param, appId);
        Result<ResponseGetOfficialSeatTimeBean> result = activityOfficialSeatTimeServiceRemote.getOfficialSeatTimeList(req);
        if (ResultUtils.isFailure(result)) {
            String message = StringUtils.defaultIfBlank(result.getMessage(), "查询官方座位时间失败, 错误码: " + result.rCode());
            return ResultVO.failure(result.rCode(), message);
        }
        ResponseGetOfficialSeatTimeBean resp = result.target();
        ListOfficialSeatResult listOfficialSeatResult = ActivityApplyConvert.I.toListOfficialSeatResult(resp);
        return ResultVO.success(listOfficialSeatResult);
    }

    public ResultVO<CancelActivityResult> cancelActivityApply(CancelActivityParam param, Integer appId, String operator) {
        RequestCancelActivity requestActivityCancelBean = ActivityApplyConvert.I.toRequestActivityCancelBean(param, appId, operator);
        Result<ResponseCancelActivity> result = activityApplyServiceRemote.cancelActivity(requestActivityCancelBean);
        if (ResultUtils.isFailure(result)) {
            String message = StringUtils.defaultIfBlank(result.getMessage(), "取消活动失败, 错误码: " + result.rCode());
            return ResultVO.failure(result.rCode(), message);
        }

        CancelActivityResult cancelActivityResult = new CancelActivityResult().setMessage(result.target().getErrorMsg());
        return ResultVO.success(cancelActivityResult);
    }

    public ResultVO<ModifyActivityApplyResult> modifyActivityApply(ModifyActivityApplyParam param, Integer appId) {
        RequestActivityModifyBean requestModifyActivityApplyBean = ActivityApplyConvert.I.toRequestActivityModifyBean(param, appId);
        Result<ResponseActivityModifyBean> result = activityApplyServiceRemote.modifyActivityApply(requestModifyActivityApplyBean);
        if (ResultUtils.isFailure(result)) {
            String message = StringUtils.defaultIfBlank(result.getMessage(), "修改活动失败, 错误码: " + result.rCode());
            return ResultVO.failure(result.rCode(), message);
        }
        ResponseActivityModifyBean resp = result.target();
        ModifyActivityApplyResult modifyActivityApplyResult = new ModifyActivityApplyResult().setMessage(resp.getErrorMsg());
        return ResultVO.success(modifyActivityApplyResult);
    }

    /**
     * 批量同意活动申请
     *
     * @param param 批量同意参数
     * @param appId 应用ID
     * @param operator 操作人
     * @return 结果
     */
    public ResultVO<BatchActivityAuditResult> batchAgreeActivityApply(BatchActivityAuditAgreeParam param, Integer appId, String operator) {
        if (param == null || CollectionUtils.isEmpty(param.getActivity())) {
            return ResultVO.failure("活动列表不能为空");
        }

        RequestBatchActivityAuditAgree requests = ActivityApplyConvert.I.batchAgreeActivityApplyParam2Beans(param, appId, operator);
        Result<ResponseBatchActivityAuditData> result = activityApplyServiceRemote.batchAgreeActivityApply(requests);

        if (ResultUtils.isFailure(result)) {
            log.warn("ActivityApplyManager.batchAgreeActivityApply failed, rCode: {}", result.rCode());
            return ResultVO.failure(result.rCode(), Optional.ofNullable(result.getMessage()).orElse("批量同意活动申请失败"));
        }

        return ResultVO.success(ActivityApplyConvert.I.toBatchActivityAuditResult(result.target()));
    }

    /**
     * 批量拒绝活动申请
     *
     * @param param 批量拒绝参数
     * @param appId 应用ID
     * @param operator 操作人
     * @return 结果
     */
    public ResultVO<BatchActivityAuditResult> batchRejectActivityApply(BatchActivityAuditRejectParam param, Integer appId, String operator) {
        if (param == null || CollectionUtils.isEmpty(param.getActivity())) {
            return ResultVO.failure("活动列表不能为空");
        }

        RequestBatchActivityAuditReject requests = ActivityApplyConvert.I.batchRejectActivityApplyParam2Beans(param, appId, operator);
        Result<ResponseBatchActivityAuditData> result = activityApplyServiceRemote.batchRejectActivityApply(requests);

        if (ResultUtils.isFailure(result)) {
            log.warn("ActivityApplyManager.batchRejectActivityApply failed, rCode: {}", result.rCode());
            return ResultVO.failure(result.rCode(), Optional.ofNullable(result.getMessage()).orElse("批量拒绝活动申请失败"));
        }

        BatchActivityAuditResult batchResult = ActivityApplyConvert.I.toBatchActivityAuditResult(result.target());
        return ResultVO.success(batchResult);
    }
}
