package fm.lizhi.ocean.wave.management.datastore.dao.singer;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerSingHallApplyRecord;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerSingHallApplyRecordExample;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerSingHallApplyRecordMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class SingerHallApplyDao {

    @Resource
    private SingerSingHallApplyRecordMapper singerSingHallApplyRecordMapper;

    /**
     * 批量查询点唱厅申请记录
     * @param appId 应用ID
     * @param njIds 厅主ID列表
     * @return 点唱厅申请记录列表
     */
    public List<SingerSingHallApplyRecord> batchGetSingerHallApplyRecordList(int appId, List<Long> njIds, List<Integer> auditStatusList) {
        SingerSingHallApplyRecordExample example = new SingerSingHallApplyRecordExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andNjIdIn(njIds)
                .andDeletedEqualTo(Boolean.FALSE)
                .andAuditStatusIn(auditStatusList)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        return singerSingHallApplyRecordMapper.selectByExample(example);
    }
}
