package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class CancelActivityParam {

     /**
     * 活动ID
     */
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    /**
     * 取消原因
     */
    private String reason;

    /**
     * 版本号
     */
    @NotNull(message = "版本号不能为空")
    private Integer version;

}
