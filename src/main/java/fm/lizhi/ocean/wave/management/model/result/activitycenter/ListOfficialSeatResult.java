package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import lombok.Data;

import java.util.List;

/**
 * 官方座位列表查询结果
 */
@Data
public class ListOfficialSeatResult {

    /**
     * 官频位最大数量，用于前端计算每个时间段剩余数量
     */
    private Integer maxOfficialSeatHallCount;

    /**
     * 官频位时间表
     */
    private List<Date> timeList;

    @Data
    public static class Date {

        /**
         * 日期, 毫秒时间戳, 某一天的0时0分0秒
         */
        private Long date;

        /**
         * 官频位时段列表
         */
        private List<Period> list;
    }

    /**
     * 官频位时段
     */
    @Data
    public static class Period {

        /**
         * 座位
         */
        private Integer seat;

        /**
         * 官频位展示开始时间, 毫秒时间戳
         */
        private Long startTime;

        /**
         * 官频位展示结束时间, 毫秒时间戳
         */
        private Long endTime;

        /**
         * 档期官频位厅已使用数
         */
        private Integer count;
    }
}
