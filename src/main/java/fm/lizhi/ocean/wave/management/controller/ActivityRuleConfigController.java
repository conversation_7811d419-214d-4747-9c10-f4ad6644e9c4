package fm.lizhi.ocean.wave.management.controller;

import fm.lizhi.ocean.wave.management.manager.ActivityRuleConfigManager;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.DeleteActivityRuleParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityRuleParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityRuleParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityRuleConfigResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/activity/rule")
@Slf4j
public class ActivityRuleConfigController {

    @Autowired
    private ActivityRuleConfigManager activityRuleConfigManager;

    /**
     * 保存提报规则
     */
    @PostMapping("/save")
    public ResultVO<Void> save(@RequestBody SaveActivityRuleParam param){

        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("save activity rule config, param={}, appId={}, operator={}", param, appId, operator);
        return activityRuleConfigManager.save(param, appId, operator);
    }

    /**
     * 更新提报规则
     */
    @PostMapping("/update")
    public ResultVO<Void> update(@RequestBody UpdateActivityRuleParam param){
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("update activity rule config, param={}, appId={}, operator={}", param, appId, operator);
        return activityRuleConfigManager.update(param, appId, operator);
    }

    /**
     * 删除提报规则
     */
    @PostMapping("/delete")
    public ResultVO<Void> delete(@RequestBody DeleteActivityRuleParam param){
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("delete activity rule config,id:{}, appId={}, operator={}", param.getId(), appId, operator);
        return activityRuleConfigManager.delete(param.getId(), appId, operator);
    }

    /**
     * 获取提报规则
     */
    @GetMapping("/list")
    public ResultVO<List<ActivityRuleConfigResult>> list(){
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("list activity rule config,appId={}, operator={}", appId, operator);

        return activityRuleConfigManager.list(appId);
    }

}
