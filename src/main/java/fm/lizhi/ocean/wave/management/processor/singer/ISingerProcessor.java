package fm.lizhi.ocean.wave.management.processor.singer;

import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ISingerProcessor extends BusinessEnvAwareProcessor {

    /**
     * 获取需要关联淘汰的歌手库 ID
     *
     * @param userIds    用户ID列表
     * @param appId      应用ID
     * @param singerType 歌手类型
     * @return 需要关联淘汰的歌手库ID列表
     */
    List<Long> getRelatedEliminateSingerRecordIds(List<Long> userIds, int appId, int singerType);

    /**
     * 关联淘汰后装扮补发操作
     *
     * @param appId               应用ID
     * @param singerIds           歌手ID
     * @param eliminateSingerType 待淘汰的歌手类型
     * @param operator            操作人
     */
    void reissueDecorateAfterEliminate(int appId, List<Long> singerIds, int eliminateSingerType, String operator);

    /**
     * 判断移除白名单后是否需要移除歌手
     *
     * @param appId      应用ID
     * @param njId       厅主ID
     * @return 结果
     */
    boolean isNeedRemoveSingerAfterRemoveWhiteList(int appId, Long njId);

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return ISingerProcessor.class;
    }

}
