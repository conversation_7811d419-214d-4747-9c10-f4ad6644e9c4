package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.gift.GetGiftsResult;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.DecorateBean;
import fm.lizhi.ocean.wavecenter.api.gift.bean.GetGiftsBean;
import lombok.Data;

import java.util.List;

/**
 * @description: 用户提报活动详情
 * @author: guoyibin
 * @create: 2024/10/23 21:06
 */
@Data
public class UserActivityDetailResult {

    /**
     * 活动开始时间
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long startTime;

    /**
     * 活动结束时间
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long endTime;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 联系方式
     */
    private String contactNumber;

    /**
     * 活动ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 活动名称
     */
    private String name;


    /**
     * 活动大类ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bigClassId;

    /**
     * 活动大类名称
     */
    private String bigClassName;

    /**
     * 活动分类ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    /**
     * 活动分类名称
     */
    private String className;

    /**
     * 活动目标
     */
    private String goal;

    /**
     * 活动简介
     */
    private String introduction;

    /**
     * 房间公告
     */
    private String roomAnnouncement;


    /**
     * 活动海报
     */
    private String cover;

    /**
     * 活动审核状态
     */
    private Integer auditStatus;


    /**
     * 活动厅主信息
     */
    private ActivityNjInfoResult njInfo;


    /**
     * 活动主持信息
     */
    private ActivityUserInfoResult hostInfo;

    /**
     * 陪档主播信息
     */
    private List<ActivityUserInfoResult> accompanyNjs;

    /**
     * 活动环节列表
     */
    private List<ActivityProcessResult> processes;

    /**
     * 活动道具图片列表
     */
    private List<String> auxiliaryPropUrls;

    /**
     * 房间公告图片列表
     */
    private List<String> roomAnnouncementImages;

    /**
     * 玩法工具列表
     */
    private List<ActivityToolInfoResult> activityTools;

    /**
     * 房间背景信息
     */
    @Deprecated
    private BackGroundInfoResult roomBackgroundInfo;

    /**
     * 房间背景信息列表
     */
    private List<BackGroundInfoResult> roomBackgroundInfos;

    /**
     * 头像框信息
     */
    @Deprecated
    private BackGroundInfoResult avatarWidgetInfo;

    /**
     * 头像框信息列表
     */
    private List<BackGroundInfoResult> avatarWidgetInfos;

    /**
     * 流量资源列表
     */
    private List<FlowResourceResult> flowResources;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 模板ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long templateId;


    /**
     * 申请者uid
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long applicantUid;

    /**
     * 活动状态
     */
    private Integer activityStatus;

    /**
     * 申请类型，1：自主提报，2: 官方活动
     */
    private int applyType;


    /**
     * 礼物信息列表
     */
    private List<GetGiftsResult> giftInfos;


    /**
     * 角标
     */
    private RoomMarkInfoResult roomMarkInfo;

    /**
     * 房间角标URl
     */
    private String roomMarkUrl;
}
