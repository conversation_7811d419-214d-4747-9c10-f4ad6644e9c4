package fm.lizhi.ocean.wave.management.kafka.utility.webp.consumer;

import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.ocean.wave.management.kafka.utility.webp.producer.WebpAsyncConvertResultProducer;
import fm.lizhi.ocean.wave.management.manager.utility.webp.WebpConvertManager;
import fm.lizhi.ocean.wave.management.manager.utility.webp.WebpNotifyManager;
import fm.lizhi.ocean.wave.management.model.converter.utility.webp.WebpConvertConvert;
import fm.lizhi.ocean.wave.management.model.param.utility.webp.WebpAsyncConvertParam;
import fm.lizhi.ocean.wave.management.model.param.utility.webp.WebpConvertParam;
import fm.lizhi.ocean.wave.management.model.result.utility.webp.WebpAsyncConvertResult;
import fm.lizhi.ocean.wave.management.model.result.utility.webp.WebpConvertResult;
import fm.lizhi.ocean.wave.management.util.JsonUtils;
import fm.lizhi.ocean.wave.management.util.KafkaUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * webp异步转换请求消费者. 由业务方提交转换请求, 创作者执行转换.
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "public-kafka250-bootstrap-server")
public class WebpAsyncConvertRequestConsumer {

    @Autowired
    private WebpConvertConvert webpConvertConvert;

    @Autowired
    private WebpConvertManager webpConvertManager;

    @Autowired
    private WebpNotifyManager webpNotifyManager;

    @Autowired
    private WebpAsyncConvertResultProducer webpAsyncConvertResultProducer;

    /**
     * 处理webp异步转换请求
     *
     * @param body 请求体
     */
    @KafkaHandler(topic = "ocean_wave_topic_webp_convert_request",
            group = "web_ocean_wave_management_group_webp_convert_request")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleRequest(String body) {
        WebpAsyncConvertParam asyncConvertParam;
        try {
            String pureBody = KafkaUtils.removeBodyRoutingInfo(body);
            asyncConvertParam = JsonUtils.readValue(pureBody, WebpAsyncConvertParam.class);
        } catch (RuntimeException e) {
            log.warn("Could not parse WebpAsyncConvertParam from body: {}", body, e);
            return;
        }
        log.info("Received webp convert kafka request, param={}", asyncConvertParam);
        WebpConvertParam param = webpConvertConvert.toKafkaRequestConvertParam(asyncConvertParam);
        WebpConvertResult convertResult = webpConvertManager.webpConvert(param);
        log.info("Finished webp convert, convertResult={}", convertResult);
        WebpAsyncConvertResult result = webpConvertConvert.toWebpAsyncConvertResult(convertResult);
        webpAsyncConvertResultProducer.sendResult(result);
        if (result.isFailure()) {
            webpNotifyManager.sendConvertFailure(result);
        }
    }
}
