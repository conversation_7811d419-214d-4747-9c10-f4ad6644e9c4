package fm.lizhi.ocean.wave.management.manager.singer.handler;

import fm.lizhi.ocean.wave.management.model.dto.singer.SingerAuditParamDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerExecuteAuditDTO;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;

/**
 * 歌手认证预审核过滤器
 * <AUTHOR>
 */
public interface SingerAuditStatusHandler {

    /**
     * 执行审核操作
     *
     * @param param 审核参数
     * @param verifyRecord 认证记录
     */
    SingerExecuteAuditDTO executeAudit(SingerAuditParamDTO param, SingerVerifyRecord verifyRecord);

    /**
     * 审核状态枚举
     *
     * @return 审核状态枚举
     */
    SingerAuditStatusEnum getAuditStatusEnum();
}
