package fm.lizhi.ocean.wave.management.config.apollo;

import lombok.Data;

import java.time.Duration;

/**
 * webp相关配置
 */
@Data
public class WebpProperties {

    /**
     * 是否启用转换缓存, 如果开启, 对于相同源文件相同转换质量的请求, 会优先返回已有的转换结果
     */
    private boolean convertCacheEnabled = true;

    /**
     * webp转换服务地址
     */
    private String nodeServerUrl = "http://effectpack.yfxn.lizhi.fm/webp/convert";

    /**
     * restTemplate配置
     */
    private RestTemplate restTemplate = new RestTemplate();

    /**
     * 飞书通知地址, 如果为空则不发送通知
     */
    private String feiShuWebhook;

    /**
     * restTemplate配置
     */
    @Data
    public static class RestTemplate {

        /**
         * 最大连接数
         */
        private int maxRequests = 100;

        /**
         * 最大每个主机连接数
         */
        private int maxRequestsPerHost = 10;

        /**
         * 连接超时时间
         */
        private Duration connectTimeout = Duration.ofSeconds(10);

        /**
         * 读取超时时间
         */
        private Duration readTimeout = Duration.ofSeconds(180);

        /**
         * 写入超时时间
         */
        private Duration writeTimeout = Duration.ofSeconds(60);
    }
}
