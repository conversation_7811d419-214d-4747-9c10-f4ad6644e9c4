package fm.lizhi.ocean.wave.management.model.result.anchor.singer;

import java.util.List;

import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerVerifyAuditLosingVO;
import lombok.Data;
import lombok.experimental.Accessors;
/**
 * 歌手认证审核结果
 */
@Data
@Accessors(chain = true)
public class SingerVerifyAuditResult {

    /**
     * 失败的ID
     */
    private List<SingerVerifyAuditLosingVO> losingList;
}
