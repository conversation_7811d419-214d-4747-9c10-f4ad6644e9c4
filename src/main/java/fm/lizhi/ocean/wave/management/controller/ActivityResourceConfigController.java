package fm.lizhi.ocean.wave.management.controller;

import fm.lizhi.ocean.wave.management.manager.ActivityResourceConfigManager;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.DeleteActivityResourceParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.PageActivityResourcesParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityResourceParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityResourceParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.GetActivityResourceResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.PageActivityResourceResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/activity/flowResource")
@Slf4j
public class ActivityResourceConfigController {

    @Autowired
    private ActivityResourceConfigManager activityResourceConfigManager;


    /**
     * 保存流量资源
     */
    @PostMapping("/save")
    public ResultVO<Void> save(@Validated @RequestBody SaveActivityResourceParam param){
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("save activity resource config, param={}, appId={}, operator={}", param, appId, operator);
        return activityResourceConfigManager.save(param, appId, operator);
    }

    /**
     * 更新流量资源
     */
    @PostMapping("/update")
    public ResultVO<Void> update(@Validated @RequestBody UpdateActivityResourceParam param){
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("update activity resource config, param={}, appId={}, operator={}", param, appId, operator);
        return activityResourceConfigManager.update(param, appId, operator);
    }

    /**
     * 删除流量资源
     */
    @PostMapping("/delete")
    public ResultVO<Void> delete(@RequestBody DeleteActivityResourceParam param){
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("delete activity resource config, param={}, appId={}, operator={}", param, appId, operator);
        return activityResourceConfigManager.delete(param.getId(), appId, operator);
    }

    /**
     * 分页获取列表
     */
    @GetMapping("/list")
    public ResultVO<PageVO<PageActivityResourceResult>> list(PageActivityResourcesParam param){
        Integer appId = SessionExtUtils.getAppId();

        log.info("list activity resource config, appId={}", appId);
        return activityResourceConfigManager.list(appId, param);
    }

    /**
     * 根据等级 ID 获取资源列表
     */
    @GetMapping("/listByLevelId")
    public ResultVO<List<GetActivityResourceResult>> listByLevelId(@RequestParam("levelId") Long levelId){

        Integer appId = SessionExtUtils.getAppId();
        log.info("list activity resource config, appId={}, levelId={}", appId, levelId);
        return activityResourceConfigManager.listByLevelId(levelId, appId);
    }

}
