package fm.lizhi.ocean.wave.management.datastore.mapper.family.offlinezone;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wave.management.model.dto.family.offlinezone.ListPlayerDataDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.ListPlayerDataParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListPlayerDataResult;

/**
 * 线下主播数据扩展映射器
 * <AUTHOR>
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface PlayerDataExtMapper {

    /**
     * 分页查询线下主播数据列表（带保护状态）
     * @param param 查询参数
     * @return 主播数据列表
     */
    @Select("<script>" +
            "SELECT " +
            "    p.*, " +
            "    (SELECT id FROM offline_zone_protection prot WHERE prot.player_id = p.user_id ORDER BY prot.create_time DESC LIMIT 1) AS protection_id " +
            "FROM offline_zone_data_player_week p " +
            "WHERE p.app_id = #{param.appId} " +
            "<if test='param.startWeekDate != null and param.startWeekDate != \"\"'>" +
            "    AND p.start_week_date &gt;= #{param.startWeekDate} " +
            "</if>" +
            "<if test='param.endWeekDate != null and param.endWeekDate != \"\"'>" +
            "    AND p.end_week_date &lt;= #{param.endWeekDate} " +
            "</if>" +
            "<if test='param.idName != null and param.idName != \"\"'>" +
            "    AND p.id_name LIKE CONCAT('%', #{param.idName}, '%') " +
            "</if>" +
            "<if test='param.familyName != null and param.familyName != \"\"'>" +
            "    AND p.family_name LIKE CONCAT('%', #{param.familyName}, '%') " +
            "</if>" +
            "<if test='param.playerId != null and param.playerId != \"\"'>" +
            "    AND p.user_id = #{param.playerId} " +
            "</if>" +
            "<if test='param.idCardNumber != null and param.idCardNumber != \"\"'>" +
            "    AND p.id_card_number = #{param.idCardNumber} " +
            "</if>" +
            "<if test='param.njId != null and param.njId != \"\"'>" +
            "    AND p.nj_id = #{param.njId} " +
            "</if>" +
            "<if test='param.familyId != null and param.familyId != \"\"'>" +
            "    AND p.family_id = #{param.familyId} " +
            "</if>" +
            "<if test='param.category != null'>" +
            "    AND p.category = #{param.category} " +
            "</if>" +
            "<if test='param.protection != null'>" +
                 // 上传协议
            "    <if test='param.protection == true'>" +
            "        AND EXISTS (SELECT id FROM offline_zone_protection prot " +
            "               WHERE prot.player_id = p.user_id and prot.player_agree = 1 and prot.archived = 1 " +
            "               ORDER BY prot.create_time DESC LIMIT 1) " +
            "    </if>" +
            "    <if test='param.protection == false'>" +
            "         AND NOT EXISTS (SELECT id FROM offline_zone_protection prot " +
            "               WHERE prot.player_id = p.user_id and prot.player_agree = 1 and prot.archived = 1 " +
            "               ORDER BY prot.create_time DESC LIMIT 1) " +
            "    </if>" +
            "</if>" +
            "ORDER BY p.create_time DESC, p.id DESC" +
            "</script>")
    PageList<ListPlayerDataDTO> listPlayerDataWithProtection(@Param("param") ListPlayerDataParam param,
                                                             @Param(ParamContants.PAGE_NUMBER) int pageNo,
                                                             @Param(ParamContants.PAGE_SIZE) int pageSize);

}