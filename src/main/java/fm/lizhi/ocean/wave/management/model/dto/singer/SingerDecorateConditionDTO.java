package fm.lizhi.ocean.wave.management.model.dto.singer;

import lombok.Data;

import java.util.List;

/**
 * 歌手装扮发放规则条件
 */
@Data
public class SingerDecorateConditionDTO {
    /**
     * 条件id
     */
    private Long id;

    /**
     * 1:曲风，2：原创
     */
    private Integer conditionType;

    /**
     * 1:固定曲风，2：任一曲风，3：全能曲风
     */
    private Integer songStyleType;

    /**
     * 是否原创歌手
     */
    private boolean originalSinger;

    /**
     * 曲风
     */
    private List<String> songStyleList;
}
