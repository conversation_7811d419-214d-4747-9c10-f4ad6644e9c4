package fm.lizhi.ocean.wave.management.model.converter.family.offlinezone;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.management.config.apollo.CommonConfig;
import fm.lizhi.ocean.wave.management.model.converter.CommonConvert;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.AddLearningClassParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.SaveLearningClassParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.UpdateLearningClassParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.AddLearningClassResult;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListLearningClassResult;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wave.management.util.UrlUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLearningClass;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLearningClassWhiteList;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneLearningClassTypeEnum;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneLearningClassWhiteTypeEnum;
import fm.lizhi.sso.client.SessionUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListValuedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 线下专区学习课堂转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
                ConfigUtils.class,
                Date.class,
                OfflineZoneLearningClassWhiteTypeEnum.class,
                SessionUtils.class,
                SessionExtUtils.class,
                UrlUtils.class,
        },
        uses = {
                CommonConvert.class,
        }
)
public abstract class LearningClassConvert {

    @Autowired
    protected CommonConfig commonConfig;

    /**
     * 转换为新增的学习课堂实体
     *
     * @param param 新增参数
     * @return 学习课堂实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "appId", expression = "java(SessionExtUtils.getAppId())")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "fileUrl", source = "param", qualifiedByName = "removeFileUrlHostIfRequired")
    @Mapping(target = "fileCover", expression = "java(UrlUtils.removeHostOrEmpty(param.getFileCover()))")
    @Mapping(target = "label", source = "param.label", defaultValue = "")
    @Mapping(target = "labelColor", source = "param.labelColor", defaultValue = "")
    @Mapping(target = "deleted", constant = "false")
    @Mapping(target = "operator", expression = "java(SessionUtils.getAccount())")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    public abstract OfflineZoneLearningClass toCreateClassEntity(AddLearningClassParam param);

    @Named("removeFileUrlHostIfRequired")
    protected String removeFileUrlHostIfRequired(SaveLearningClassParam param) {
        String fileUrl = param.getFileUrl();
        Integer type = param.getType();
        OfflineZoneLearningClassTypeEnum typeEnum = OfflineZoneLearningClassTypeEnum.fromValue(type);
        Validate.notNull(typeEnum);
        return typeEnum.isRomeFs() ? UrlUtils.removeHostOrEmpty(fileUrl) : StringUtils.defaultString(fileUrl);
    }

    /**
     * 转换为新增的白名单实体列表
     *
     * @param param      保存参数
     * @param learningId 学习课堂ID
     * @return 白名单实体列表
     */
    public List<OfflineZoneLearningClassWhiteList> toCreateWhiteListEntities(SaveLearningClassParam param, Long learningId) {
        if (CollectionUtils.isEmpty(param.getWhiteIds())) {
            return Collections.emptyList();
        }
        ArrayList<OfflineZoneLearningClassWhiteList> entities = new ArrayList<>();
        for (Long whiteId : param.getWhiteIds()) {
            entities.add(toCreateWhiteListEntity(learningId, whiteId));
        }
        return entities;
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "type", expression = "java(OfflineZoneLearningClassWhiteTypeEnum.FAMILY.getValue())")
    protected abstract OfflineZoneLearningClassWhiteList toCreateWhiteListEntity(Long learningId, Long whiteId);

    /**
     * 转换为新增结果
     *
     * @param id 新增的学习课堂ID
     * @return 新增结果
     */
    @Mapping(target = "id", source = "id")
    public abstract AddLearningClassResult toAddLearningClassResult(Long id);

    /**
     * 转换为更新的学习课堂实体
     *
     * @param param 更新参数
     * @return 学习课堂实体
     */
    @Mapping(target = "appId", ignore = true)
    @Mapping(target = "deployEnv", ignore = true)
    @Mapping(target = "fileUrl", source = "param", qualifiedByName = "removeFileUrlHostIfRequired")
    @Mapping(target = "fileCover", expression = "java(UrlUtils.removeHostOrEmpty(param.getFileCover()))")
    @Mapping(target = "label", source = "param.label", defaultValue = "")
    @Mapping(target = "labelColor", source = "param.labelColor", defaultValue = "")
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "operator", expression = "java(SessionUtils.getAccount())")
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    public abstract OfflineZoneLearningClass toUpdateClassEntity(UpdateLearningClassParam param);

    /**
     * 转换为学习课堂列表结果
     *
     * @param classEntities        学习课堂实体列表
     * @param classWhiteListIdsMap 学习课堂白名单ID映射
     * @return 学习课堂结果列表
     */
    public abstract List<ListLearningClassResult> toListLearningClassResults(List<OfflineZoneLearningClass> classEntities, @Context ListValuedMap<Long, Long> classWhiteListIdsMap);

    @Mapping(target = "fileUrl", source = "classEntity", qualifiedByName = "addFileUrlHostIfRequired")
    @Mapping(target = "fileCover", source = "classEntity.fileCover", qualifiedByName = "addWaveCdnHost")
    @Mapping(target = "whiteIds", source = "classEntity.id", qualifiedByName = "getWhiteListIds")
    protected abstract ListLearningClassResult toListLearningClassResult(OfflineZoneLearningClass classEntity, @Context ListValuedMap<Long, Long> classWhiteListIdsMap);

    @Named("addFileUrlHostIfRequired")
    protected String addFileUrlHostIfRequired(OfflineZoneLearningClass classEntity) {
        Integer type = classEntity.getType();
        String fileUrl = classEntity.getFileUrl();
        OfflineZoneLearningClassTypeEnum typeEnum = OfflineZoneLearningClassTypeEnum.fromValue(type);
        Validate.notNull(typeEnum);
        return typeEnum.isRomeFs() ? UrlUtils.addHostOrEmpty(fileUrl, commonConfig.getWaveCdn().getCdnHost()) : fileUrl;
    }

    @Named("addWaveCdnHost")
    protected String addWaveCdnHost(String path) {
        return UrlUtils.addHostOrEmpty(path, commonConfig.getWaveCdn().getCdnHost());
    }

    @Named("getWhiteListIds")
    protected List<Long> getWhiteListIds(Long learningId, @Context ListValuedMap<Long, Long> classWhiteListIdsMap) {
        if (learningId == null || classWhiteListIdsMap == null) {
            return Collections.emptyList();
        }
        return classWhiteListIdsMap.get(learningId);
    }
}
