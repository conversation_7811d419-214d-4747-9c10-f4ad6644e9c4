package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wave.management.common.serializer.ListLongToListStringSerializer;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityTemplateStatusEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 获取活动模板响应
 */
@Data
public class GetActivityTemplateResult {

    /**
     * 活动模板id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 大类id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bigClassId;

    /**
     * 大类名称
     */
    private String bigClassName;

    /**
     * 大类类型
     */
    private Integer bigClassType;

    /**
     * 分类id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    /**
     * 分类名称
     */
    private String className;

    /**
     * 活动目标
     */
    private String goal;

    /**
     * 活动介绍
     */
    private String introduction;

    /**
     * 活动流程列表
     */
    private List<Process> processes;

    /**
     * 辅助道具图片列表
     */
    private List<String> auxiliaryPropUrls;

    /**
     * 活动海报
     */
    private String posterUrl;

    /**
     * 玩法工具列表
     */
    private List<Integer> activityTools;

    /**
     * 房间公告
     */
    private String roomAnnouncement;

    /**
     * 房间公告图片列表
     */
    private List<String> roomAnnouncementImages;

    /**
     * 房间背景id列表
     */
    @JsonSerialize(using = ListLongToListStringSerializer.class)
    private List<Long> roomBackgroundIds;

    /**
     * 礼物列表ID列表
     */
    @JsonSerialize(using = ListLongToListStringSerializer.class)
    private List<Long> giftIds;

    /**
     * 房间背景可选数量限制
     */
    private Integer roomBackgroundLimit;

    /**
     * 房间头像框id列表
     */
    @JsonSerialize(using = ListLongToListStringSerializer.class)
    private List<Long> avatarWidgetIds;

    /**
     * 头像框可选数量限制
     */
    private Integer avatarWidgetLimit;

    /**
     * 流量资源列表
     */
    private List<FlowResource> flowResources;

    /**
     * 是否已删除
     */
    private Boolean deleted;

    /**
     * 封面地址
     */
    private String cover;

    /**
     * 上下架状态
     *
     * @see ActivityTemplateStatusEnum
     */
    private Integer status;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 是否热门推荐
     */
    private Boolean hotRec;

    /**
     * 热门推荐权重
     */
    private Integer hotWeight;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 活动限时
     */
    private Integer activityDurationLimit;

    /**
     * 活动开始时间限制
     */
    private Long activityStartTimeLimit;

    /**
     * 活动结束时间限制
     */
    private Long activityEndTimeLimit;


    /**
     * 房间角标ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long roomMarkId;

    /**
     * 房间角标URL,xm的角标只是一张图片配置，所以没有 ID
     */
    private String roomMarkUrl;

    /**
     * 日程重复类型
     * @see fm.lizhi.ocean.wavecenter.module.api.background.activitycenter.constants.ActivityTemplateRecurrenceEnum
     */
    private Integer recurrenceType;

    /**
     * 当天+选中的小时+选中的分钟 时间戳，服务端提取小时+分钟
     */
    private Long recurrenceStartTime;

    /**
     * 当天+选中的小时+选中的分钟 时间戳，服务端提取小时+分钟
     */
    private Long recurrenceEndTime;

    /**
     * 活动模板流程
     */
    @Data
    public static class Process {

        /**
         * 环节名称
         */
        private String name;

        /**
         * 时长
         */
        private String duration;

        /**
         * 说明
         */
        private String explanation;
    }

    /**
     * 活动模板流量资源图片扩展信息
     */
    @Data
    public static class FlowResourceImageExtra {

        /**
         * 颜色
         */
        private String color;

        /**
         * 宽高比
         */
        private String scale;
    }

    /**
     * 活动模板流量资源图片
     */
    @Data
    public static class FlowResourceImage {

        /**
         * 图片url
         */
        private String imageUrl;

        /**
         * 图片扩展配置
         */
        private FlowResourceImageExtra extra;
    }

    /**
     * 活动模板流量资源扩展信息
     */
    @Data
    public static class FlowResourceExtra {

        /**
         * 官频位时长限制, 单位分钟, 当资源类型为官频位时有值
         */
        @Deprecated
        private Integer durationLimit;

        /**
         * 官频位可选座位号列表, 当资源类型为官频位时有值
         */
        private List<Integer> officialSeatNumbers;

        /**
         * 挂件id, 当资源类型为挂件时有值
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long pendantId;
    }

    /**
     * 活动模板流量资源
     */
    @Data
    public static class FlowResource {

        /**
         * 资源配置id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long resourceConfigId;

        /**
         * 资源名称
         */
        private String name;

        /**
         * 资源介绍
         */
        private String introduction;

        /**
         * 资源预览图片
         */
        private String imageUrl;

        /**
         * 资源配置类型
         *
         * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceDeployTypeConstants
         */
        private Integer deployType;

        /**
         * 资源是否必选
         */
        private Boolean required;

        /**
         * 资源code, 只有自动配置的资源有
         */
        private String resourceCode;

        /**
         * 资源状态
         */
        private Integer resourceStatus;

        /**
         * 资源是否已删除
         */
        private Boolean resourceDeleted;

        /**
         * 扩展字段
         */
        private FlowResourceExtra extra;

        /**
         * 资源物料图片列表
         */
        private List<FlowResourceImage> images;
    }
}
