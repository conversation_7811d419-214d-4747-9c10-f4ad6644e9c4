package fm.lizhi.ocean.wave.management.datastore.dao.singer;

import com.alibaba.csp.sentinel.util.AssertUtil;
import com.google.common.collect.Lists;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.*;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerOperateRecordMapper;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerVerifyApplySongInfoMapper;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerVerifyRecordMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class SingerVerifyApplyDao {

    @Resource
    private SingerVerifyRecordMapper singerVerifyApplyMapper;

    @Resource
    private SingerInfoDao singerInfoDao;

    @Resource
    private SingerOperateRecordMapper singerOperateRecordMapper;

    @Resource
    private SingerVerifyApplySongInfoMapper singerVerifyApplySongInfoMapper;

    /**
     * 根据ID列表查询出歌手认证记录
     *
     * @param ids 歌手认证记录ID列表
     * @return 歌手认证记录列表
     */
    public List<SingerVerifyRecord> getSingerVerifyRecordByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        example.createCriteria()
                .andIdIn(ids);
        return singerVerifyApplyMapper.selectByExample(example);
    }

    /**
     * 根据ID查询歌手认证记录
     *
     * @param id 歌手认证记录ID
     * @return 歌手认证记录
     */
    public SingerVerifyRecord getSingerVerifyRecordById(Long id) {
        SingerVerifyRecord entity = new SingerVerifyRecord();
        entity.setId(id);
        entity.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return singerVerifyApplyMapper.selectOne(entity);
    }

    /**
     * 事务操作审核通过
     *
     * @param id                   歌手认证记录ID
     * @param currentAuditStatus   当前审核状态
     * @param currentSingerStatus  当前歌手状态
     * @param operator             操作人
     * @param singerInfo           歌手信息
     * @param singerOperateRecord  歌手操作记录
     * @param needDeleteSingerType 需要修改歌手类型
     * @return 是否审核通过
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean approveSingerVerifyRecord(Long id, Integer currentAuditStatus, Integer currentSingerStatus, String operator, SingerInfo singerInfo,
                                             SingerOperateRecord singerOperateRecord, Integer needDeleteSingerType) {
        boolean success = updateSingerVerifyRecordStatus(id, currentAuditStatus, SingerAuditStatusEnum.PASS.getStatus(), operator, null, null);
        AssertUtil.assertState(success, "审核通过失败");
        if (needDeleteSingerType != null) {
            boolean res = singerInfoDao.deleteSingerInfo(singerInfo.getAppId(), singerInfo.getUserId(), Lists.newArrayList(needDeleteSingerType));
            AssertUtil.assertState(res, "删除歌手信息失败");
        }
        boolean singerUpdateRes = singerInfoDao.updateSingerStatusOrInsert(currentSingerStatus, singerInfo);
        AssertUtil.assertState(singerUpdateRes, "歌手信息更新失败");
        if (singerOperateRecord != null) {
            int count = singerOperateRecordMapper.insert(singerOperateRecord);
            AssertUtil.assertState(count > 0, "插入歌手操作记录失败");
        }
        return true;
    }

    /**
     * 修改认证记录状态
     *
     * @param id                   认证记录ID
     * @param currentAuditStatus   当前审核状态
     * @param currentSingerStatus  当前歌手状态
     * @param singerInfo           歌手信息
     * @param targetAuditStatus    目标审核状态
     * @param operator             操作人
     * @param rejectReason         拒绝原因
     * @param preAuditRejectReason 预审核拒绝原因
     * @param needDeleteSingerType 需要修改歌手类型
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSingerVerifyRecordStatus(Long id, Integer currentAuditStatus, Integer targetAuditStatus, Integer currentSingerStatus,
                                                  SingerInfo singerInfo, String operator, String rejectReason, SingerOperateRecord singerOperateRecord,
                                                  String preAuditRejectReason, Integer needDeleteSingerType) {
        boolean success = updateSingerVerifyRecordStatus(id, currentAuditStatus, targetAuditStatus, operator, rejectReason, preAuditRejectReason);
        AssertUtil.assertState(success, "审核状态修改失败");
        if (needDeleteSingerType != null) {
            boolean res = singerInfoDao.deleteSingerInfo(singerInfo.getAppId(), singerInfo.getUserId(), Lists.newArrayList(needDeleteSingerType));
            AssertUtil.assertState(res, "删除歌手信息失败");
        }
        //新增
        boolean singerUpdateRes = singerInfoDao.updateSingerStatusOrInsert(currentSingerStatus, singerInfo);
        AssertUtil.assertState(singerUpdateRes, "歌手信息更新失败");
        if (singerOperateRecord != null) {
            int count = singerOperateRecordMapper.insert(singerOperateRecord);
            AssertUtil.assertState(count > 0, "插入歌手操作记录失败");
        }
        return true;
    }

    /**
     * 根据ID和原始状态修改认证记录状态
     *
     * @param id            歌手认证记录ID
     * @param currentStatus 当前状态
     * @param auditStatus   目标状态
     * @param operator      操作人
     * @param rejectReason  拒绝原因
     * @return 是否修改成功
     */
    public boolean updateSingerVerifyRecordStatus(Long id, Integer currentStatus, Integer auditStatus, String operator, String rejectReason, String preAuditRejectReason) {
        return updateSingerVerifyRecordStatus(id, Lists.newArrayList(currentStatus), auditStatus, operator, rejectReason, preAuditRejectReason);
    }

    /**
     * 根据ID和原始状态修改认证记录状态
     *
     * @param id            歌手认证记录ID
     * @param currentStatus 当前状态
     * @param auditStatus   目标状态
     * @param operator      操作人
     * @param rejectReason  拒绝原因
     * @return 是否修改成功
     */
    public boolean updateSingerVerifyRecordStatus(Long id, List<Integer> currentStatus, Integer auditStatus, String operator, String rejectReason, String preAuditRejectReason) {
        SingerVerifyRecord singerVerifyRecord = new SingerVerifyRecord();
        singerVerifyRecord.setId(id);
        singerVerifyRecord.setAuditStatus(auditStatus);
        singerVerifyRecord.setOperator(operator);
        singerVerifyRecord.setAuditTime(new Date());
        if (Objects.equals(auditStatus, SingerAuditStatusEnum.REJECTED.getStatus())) {
            singerVerifyRecord.setRejectReason(rejectReason == null ? "" : rejectReason);
        }
        if (Objects.equals(auditStatus, SingerAuditStatusEnum.PRE_AUDIT_REJECTED.getStatus())) {
            singerVerifyRecord.setPreAuditRejectReason(preAuditRejectReason == null ? "" : preAuditRejectReason);
        }

        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        example.createCriteria()
                .andIdEqualTo(id)
                .andAuditStatusIn(currentStatus);
        return singerVerifyApplyMapper.updateByExample(singerVerifyRecord, example) > 0;
    }

    /**
     * 修改家族ID和厅主ID
     *
     * @param appId      应用ID
     * @param verifyId   认证记录ID
     * @param singerId   歌手ID
     * @param singerType 歌手类型
     * @param familyId   家族ID
     * @param njId       厅主ID
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFamilyIdAndNjId(Integer appId, Long verifyId, Long singerId, Integer singerType, Long familyId, Long njId) {
        SingerVerifyRecord singerVerifyRecord = new SingerVerifyRecord();
        singerVerifyRecord.setId(verifyId);
        singerVerifyRecord.setFamilyId(familyId);
        singerVerifyRecord.setNjId(njId);
        boolean res = singerVerifyApplyMapper.updateByPrimaryKey(singerVerifyRecord) > 0;
        AssertUtil.assertState(res, "修改认证记录签约信息失败");

        //歌手记录可能不存在，不用判断成功，审核通过后会拿最新的认证记录的签约信息同步过去的
        singerInfoDao.updateSingerFamilyAndNjId(appId, singerId, singerType, familyId, njId);
        return true;
    }

    /**
     * 根据认证申请ID列表查询歌曲信息
     *
     * @param applyIds 认证申请ID列表
     * @return 歌曲信息列表
     */
    public List<SingerVerifyApplySongInfo> batchGetApplySongInfoList(List<Long> applyIds) {
        SingerVerifyApplySongInfoExample example = new SingerVerifyApplySongInfoExample();
        example.createCriteria().andApplyIdIn(applyIds);
        return singerVerifyApplySongInfoMapper.selectByExample(example);
    }

    /**
     * 根据认证申请ID查询歌曲曲风，结果是map, key: 认证ID，value: 曲风字符串，逗号分隔
     *
     * @param applyIds 认证申请IDs
     * @return 歌曲信息
     */
    public Map<Long, String> getApplySongStyleMap(List<Long> applyIds) {
        List<SingerVerifyApplySongInfo> list = batchGetApplySongInfoList(applyIds);
        return list.stream()
                .collect(Collectors.groupingBy(SingerVerifyApplySongInfo::getApplyId, Collectors.mapping(SingerVerifyApplySongInfo::getSongStyle, Collectors.joining(","))));
    }

}
