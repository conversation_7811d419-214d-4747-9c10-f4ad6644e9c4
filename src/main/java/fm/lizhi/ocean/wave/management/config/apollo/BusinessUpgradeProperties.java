package fm.lizhi.ocean.wave.management.config.apollo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 业务配置项
 * <AUTHOR>
 */
@Data
public class BusinessUpgradeProperties {

    /**
     * 升级的模块
     */
    private String uri;

    /**
     * 升级开关
     */
    private boolean enabled;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 升级说明
     */
    private String upgradeRemark;
}
