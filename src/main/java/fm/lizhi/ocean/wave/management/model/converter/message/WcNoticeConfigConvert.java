package fm.lizhi.ocean.wave.management.model.converter.message;

import fm.lizhi.ocean.wave.management.model.param.message.WcNoticeConfigParam;
import fm.lizhi.ocean.wave.management.model.param.message.WcNoticeConfigQueryParam;
import fm.lizhi.ocean.wave.management.model.param.message.WcNoticeUpdateStatusParam;
import fm.lizhi.ocean.wave.management.model.vo.message.WcNoticeConfigItemVO;
import fm.lizhi.ocean.wave.management.model.vo.message.WcNoticeConfigResVO;
import fm.lizhi.ocean.wavecenter.api.message.bean.WcNoticeConfigBean;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestWcNoticeConfigPage;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestWcNoticeConfigSave;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestWcNoticeUpdateStatus;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseWcNoticeConfigPage;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface WcNoticeConfigConvert {

    WcNoticeConfigConvert I = Mappers.getMapper(WcNoticeConfigConvert.class);

    /**
     * 转换参数
     *
     * @param param    参数
     * @param appId    appId
     * @param operator 操作人
     * @return 转换结果
     */
    @Mappings({
            @Mapping(source = "appId", target = "appId"),
            @Mapping(source = "operator", target = "operator")
    })
    RequestWcNoticeConfigSave toRequestSaveConfig(WcNoticeConfigParam param, Integer appId, String operator);

    /**
     * 查询参数转换
     */
    RequestWcNoticeConfigPage toRequestQueryConfig(WcNoticeConfigQueryParam param, Integer appId);

    /**
     * DTO列表转VO列表
     */
    WcNoticeConfigResVO respToConfigVo(ResponseWcNoticeConfigPage response);

    /**
     * DTO转VO
     *
     * @param list DTO列表
     * @return 列表
     */
    List<WcNoticeConfigItemVO> toNoticeConfigVOList(List<WcNoticeConfigBean> list);

    /**
     * DTO转VO
     *
     * @param config DTO
     * @return 列表
     */
    WcNoticeConfigItemVO toNoticeConfigVO(WcNoticeConfigBean config);

    @Mappings({
            @Mapping(source = "appId", target = "appId"),
            @Mapping(source = "operator", target = "operator")
    })
    RequestWcNoticeUpdateStatus toRequestUpdateStatus(WcNoticeUpdateStatusParam param, Integer appId, String operator);
}
