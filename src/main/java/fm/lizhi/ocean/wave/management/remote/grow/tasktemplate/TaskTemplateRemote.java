package fm.lizhi.ocean.wave.management.remote.grow.tasktemplate;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.grow.tasktemplate.TaskTemplateConvert;
import fm.lizhi.ocean.wave.management.model.param.grow.tasktemplate.TaskTemplateManageListParam;
import fm.lizhi.ocean.wave.management.model.param.grow.tasktemplate.TaskTemplateSaveParam;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.request.RequestDeleteTaskTemplate;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.request.RequestQueryTaskTemplate;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.request.RequestSaveTaskTemplate;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.response.ResponseQueryTaskTemplate;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.service.TaskTemplateService;
import fm.lizhi.sso.client.SessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 任务模板远程服务
 *
 * <AUTHOR>
 */
@Component
public class TaskTemplateRemote {
    @Autowired
    private TaskTemplateService taskTemplateService;

    /**
     * 保存任务模板
     *
     * @param param 保存参数
     * @return 状态码，0为成功
     */
    public Result<Void> saveTaskTemplate(TaskTemplateSaveParam param) {
        RequestSaveTaskTemplate request = TaskTemplateConvert.INSTANCE.convert(param);
        request.setAppId(SessionExtUtils.getAppId());
        request.setOperator(SessionUtils.getAccount());
        return taskTemplateService.saveTaskTemplate(request);
    }

    /**
     * 查询任务模板列表
     *
     * @param param 查询参数
     * @return 任务模板列表结果
     */
    public Result<ResponseQueryTaskTemplate> queryTaskTemplateList(TaskTemplateManageListParam param) {
        RequestQueryTaskTemplate request = TaskTemplateConvert.INSTANCE.queryParamToReq(param);
        request.setAppId(SessionExtUtils.getAppId());
        return taskTemplateService.queryTaskTemplateList(request);
    }

    /**
     * 删除任务模板
     *
     * @return 状态码，0为成功
     */
    public Result<Void> deleteGrowTaskTemplate(List<Long> ids) {
        try {
            RequestDeleteTaskTemplate request = new RequestDeleteTaskTemplate();
            request.setAppId(SessionExtUtils.getAppId());
            request.setIds(ids);
            return taskTemplateService.deleteGrowTaskTemplate(request);
        } catch (Exception e) {
            // 记录异常日志，返回失败Result
            return ResultUtils.failure(e);
        }
    }
} 