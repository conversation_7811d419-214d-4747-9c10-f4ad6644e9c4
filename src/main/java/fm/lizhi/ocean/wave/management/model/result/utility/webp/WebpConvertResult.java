package fm.lizhi.ocean.wave.management.model.result.utility.webp;

import fm.lizhi.ocean.wave.platform.api.platform.webp.constant.WebpConvertQualityOptionEnum;
import fm.lizhi.ocean.wave.platform.api.platform.webp.constant.WebpConvertRequestTypeEnum;
import fm.lizhi.ocean.wave.platform.api.platform.webp.constant.WebpConvertStatusEnum;
import lombok.Data;

/**
 * webp转换结果
 */
@Data
public class WebpConvertResult {

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 业务请求id, 由业务方生成并保证唯一
     */
    private String bizRequestId;

    /**
     * 请求类型
     *
     * @see WebpConvertRequestTypeEnum
     */
    private String requestType;

    /**
     * 业务类型, 由业务方定义, 用于分类
     */
    private String bizType;

    /**
     * 源文件类型
     */
    private String sourceType;

    /**
     * 源文件SHA256值
     */
    private String sourceSha;

    /**
     * 源文件字节数
     */
    private Long sourceSize;

    /**
     * 源文件路径, 斜杆开头的相对路径
     */
    private String sourcePath;

    /**
     * 转换质量选项
     *
     * @see WebpConvertQualityOptionEnum
     */
    private Integer qualityOption;

    /**
     * 转换后的webp文件SHA256值
     */
    private String webpSha;

    /**
     * 转换后的webp文件字节数
     */
    private Long webpSize;

    /**
     * 转换后的webp文件路径, 斜杆开头的相对路径
     */
    private String webpPath;

    /**
     * 转换耗时秒数
     */
    private Long costSeconds;

    /**
     * 状态
     *
     * @see WebpConvertStatusEnum
     */
    private Integer status;

    /**
     * 错误码, 用于记录失败信息, 成功时值为0
     */
    private Integer errorCode;

    /**
     * 错误文本, 用于记录失败信息, 成功时值为空白
     */
    private String errorText;
}
