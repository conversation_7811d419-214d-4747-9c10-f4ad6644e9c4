package fm.lizhi.ocean.wave.management.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import fm.lizhi.commons.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/21 15:27
 */
public class MyDateUtil {

    /**
     * 重置秒为00
     *
     * @return
     */
    public static Date resetSecond(Long date) {
        return DateUtil.formatStrToDate(DateUtil.formatDateNormal(new Date(date)), "yyyy-MM-dd HH:mm");
    }

    /**
     * 将日期格式化为yyyyMMdd的数值
     *
     * @param date
     * @return
     */
    public static Integer getDateDayValue(Date date) {
        String str = DateUtil.formatDateToString(date, DateUtil.date);
        return Integer.valueOf(str);
    }

    /**
     * 日期向前偏移，并将日期格式化为yyyyMMdd的数值
     *
     * @param date
     * @return
     */
    public static Integer getDateDayValueBefore(int date, int before) {
        Date dayBefore = DateUtil.getDayBefore(getDayValueDate(date), before);
        return getDateDayValue(dayBefore);
    }

    /**
     * 返回指定的日期天数列表 倒序
     *
     * @param days
     * @return
     */
    public static List<Integer> getDateDayValueList(int days) {
        Date yesterday = DateUtil.getDayBefore(1);
        List<Integer> dates = new ArrayList<>(days);
        dates.add(MyDateUtil.getDateDayValue(yesterday));
        for (int i = 1; i < days; i++) {
            Date dayBefore = DateUtil.getDayBefore(yesterday, i);
            dates.add(MyDateUtil.getDateDayValue(dayBefore));
        }
        return dates;
    }

    /**
     * 获取一段时间范围内的dayValue列表 升序
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<Integer> getRangeDayValues(Date startDate, Date endDate) {
        List<Integer> dayValues = new ArrayList<>();
        int dayNum = getRangeTotal(startDate, endDate);
        dayValues.add(getDateDayValue(startDate));
        for (int i = 1; i < dayNum; i++) {
            Date after = DateUtil.getDayAfter(startDate, i);
            dayValues.add(getDateDayValue(after));
        }
        return dayValues;
    }

    public static Date getDayValueDate(int dateValue) {
        String str = String.valueOf(dateValue);
        return DateUtil.formatStrToDate(str, DateUtil.date);
    }

    public static Integer getDateMonthValue(Date date) {
        return Integer.valueOf(DateUtil.formatDateToString(date, "yyyyMM"));
    }


    /**
     * 获取分页区间的时间
     *
     * @param start
     * @param end
     * @param pageNo
     * @param pageSize
     * @return yyyy_MM_dd 天的格式
     */
    public static List<Date> getRangeDayDate(Date start, Date end, Integer pageNo, Integer pageSize) {
        if (pageNo == null || pageNo < 1) {
            pageNo = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        List<DateTime> dayList = cn.hutool.core.date.DateUtil.rangeToList(start, end, DateField.DAY_OF_YEAR);

        //是否超过范围
        if ((pageNo - 1) * pageSize > dayList.size()) {
            return Collections.EMPTY_LIST;
        }

        // 计算要跳过的元素数量
        int skipElements = (pageNo - 1) * pageSize;
        if (pageSize >= dayList.size()) {
            skipElements = 0;
        }
        dayList.sort(Comparator.comparing(DateTime::getTime).reversed());

        // 使用 Stream 实现分页
        return dayList.stream()
                .map(e -> DateUtil.formatDateToString(e, DateUtil.yyyy_MM_dd))
                .map(e -> DateUtil.formatStrToDate(e, DateUtil.yyyy_MM_dd))
                .skip(skipElements)
                .limit(pageSize)
                .collect(Collectors.toList());
    }


    public static List<Date> getRangeHourDate(Date start, Date end) {
        // 使用 Stream 实现分页
        List<DateTime> dayList = cn.hutool.core.date.DateUtil.rangeToList(start, end, DateField.HOUR);
        return new ArrayList<>(dayList);
    }


    /**
     * 获取时间范围的总天数
     *
     * @param start
     * @param end
     * @return
     */
    public static int getRangeTotal(Date start, Date end) {
        List<DateTime> dayList = cn.hutool.core.date.DateUtil.rangeToList(start, end, DateField.DAY_OF_YEAR);
        return dayList.size();
    }


    /**
     * 获取时间范围分页的结束时间
     *
     * @param start
     * @param end
     * @param pageNo
     * @param pageSize
     * @return
     */
    public static Date getRangeEndDesc(Date start, Date end, Integer pageNo, Integer pageSize) {
        if (pageNo == null || pageNo < 1) {
            pageNo = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        List<DateTime> dayList = cn.hutool.core.date.DateUtil.rangeToList(start, end, DateField.DAY_OF_YEAR);

        // 计算要跳过的元素数量
        int skipElements = (pageNo - 1) * pageSize;
        dayList.sort(Comparator.comparing(DateTime::getTime).reversed());
        // 使用 Stream 实现分页
        List<DateTime> pageList = dayList.stream()
                .skip(skipElements)
                .limit(pageSize)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pageList)) {
            return dayList.get(dayList.size() - 1);
        }
        return pageList.get(0);
    }


    public static Date getRangeStartDesc(Date start, Date end, Integer pageNo, Integer pageSize) {
        if (pageNo == null || pageNo < 1) {
            pageNo = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        List<DateTime> dayList = cn.hutool.core.date.DateUtil.rangeToList(start, end, DateField.DAY_OF_YEAR);

        // 计算要跳过的元素数量
        int skipElements = (pageNo - 1) * pageSize;
        dayList.sort(Comparator.comparing(DateTime::getTime).reversed());

        // 使用 Stream 实现分页
        List<DateTime> pageList = dayList.stream()
                .skip(skipElements)
                .limit(pageSize)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pageList)) {
            return dayList.get(dayList.size() - 1);
        }
        return pageList.get(pageList.size() - 1);
    }

    /**
     * 获取上一周的开始时间
     *
     * @return
     */
    public static Date getLastWeekStartDay() {
        DateTime dateTime = cn.hutool.core.date.DateUtil.offsetDay(new Date(), -7);
        return cn.hutool.core.date.DateUtil.beginOfWeek(dateTime);
    }

    /**
     * 获取上周的结束时间
     *
     * @return
     */
    public static Date getLastWeekEndDay() {
        DateTime dateTime = cn.hutool.core.date.DateUtil.offsetDay(new Date(), -7);
        return cn.hutool.core.date.DateUtil.endOfWeek(dateTime);
    }

    /**
     * 根据周开始时间返回周的结束时间
     *
     * @param weekStartDay
     * @return
     */
    public static Date getLastWeekEndDayByStartDay(Date weekStartDay) {
        if (weekStartDay == null) {
            return null;
        }
        return cn.hutool.core.date.DateUtil.endOfWeek(weekStartDay);
    }

    public static Date beginOfWeek(Date date) {
        return cn.hutool.core.date.DateUtil.beginOfWeek(date);
    }

    public static Date endOfWeek(Date date) {
        return cn.hutool.core.date.DateUtil.endOfWeek(date);
    }

    /**
     * 获取某个时间的上一周的结束时间
     *
     * @param date
     * @return
     */
    public static Date getLastWeekEnd(Date date) {
        return cn.hutool.core.date.DateUtil.offsetWeek(date, -1);
    }

    /**
     * 获取某个时间的上周的开始时间
     *
     * @param date
     * @return
     */
    public static Date getLastWeekStart(Date date) {
        DateTime dateTime = cn.hutool.core.date.DateUtil.offsetDay(date, -7);
        return cn.hutool.core.date.DateUtil.beginOfWeek(dateTime);
    }

    /**
     * 获取当前周的开始时间
     */
    public static Date getCurrentWeekStart() {
        return cn.hutool.core.date.DateUtil.beginOfWeek(new Date());
    }


    public static void main(String[] args) {
        Date startDate = DateUtil.formatStrToDate("2024-06-10 00:00:00", DateUtil.datetime_2);
        Date endDate = DateUtil.formatStrToDate("2024-07-01 23:59:59", DateUtil.datetime_2);
        Date date = resetSecond(endDate.getTime());
        System.out.println("date = " + date);

        System.out.println("recentDay" + getOffsetDayStartBefore(new Date(), 15));

        System.out.println(getOffsetDayStartBefore(new Date(1750240920000L), 15));
        System.out.println(MyDateUtil.getBeginOfDay(new Date(1750240920000L)));


    }

    /**
     * 获取在date之前的X天的开始时间
     *
     * @param date
     * @param offset
     * @return
     */
    public static Date getOffsetDayStartBefore(Date date, int offset) {
        DateTime dateTime = cn.hutool.core.date.DateUtil.offsetDay(date, -offset);
        return cn.hutool.core.date.DateUtil.beginOfDay(dateTime);
    }

    public static Date getBeginOfDay(Date date) {
        return cn.hutool.core.date.DateUtil.beginOfDay(date);
    }


    public static Date getPreviousHourStart(Date statTime) {
        DateTime previousHour = cn.hutool.core.date.DateUtil.offsetHour(statTime, -1);  // 减1小时
        return cn.hutool.core.date.DateUtil.beginOfHour(previousHour);              // 取整点
    }

    public static Date getHourEnd(Date statTime) {
        return cn.hutool.core.date.DateUtil.endOfHour(statTime);              // 取整点
    }

    /**
     * 获取当前时间距离指定格式时间还剩多少分钟
     * @param dateFormat YYYY-mm-dd
     * @return
     */
    public static int getMinutesFromNow(String dateFormat) {
        Date targetDate = DateUtil.formatStrToDate(dateFormat, DateUtil.date);
        Date now = new Date();
        long diffInMillies = targetDate.getTime() - now.getTime();
        return (int) (diffInMillies / (60 * 1000));
    }

    /**
     * 获取时间戳的年值
     * 
     * @param timestamp 时间戳(毫秒)
     * @return 年值，例如2025
     */
    public static int getYearFromTimestamp(long timestamp) {
        return cn.hutool.core.date.DateUtil.year(new Date(timestamp));
    }

}
