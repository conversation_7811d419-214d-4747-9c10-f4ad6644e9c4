package fm.lizhi.ocean.wave.management.manager.common;

import fm.lizhi.ocean.wave.management.model.dto.common.FeiShuCardMessageDTO;
import fm.lizhi.ocean.wave.management.model.param.common.FeiShuCardMessageParam;
import fm.lizhi.ocean.wave.management.model.result.common.FeiShuCardMessageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.net.URISyntaxException;

/**
 * 飞书机器人管理器
 */
@Component
@Slf4j
public class FeiShuBotManager {

    @Autowired
    @Qualifier("restTemplate")
    private RestTemplate restTemplate;

    /**
     * 发送卡片消息. 参考
     * <a href="https://open.feishu.cn/document/common-capabilities/message-card/getting-started/send-message-cards-with-a-custom-bot">
     * 使用自定义机器人发送卡片消息</a>
     *
     * @param param 消息参数
     * @return 发送结果
     */
    public FeiShuCardMessageResult sendCardMessage(FeiShuCardMessageParam param) {
        try {
            URI uri;
            try {
                uri = new URI(param.getWebhook());
            } catch (URISyntaxException e) {
                log.trace("Invalid webhook: {}", param.getWebhook(), e);
                return FeiShuCardMessageResult.failure("Invalid webhook: " + param.getWebhook());
            }
            RequestEntity<FeiShuCardMessageDTO> requestEntity = RequestEntity
                    .method(HttpMethod.POST, uri)
                    .contentType(MediaType.APPLICATION_JSON_UTF8)
                    .body(param.getCardMessage());
            ResponseEntity<FeiShuCardMessageResult> responseEntity = restTemplate.exchange(
                    requestEntity, FeiShuCardMessageResult.class);
            log.info("Send card message, requestEntity: {}, responseEntity: {}", requestEntity, responseEntity);
            FeiShuCardMessageResult body = responseEntity.getBody();
            if (body == null) {
                int httpStatus = responseEntity.getStatusCodeValue();
                return FeiShuCardMessageResult.failure("Empty response body, httpStatus: " + httpStatus);
            }
            return body;
        } catch (RuntimeException e) {
            log.info("Send card message exception, param: {}", param, e);
            return FeiShuCardMessageResult.failure("Send card message exception");
        }
    }
}
