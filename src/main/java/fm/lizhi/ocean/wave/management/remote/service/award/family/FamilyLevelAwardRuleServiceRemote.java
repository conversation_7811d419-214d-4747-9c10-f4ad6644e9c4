package fm.lizhi.ocean.wave.management.remote.service.award.family;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.award.family.FamilyLevelAwardRuleConvert;
import fm.lizhi.ocean.wave.management.model.param.award.family.CreateFamilyLevelAwardRuleParam;
import fm.lizhi.ocean.wave.management.model.param.award.family.DeleteFamilyLevelAwardRuleParam;
import fm.lizhi.ocean.wave.management.model.param.award.family.GetFamilyLevelAwardRuleParam;
import fm.lizhi.ocean.wave.management.model.param.award.family.UpdateFamilyLevelAwardRuleParam;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.ListFamilyLevelAwardRuleBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.*;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyLevelAwardRule;
import fm.lizhi.ocean.wavecenter.api.award.family.service.FamilyLevelAwardRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class FamilyLevelAwardRuleServiceRemote {

    @Autowired
    private FamilyLevelAwardRuleService familyLevelAwardRuleService;

    public Result<Void> createFamilyLevelAwardRule(CreateFamilyLevelAwardRuleParam param, Integer appId, String operator) {
        RequestCreateFamilyLevelAwardRule request = FamilyLevelAwardRuleConvert.I.toRequestCreateFamilyLevelAwardRule(param, appId, operator);
        return familyLevelAwardRuleService.createFamilyLevelAwardRule(request);
    }

    public Result<Void> updateFamilyLevelAwardRule(UpdateFamilyLevelAwardRuleParam param, Integer appId, String operator) {
        RequestUpdateFamilyLevelAwardRule request = FamilyLevelAwardRuleConvert.I.toRequestUpdateFamilyLevelAwardRule(param, appId, operator);
        return familyLevelAwardRuleService.updateFamilyLevelAwardRule(request);
    }

    public Result<Void> deleteFamilyLevelAwardRule(DeleteFamilyLevelAwardRuleParam param, String operator) {
        RequestDeleteFamilyLevelAwardRule request = FamilyLevelAwardRuleConvert.I.toRequestDeleteFamilyLevelAwardRule(param, operator);
        return familyLevelAwardRuleService.deleteFamilyLevelAwardRule(request);
    }

    public Result<List<ListFamilyLevelAwardRuleBean>> listFamilyLevelAwardRule(Integer appId) {
        RequestListFamilyLevelAwardRule request = FamilyLevelAwardRuleConvert.I.toRequestListFamilyLevelAwardRule(appId);
        return familyLevelAwardRuleService.listFamilyLevelAwardRule(request);
    }

    public Result<ResponseGetFamilyLevelAwardRule> getFamilyLevelAwardRule(GetFamilyLevelAwardRuleParam param) {
        RequestGetFamilyLevelAwardRule request = FamilyLevelAwardRuleConvert.I.toRequestGetFamilyLevelAwardRule(param);
        return familyLevelAwardRuleService.getFamilyLevelAwardRule(request);
    }
}
