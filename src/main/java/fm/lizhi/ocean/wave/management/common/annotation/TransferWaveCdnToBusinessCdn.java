package fm.lizhi.ocean.wave.management.common.annotation;

import fm.lizhi.ocean.wave.management.manager.common.CommonCdnManager;

import java.lang.annotation.Documented;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 标注字段需要转存到CDN, 该注解仅提供标注. 实际转存请调用{@link CommonCdnManager#transferWaveToBusinessByBean(Object, int)}
 */
@Documented
@Retention(RUNTIME)
@Target({FIELD})
@Inherited
public @interface TransferWaveCdnToBusinessCdn {

}
