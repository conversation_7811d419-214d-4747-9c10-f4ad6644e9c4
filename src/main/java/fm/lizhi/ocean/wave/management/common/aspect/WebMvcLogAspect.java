package fm.lizhi.ocean.wave.management.common.aspect;

import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * WebMvc日志切面
 */
@Aspect
@Slf4j
public class WebMvcLogAspect {

    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    /**
     * WebMvc日志
     *
     * @param joinPoint 切点
     * @return 执行结果
     * @throws Throwable 异常
     */
    @Around("@within(org.springframework.web.bind.annotation.RestController)")
    public Object logWebMvc(ProceedingJoinPoint joinPoint) throws Throwable {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (!(requestAttributes instanceof ServletRequestAttributes)) {
            log.info("RequestAttributes is not instance of ServletRequestAttributes, skip log.");
            return joinPoint.proceed();
        }
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        String requestURI = request.getRequestURI();
        BusinessEvnEnum businessEnvEnum = BusinessEvnEnum.from(SessionExtUtils.getAppId());
        String businessEnv = businessEnvEnum != null ? businessEnvEnum.getName() : null;
        String account = SessionUtils.getAccount();
        String realName = SessionUtils.getRealName();
        LocalDateTime start = LocalDateTime.now();
        try {
            return joinPoint.proceed();
        } finally {
            LocalDateTime end = LocalDateTime.now();
            log.info("{}, businessEnv: {}, account: {}, realName: {}, start: {}, end: {}, cost: {}ms", requestURI,
                    businessEnv, account, realName, start.format(DATE_TIME_FORMATTER), end.format(DATE_TIME_FORMATTER),
                    Duration.between(start, end).toMillis());
        }
    }
}
