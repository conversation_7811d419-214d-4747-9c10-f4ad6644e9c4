package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 活动日历分页查询参数
 */
@Data
public class PageActivityCalendarParam {

    /**
     * 分页页码
     */
    @NotNull
    @Min(1)
    private Integer pageNo;

    /**
     * 分页大小
     */
    @NotNull
    @Min(1)
    @Max(500)
    private Integer pageSize;

    /**
     * 厅主id
     */
    @NotNull
    private Long njId;

    /**
     * 最小开始时间
     */
    @NotNull
    private Long minStartTime;

    /**
     * 最大开始时间
     */
    @NotNull
    private Long maxStartTime;
}
