package fm.lizhi.ocean.wave.management.model.vo.award.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 列出公会等级奖励规则的结果VO
 */
@Data
public class ListFamilyLevelAwardRuleVO {

    /**
     * 规则id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 公会等级id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long levelId;

    /**
     * 公会等级名称
     */
    private String levelName;

    /**
     * 公会等级是否已删除
     */
    private Boolean levelDeleted;

    /**
     * 推荐卡数量
     */
    private Integer recommendCardNumber;

    /**
     * 推荐卡有效期, 默认单位为天
     */
    private Integer recommendCardValidPeriod;

    /**
     * 座驾id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long vehicleId;

    /**
     * 座驾名称
     */
    private String vehicleName;

    /**
     * 座驾图片
     */
    private String vehicleImage;

    /**
     * 座驾有效期, 默认单位为天
     */
    private Integer vehicleValidPeriod;

    /**
     * 勋章id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long medalId;

    /**
     * 勋章名称
     */
    private String medalName;

    /**
     * 勋章图片
     */
    private String medalImage;

    /**
     * 勋章有效期, 默认单位为天
     */
    private Integer medalValidPeriod;

    /**
     * 短号id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long shortNumberId;

    /**
     * 短号名称
     */
    private String shortNumberName;

    /**
     * 创建时间, 毫秒时间戳
     */
    private Long createTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改时间, 毫秒时间戳
     */
    private Long modifyTime;

    /**
     * 修改者
     */
    private String modifier;
}
