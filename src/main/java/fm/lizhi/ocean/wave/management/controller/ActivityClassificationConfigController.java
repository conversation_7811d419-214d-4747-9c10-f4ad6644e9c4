package fm.lizhi.ocean.wave.management.controller;

import fm.lizhi.ocean.wave.management.manager.ActivityClassificationConfigManager;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.DeleteClassificationParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveClassificationParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateClassificationParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityClassConfigResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 活动分类配置
 * <AUTHOR>
 */
@RestController
@RequestMapping("/activity/class")
@Slf4j
public class ActivityClassificationConfigController {

    @Autowired
    private ActivityClassificationConfigManager activityClassificationConfigManager;

    /**
     * 保存活动分类
     */
    @PostMapping("/save")
    public ResultVO<Void> save(@RequestBody SaveClassificationParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("save activity class: {}, appId:{}, operator:{}", param.getClassName(), appId, operator);
        return activityClassificationConfigManager.saveClassification(param, operator, appId);
    }

    /**
     * 更新活动分类
     */
    @PostMapping("/update")
    public ResultVO<Void> update(@RequestBody UpdateClassificationParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("update activity class: {}, appId:{}, operator:{}", param.getClassName(), appId, operator);
        return activityClassificationConfigManager.updateClassification(param, operator, appId);
    }


    /**
     * 删除活动分类
     */
    @PostMapping("/delete")
    public ResultVO<Void> delete(@RequestBody DeleteClassificationParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("delete activity class. id:{}, appId:{}, operator:{}", param.getId(), appId, operator);

        return activityClassificationConfigManager.deleteClassification(param.getId(), appId, operator);
    }

    /**
     * 列表
     */
    @GetMapping("/list")
    public ResultVO<List<ActivityClassConfigResult>> list(@RequestParam("bigClassId") Long bigClassId) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("list activity class. appId: {}, operator:{}, bigClassId:{}", appId, operator, bigClassId);

        return activityClassificationConfigManager.getClassificationList(appId, bigClassId);
    }
}
