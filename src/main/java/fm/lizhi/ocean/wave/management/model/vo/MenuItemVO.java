package fm.lizhi.ocean.wave.management.model.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class MenuItemVO {

    /**
     * 菜单id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer id;

    /**
     * 父菜单id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer parentId;

    /**
     * 菜单名
     */
    private String name;

    /**
     * 访问url
     */
    private String url;

    /**
     * 附加属性值
     */
    private String extraAttr;

}
