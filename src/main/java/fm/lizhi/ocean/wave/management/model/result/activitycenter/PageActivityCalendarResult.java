package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 活动日历分页查询结果
 */
@Data
public class PageActivityCalendarResult {

    /**
     * 活动id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 报名类型
     *
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum
     */
    private Integer applyType;

    /**
     * 审核状态
     *
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityAuditStatusEnum
     */
    private Integer auditStatus;

    /**
     * 活动状态
     *
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityStatusEnum
     */
    private Integer activityStatus;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 联系号码，微信
     */
    private String contactNumber;

    /**
     * 主持信息
     */
    private HostInfo hostInfo;

    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 大类名称
     */
    private String bigClassName;

    /**
     * 分类id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    /**
     * 分类名称
     */
    private String className;

    /**
     * 主持信息
     */
    @Data
    public static class HostInfo {

        /**
         * 用户id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 波段号
         */
        private String band;

        /**
         * 昵称
         */
        private String name;

        /**
         * 头像
         */
        private String photo;
    }
}
