package fm.lizhi.ocean.wave.management.model.result.activitycenter.ai;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.List;

/**
 * 背景去除结果
 */
@Data
public class RemoveImageBackgroundResult {

    /**
     * ai任务批次id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long serialId;

    /**
     * 结果列表
     */
    private List<Result> resultList;

    /**
     * 单个图片结果
     */
    @Data
    public static class Result {

        /**
         * 图片ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long imageId;

        /**
         * 图片URL
         */
        private String imageUrl;

        /**
         * 图片路径
         */
        private String imagePath;

        /**
         * 是否成功
         */
        private boolean success;

        /**
         * 错误提示信息
         */
        private String errorMsg;
    }
}
