package fm.lizhi.ocean.wave.management.model.vo.permission;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/20 15:34
 */
@Data
public class StatementVO {

    /**
     * ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 状态 1=启用 0=禁用
     */
    private Integer status;

    /**
     * 资源列表
     */
    private List<StatementResourceVO> resources;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 最近更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /**
     * 最近更新人
     */
    private String modifyUser;

    /**
     * 策略dsl
     */
    private String statementDsl;

    /**
     * 策略说明
     */
    private String description;

}
