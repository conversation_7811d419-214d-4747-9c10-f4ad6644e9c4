package fm.lizhi.ocean.wave.management.model.vo.resource;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class MountVO {

    /**
     * 座驾ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 座驾名称
     */
    private String name;

    /**
     * 座驾图片
     */
    private String image;
    
    
}
