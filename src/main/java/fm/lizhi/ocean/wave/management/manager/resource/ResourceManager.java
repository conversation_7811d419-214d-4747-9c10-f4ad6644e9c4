package fm.lizhi.ocean.wave.management.manager.resource;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.resource.DecorateConvert;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.resource.MedalVO;
import fm.lizhi.ocean.wave.management.model.vo.resource.MountVO;
import fm.lizhi.ocean.wave.management.remote.service.resource.DecorateServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;

@Component
public class ResourceManager {

    @Autowired
    private DecorateServiceRemote decorateServiceRemote;

    public ResultVO<PageVO<MountVO>> getMountList(String name, Integer page, Integer size, int appId) {
        Result<PageBean<DecorateInfoBean>> result = decorateServiceRemote.getMountList(name, page, size, appId);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result.rCode(), result.getMessage());
        }
        List<MountVO> mountVOs = DecorateConvert.INSTANCE.toMountVOList(result.target().getList());
        return ResultVO.success(PageVO.of(result.target().getTotal(), mountVOs));
    }

    public ResultVO<PageVO<MedalVO>> getMedalList(String name, Integer page, Integer size, int appId) {
        Result<PageBean<DecorateInfoBean>> result = decorateServiceRemote.getMedalList(name, page, size, appId);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result.rCode(), result.getMessage());
        }
        List<MedalVO> medalVOs = DecorateConvert.INSTANCE.toMedalVOList(result.target().getList());
        return ResultVO.success(PageVO.of(result.target().getTotal(), medalVOs));
    }
}
