package fm.lizhi.ocean.wave.management.controller;

import fm.lizhi.ocean.wave.management.manager.ActivityTemplateManager;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.*;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.*;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 活动模板管理
 */
@RestController
@RequestMapping("/activity/template")
@Slf4j
public class ActivityTemplateController {

    @Autowired
    private ActivityTemplateManager activityTemplateManager;

    /**
     * 创建活动模板
     *
     * @param param 创建活动模板参数
     * @return 创建活动模板结果
     */
    @PostMapping("/create")
    public ResultVO<CreateActivityTemplateResult> create(@RequestBody CreateActivityTemplateParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("create activity template, param: {}, appId: {}, operator: {}", param, appId, operator);
        return activityTemplateManager.createTemplate(param, appId, operator);
    }

    /**
     * 更新活动模板
     *
     * @param param 更新活动模板参数
     * @return 更新活动模板结果
     */
    @PostMapping("/update")
    public ResultVO<Void> update(@RequestBody UpdateActivityTemplateParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("update activity template, param: {}, appId: {}, operator: {}", param, appId, operator);
        return activityTemplateManager.updateTemplate(param, appId, operator);
    }

    /**
     * 删除活动模板
     *
     * @param param 删除活动模板参数
     * @return 删除活动模板结果
     */
    @PostMapping("/delete")
    public ResultVO<Void> delete(@RequestBody DeleteActivityTemplateParam param) {
        String operator = SessionUtils.getAccount();
        log.info("delete activity template, param: {}, operator: {}", param, operator);
        return activityTemplateManager.deleteTemplate(param, operator);
    }

    /**
     * 更新活动模板上下架状态
     *
     * @param param 更新活动模板上下架状态参数
     * @return 更新活动模板上下架状态结果
     */
    @PostMapping("/updateShelfStatus")
    public ResultVO<Void> updateShelfStatus(@RequestBody UpdateActivityTemplateShelfStatusParam param) {
        String operator = SessionUtils.getAccount();
        Integer appId = SessionExtUtils.getAppId();
        log.info("update activity template shelf status, param: {}, operator: {}, appId: {}", param, operator, appId);
        return activityTemplateManager.updateShelfStatus(param, operator, appId);
    }

    /**
     * 获取活动模板上下架状态
     *
     * @param id 活动模板id
     * @return 获取活动模板上下架状态结果
     */
    @GetMapping("/getShelfStatus")
    public ResultVO<GetActivityTemplateShelfStatusResult> getShelfStatus(@RequestParam Long id) {
        log.info("get activity template shelf status, id: {}", id);
        return activityTemplateManager.getShelfStatus(id);
    }

    /**
     * 分页查询活动模板
     *
     * @param param 分页查询活动模板参数
     * @return 分页查询活动模板结果
     */
    @GetMapping("/page")
    public ResultVO<PageVO<PageActivityTemplateResult>> page(PageActivityTemplateParam param) {
        log.info("page activity template, param: {}", param);
        return activityTemplateManager.pageTemplate(param);
    }

    /**
     * 获取活动模板
     *
     * @param id 活动模板id
     * @return 获取活动模板结果
     */
    @GetMapping("/get")
    public ResultVO<GetActivityTemplateResult> get(@RequestParam long id) {
        log.info("get activity template, id: {}", id);
        return activityTemplateManager.getTemplate(id);
    }

    /**
     * 获取基础活动配置
     *
     * @return 获取基础活动配置结果
     */
    @GetMapping("/getBaseActivityConfig")
    public ResultVO<GetBaseActivityConfigResult> getBaseActivityConfig() {
        Integer appId = SessionExtUtils.getAppId();
        log.info("get base activity config, appId: {}", appId);
        return activityTemplateManager.getBaseActivityConfig(appId);
    }
}
