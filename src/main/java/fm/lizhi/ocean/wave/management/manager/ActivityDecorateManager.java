package fm.lizhi.ocean.wave.management.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ActivityDecorateConvert;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.BatchGetDecorateParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.PageDecorateParam;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.DressUpResult;
import fm.lizhi.ocean.wave.management.remote.service.activitycenter.ActivityDecorateServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.DecorateBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestBatchGetDecorate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestGetDecorate;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityDecorateManager {

    @Autowired
    private ActivityDecorateServiceRemote activityDecorateServiceRemote;

    public ResultVO<PageVO<DressUpResult>> list(PageDecorateParam param, Integer appId){

        RequestGetDecorate request = ActivityDecorateConvert.I.toRequestGetDecorate(param, appId);

        Result<PageBean<DecorateBean>> result = activityDecorateServiceRemote.list(request);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }

        return ResultVO.success(ActivityDecorateConvert.I.toDressUpResultPageVO(result.target()));
    }

    public ResultVO<List<DressUpResult>> batchGetDressUp(BatchGetDecorateParam param, Integer appId) {

        RequestBatchGetDecorate request = ActivityDecorateConvert.I.toRequestBatchGetDecorate(param, appId);
        Result<List<DecorateBean>> result = activityDecorateServiceRemote.batchGetDressUp(request);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }

        return ResultVO.success(ActivityDecorateConvert.I.toDressUpResult(result.target()));
    }
}
