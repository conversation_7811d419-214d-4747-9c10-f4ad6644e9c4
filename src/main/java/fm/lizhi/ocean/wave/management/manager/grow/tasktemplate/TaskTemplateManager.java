package fm.lizhi.ocean.wave.management.manager.grow.tasktemplate;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.grow.tasktemplate.TaskTemplateConvert;
import fm.lizhi.ocean.wave.management.model.param.grow.tasktemplate.TaskTemplateManageListParam;
import fm.lizhi.ocean.wave.management.model.param.grow.tasktemplate.TaskTemplateSaveParam;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.grow.tasktemplate.TaskTemplateManageListVO;
import fm.lizhi.ocean.wave.management.remote.grow.tasktemplate.TaskTemplateRemote;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.response.ResponseQueryTaskTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 任务模板管理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TaskTemplateManager {
    @Autowired
    private TaskTemplateRemote taskTemplateRemote;

    /**
     * 保存任务模板
     *
     * @param param 保存参数
     * @return 只返回状态码，0为成功
     */
    public ResultVO<Void> saveTaskTemplate(TaskTemplateSaveParam param) {
        Result<Void> result = taskTemplateRemote.saveTaskTemplate(param);
        if (result.rCode() != 0) {
            log.warn("saveTaskTemplate fail, param={}, rCode={}", param, result.rCode());
            return ResultVO.failure(result.getMessage());
        }
        return ResultVO.success();
    }

    /**
     * 查询任务模板列表
     *
     * @param param 查询参数
     * @return 任务模板列表
     */
    public ResultVO<TaskTemplateManageListVO> manageList(TaskTemplateManageListParam param) {
        Result<ResponseQueryTaskTemplate> result = taskTemplateRemote.queryTaskTemplateList(param);
        if (result.rCode() != 0) {
            log.warn("manageList fail, param={}, rCode={}, msg={}", param, result.rCode(), result.getMessage());
            return ResultVO.failure("查询失败");
        }

        TaskTemplateManageListVO taskTemplateManageListVO = TaskTemplateConvert.INSTANCE.queryResultRespToVO(result.target());
        return ResultVO.success(taskTemplateManageListVO);
    }

    /**
     * 删除任务模板
     *
     * @return 只返回状态码，0为成功，失败400
     */
    public ResultVO<Void> deleteGrowTaskTemplate(List<Long> ids) {
        Result<Void> result = taskTemplateRemote.deleteGrowTaskTemplate(ids);
        if (result.rCode() != 0) {
            log.warn("deleteGrowTaskTemplate fail, id={}, rCode={}, msg={}", ids, result.rCode(), result.getMessage());
            return ResultVO.failure(result.getMessage());
        }
        return ResultVO.success();
    }
} 