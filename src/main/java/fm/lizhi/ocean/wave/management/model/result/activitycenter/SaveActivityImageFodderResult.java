package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import lombok.Data;

@Data
public class SaveActivityImageFodderResult {

    /**
     * 素材ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 素材图片地址
     */
    private String imageUrl;
    
    
    
}
