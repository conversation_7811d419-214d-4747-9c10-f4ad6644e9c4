package fm.lizhi.ocean.wave.management.datastore.dao.singer;

import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerOperateRecordConvert;
import fm.lizhi.ocean.wave.management.model.dto.singer.BuildSingerOperateRecordParamDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerInfoDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerOperateRecordDTO;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerRecordOperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingerOperateRecordDao {

    @Autowired
    private SingerVerifyApplyDao singerVerifyApplyDao;

    /**
     * 构建歌手操作记录
     *
     * @param param 构建参数
     * @return 歌手操作记录
     */
    public SingerOperateRecordDTO buildSingerOperateRecord(BuildSingerOperateRecordParamDTO param) {
        if (param.getOperateType() == null) {
            return null;
        }
        SingerInfoDTO singerInfo = param.getSingerInfo();
        if (SingerRecordOperateTypeEnum.ELIMINATE.equals(param.getOperateType())
                && SingerStatusEnum.AUTHENTICATING.getStatus() == singerInfo.getSingerStatus()) {
            // 认证中 --> 淘汰，直接跳过，不构建歌手操作流水
            log.info("singer {} is authenticating, skip build singer operate record", singerInfo.getUserId());
            return null;
        }
        SingerVerifyRecord verifyRecord = null;
        if (singerInfo != null && singerInfo.getSingerVerifyId() != null && singerInfo.getSingerVerifyId() > 0) {
            verifyRecord = singerVerifyApplyDao.getSingerVerifyRecordById(singerInfo.getSingerVerifyId());
        }

        return SingerOperateRecordConvert.I.buildSingerOperateRecordDto(singerInfo,
                param.getOperateType(),
                verifyRecord,
                param.getOperator(),
                param.getEliminationReason()
        );
    }

}
