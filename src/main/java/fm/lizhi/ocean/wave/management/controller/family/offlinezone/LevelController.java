package fm.lizhi.ocean.wave.management.controller.family.offlinezone;

import fm.lizhi.ocean.wave.management.manager.family.offlinezone.LevelConfigManager;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.UpdateLevelConfigParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListLevelConfigResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 线下专区等级控制器
 */
@RestController
@RequestMapping("/offline/level")
@Slf4j
public class LevelController {

    @Autowired
    private LevelConfigManager levelConfigManager;

    /**
     * 更新等级配置
     *
     * @param param 更新参数
     * @return 更新结果的VO
     */
    @PostMapping("/update")
    public ResultVO<Void> updateLevelConfig(@RequestBody @Validated UpdateLevelConfigParam param) {
        return levelConfigManager.updateLevelConfig(param);
    }

    /**
     * 列出所有等级配置
     *
     * @return 等级配置列表的VO
     */
    @GetMapping("/list")
    public ResultVO<List<ListLevelConfigResult>> listLevelConfigs() {
        return levelConfigManager.listLevelConfig();
    }
}
