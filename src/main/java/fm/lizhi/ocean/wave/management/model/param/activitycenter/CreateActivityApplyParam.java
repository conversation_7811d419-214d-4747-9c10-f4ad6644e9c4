package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import lombok.Data;

import java.util.List;

/**
 * 运营添加活动的参数
 */
@Data
public class CreateActivityApplyParam {

    /**
     * 厅主id
     */
    private Long njId;

    /**
     * 活动开始时间，毫秒时间戳
     */
    private Long startTime;

    /**
     * 活动结束时间，毫秒时间戳
     */
    private Long endTime;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 联系号码，微信
     */
    private String contactNumber;

    /**
     * 活动主持人id
     */
    private Long hostId;

    /**
     * 陪档主播id列表
     */
    private List<Long> accompanyNjIds;

    /**
     * 活动模板id
     */
    private Long templateId;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动分类id
     */
    private Long classId;

    /**
     * 活动目标
     */
    private String goal;

    /**
     * 活动介绍
     */
    private String introduction;

    /**
     * 活动流程列表
     */
    private List<Process> processes;

    /**
     * 辅助道具图片列表
     */
    private List<String> auxiliaryPropUrls;

    /**
     * 活动海报地址
     */
    private String posterUrl;

    /**
     * 玩法工具列表, 参考枚举值
     */
    private List<Integer> activityTools;

    /**
     * 房间公告
     */
    private String roomAnnouncement;

    /**
     * 房间公告图片列表, 最多3张, 小西米特有
     */
    private List<String> roomAnnouncementImages;

    /**
     * 房间背景id
     */
    @Deprecated
    private Long roomBackgroundId;

    /**
     * 房间背景id列表
     */
    private List<Long> roomBackgroundIds;

    /**
     * 房间头像框id
     */
    @Deprecated
    private Long avatarWidgetId;

    /**
     * 房间头像框id列表
     */
    private List<Long> avatarWidgetIds;

    /**
     * 活动流量资源列表
     */
    private List<FlowResource> flowResources;

    /**
     * 房间角标ID
     */
    private Long roomMarkId;

    /**
     * 房间角标URL
     */
    private String roomMarkUrl;

    /**
     * 礼物ID列表
     */
    private List<Long> giftIds;


    /**
     * 活动流程
     */
    @Data
    public static class Process {

        /**
         * 环节名称
         */
        private String name;

        /**
         * 时长
         */
        private String duration;

        /**
         * 说明
         */
        private String explanation;
    }

    /**
     * 流量资源
     */
    @Data
    public static class FlowResource {

        /**
         * 资源配置id
         */
        private Long resourceConfigId;

        /**
         * 资源code
         */
        private String resourceCode;

        /**
         * 资源图片URL
         */
        private String imageUrl;

        /**
         * 资源额外信息
         */
        private FlowResourceExtra extra;
    }

    /**
     * 流量资源额外信息
     */
    @Data
    public static class FlowResourceExtra {

        /**
         * 图片颜色
         */
        private String color;

        /**
         * 图片宽高比
         */
        private String scale;

        /**
         * 官频位座位号, 当资源为官频位时必须传
         */
        private Integer seat;

        /**
         * 官频位展示开始时间, 毫秒时间戳, 当资源为官频位时必须传
         */
        private Long startTime;

        /**
         * 官频位展示结束时间, 毫秒时间戳, 当资源为官频位时必须传
         */
        private Long endTime;

        /**
         * 挂件id, 当资源为挂件时必须传
         */
        private Long pendantId;
    }
}
