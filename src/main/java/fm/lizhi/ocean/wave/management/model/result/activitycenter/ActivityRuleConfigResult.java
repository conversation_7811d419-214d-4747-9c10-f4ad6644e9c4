package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.TypeNameProvider;
import lombok.Data;

/**
 *
 * 活动-提报规则
 *
 * <AUTHOR>
 * @date 2024-10-12 05:22:23
 */
@Data
public class ActivityRuleConfigResult {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 规则类型，1：提报次数，2：官频位轮播厅数
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRuleEnum
     */
    private Integer ruleType;

    /**
     * 次数
     */
    private Integer count;

    /**
     * 周期
     */
    private String period;

    /**
     * 收入限制
     */
    private Long income;

    /**
     * 用户ID列表
     */
    private String userIds;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 操作者
     */
    private String operator;

}