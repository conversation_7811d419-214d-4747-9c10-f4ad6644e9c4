package fm.lizhi.ocean.wave.management.manager.singer;

import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerInfoDao;
import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerWhiteListDao;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerInfoConvert;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.*;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.PageSingerInfoResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.processor.singer.ISingerProcessor;
import fm.lizhi.ocean.wave.management.remote.service.anchor.singer.SingerInfoRemote;
import fm.lizhi.ocean.wave.management.remote.service.user.UserCommonServiceRemote;
import fm.lizhi.ocean.wave.management.remote.service.user.UserFamilyServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponsePageSingerInfo;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerWhiteListConfig;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.ImportSingerInfoBean;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.response.ResponseBatchImportSinger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.unidal.lookup.util.StringUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingerInfoManager {

    @Autowired
    private SingerInfoRemote singerInfoRemote;

    @Autowired
    private SingerInfoDao singerInfoDao;

    @Autowired
    private ProcessorFactory factory;

    @Autowired
    private UserCommonServiceRemote userCommonServiceRemote;
    @Autowired
    private UserFamilyServiceRemote userFamilyServiceRemote;

    @Autowired
    private SingerWhiteListDao singerWhiteListDao;


    /**
     * 歌手库-歌手维度列表
     *
     * @param param
     * @param appId
     * @return
     */
    public ResultVO<PageVO<PageSingerInfoResult>> singerDetail(PageSingerInfoParam param, Integer appId) {
        Result<PageBean<ResponsePageSingerInfo>> result = singerInfoRemote.pageSingerInfo(param, appId);
        if (ResultUtils.isFailure(result)) {
            log.warn(" singerDetail is fail. appId:{}, param:{}", appId, param);
            return ResultVO.failure(result.rCode(), result.getMessage());
        }

        return ResultVO.success(SingerInfoConvert.INSTANCE.convertPageSingerInfoResult(result.target()));
    }

    /**
     * 淘汰歌手
     *
     * @param param    参数
     * @param appId    appId
     * @param operator 操作人
     * @return 淘汰结果
     */
    public ResultVO<Void> eliminateSinger(EliminateSingerParam param, Integer appId, String operator) {
        List<SingerInfo> singerInfoList = singerInfoDao.getSingerInfoByIds(appId, param.getIds(), Lists.newArrayList(SingerStatusEnum.EFFECTIVE, SingerStatusEnum.AUTHENTICATING));
        if (CollectionUtils.isEmpty(singerInfoList)) {
            log.warn("singer info list is empty. ids:{}", param.getIds());
            return ResultVO.failure("歌手信息不存在");
        }
        //校验列表中的歌手类型是否一致
        if (singerInfoList.stream().map(SingerInfo::getSingerType).distinct().count() > 1) {
            log.warn("singer type is not same. ids:{}", param.getIds());
            return ResultVO.failure("歌手类型不一致");
        }

        //过滤出认证中的歌手用户ID, 结果是String
        List<String> authenticatingUserIds = singerInfoList.stream().filter(singerInfo -> singerInfo.getSingerStatus() == SingerStatusEnum.AUTHENTICATING.getStatus()).map(singerInfo -> String.valueOf(singerInfo.getUserId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(authenticatingUserIds)) {
            log.info("has authenticating singer. ids:{}", authenticatingUserIds);
            return ResultVO.failure("存在认证中的歌手:" + StringUtils.join(authenticatingUserIds, ","));
        }

        //取出歌手ID
        List<Long> userIds = singerInfoList.stream().map(SingerInfo::getUserId).collect(Collectors.toList());
        ISingerProcessor processor = factory.getProcessor(ISingerProcessor.class);
        //获取出关联淘汰的ID
        List<Long> relatedEliminateSingerIds = processor.getRelatedEliminateSingerRecordIds(userIds, appId, singerInfoList.get(0).getSingerType());
        log.info("eliminateSinger singerId:{}, singerType:{},relatedEliminateSingerIds:{}", param.getIds(), singerInfoList.get(0).getSingerType(), relatedEliminateSingerIds);
        CollectionUtils.addAll(param.getIds(), relatedEliminateSingerIds);
        Result<Void> result = singerInfoRemote.eliminateSinger(param, appId, operator, true);
        if (ResultUtils.isFailure(result)) {
            log.warn(" eliminateSinger is fail. appId:{}, param:{}, operator:{},code:{}", appId, JsonUtil.dumps(param), operator, result.rCode());
            return ResultVO.failure(result.rCode(), result.getMessage());
        }
        //如果是淘汰认证歌手，需要补发关联淘汰的歌手的装扮
        processor.reissueDecorateAfterEliminate(appId, userIds, singerInfoList.get(0).getSingerType(), operator);
        return ResultVO.success();
    }

    /**
     * 晋升歌手
     */
    public ResultVO<Void> upgradeSinger(UpgradeSingerParam param, Integer appId, String operator) {
        Result<Void> result = singerInfoRemote.upgradeSinger(param, appId, operator);
        if (ResultUtils.isFailure(result)) {
            log.warn(" upgradeSinger is fail. appId:{}, param:{}, operator:{}", appId, param, operator);
            return ResultVO.failure(result.rCode(), result.getMessage());
        }
        return ResultVO.success();
    }

    public ResultVO<BatchImportSingerVO> importSinger(BatchImportSingerParam param, Integer appId, String operator) {

        List<ImportSingerInfoBean> importList = Lists.newArrayList();
        List<BatchImportSingerParam.ImportSingerParam> singerInfoList = param.getSingerInfoList();

        List<BatchImportSingerVO.FailImportSingerInfo> failList = Lists.newArrayList();
        // 需要移除不符合条件的singerParam，不能直接用for-each循环
        Iterator<BatchImportSingerParam.ImportSingerParam> iterator = singerInfoList.iterator();
        while (iterator.hasNext()) {
            BatchImportSingerParam.ImportSingerParam singerParam = iterator.next();
            String songStyle = singerParam.getSongStyle();
            BatchImportSingerVO.FailImportSingerInfo failImportSingerInfo = new BatchImportSingerVO.FailImportSingerInfo()
                    .setSingerId(singerParam.getSingerId())
                    .setSongStyle(songStyle)
                    .setOriginalSinger(singerParam.getOriginalSinger())
                    .setSingerType(singerParam.getSingerType());

            String[] split = songStyle.split(",");
            if (split.length == 0) {
                failImportSingerInfo.setFailReason("曲风不能为空;");
                failList.add(failImportSingerInfo);
                iterator.remove();
                continue;
            }

            Result<UserBean> userByBand = userCommonServiceRemote.getUserById(appId, singerParam.getSingerId());
            if (RpcResult.isFail(userByBand)) {
                failImportSingerInfo.setFailReason("波段号错误,请改正后重新上传;");
                failList.add(failImportSingerInfo);
                iterator.remove();
                continue;
            }
            Result<UserInFamilyBean> userInFamily = userFamilyServiceRemote.getUserInFamily(appId, userByBand.target().getId());
            if (RpcResult.isFail(userInFamily) || userInFamily.target().getNjId() == null) {
                failImportSingerInfo.setFailReason("主播签约厅信息查询失败，请重新尝试;");
                failList.add(failImportSingerInfo);
                iterator.remove();
                continue;
            }
            SingerTypeEnum byType = SingerTypeEnum.getByType(singerParam.getSingerType());
            if (byType == null) {
                failImportSingerInfo.setFailReason("歌手等级错误,请改正后重新上传;");
                failList.add(failImportSingerInfo);
                iterator.remove();
                continue;
            }
            ImportSingerInfoBean bean = new ImportSingerInfoBean();
            bean.setBand(userByBand.target().getBand());
            bean.setFamilyId(userInFamily.target().getFamilyId());
            bean.setNjId(userInFamily.target().getNjId());
            bean.setOriginalSinger(singerParam.getOriginalSinger());
            bean.setSongStyle(songStyle);
            bean.setSingerId(userByBand.target().getId());
            bean.setSingerType(byType);
            bean.setContactNumber(singerParam.getContactNumber());
            importList.add(bean);
        }
        Result<ResponseBatchImportSinger> result = singerInfoRemote.importSinger(importList, appId, operator);
        if (RpcResult.isFail(result)) {
            return ResultVO.failure("批量导入失败");
        }

        List<BatchImportSingerVO.FailImportSingerInfo> failListRes = new ArrayList<>();
        BatchImportSingerVO vo = new BatchImportSingerVO();
        List<BatchImportSingerVO.FailImportSingerInfo> batchImportSingerVO = SingerInfoConvert.INSTANCE.toBatchImportSingerVO(result.target().getFailImportSingerIdList());
        if (!batchImportSingerVO.isEmpty()) {
            failListRes.addAll(batchImportSingerVO);
        }
        if (!failList.isEmpty()) {
            failListRes.addAll(failList);
        }
        vo.setFailImportSingerInfoList(failListRes);
        return ResultVO.success(vo);
    }

    /**
     * 移除白名单后淘汰歌手
     *
     * @param appId    appId
     * @param singerId 歌手id
     * @param operator 操作人
     * @return 结果
     */
    public ResultVO<Void> eliminateSingerAfterRemoveWhiteList(int appId, Long singerId, Integer singerType, String operator) {
        //查询歌手信息
        List<SingerInfo> singerList = singerInfoDao.getSingerInfoBySingerId(appId, singerId);
        if (CollectionUtils.isEmpty(singerList)) {
            return ResultVO.success();
        }
        ISingerProcessor processor = factory.getProcessor(ISingerProcessor.class);
        if (processor.isNeedRemoveSingerAfterRemoveWhiteList(appId, singerList.get(0).getNjId())) {
            //查询出在白名单中的歌手
            List<SingerWhiteListConfig> whiteList = singerWhiteListDao.getSingerWhiteListConfigBySingerIds(appId, Lists.newArrayList(singerId));
            //取出歌手记录ID
            List<Long> singerRecordIds = getNeedRemoveSingerIds(whiteList, singerList, singerType);
            log.info("removeSingerWhiteList: userIds={}， singerId:{}, singerType:{}", singerRecordIds, singerId, singerType);
            if (CollectionUtils.isEmpty(singerRecordIds)) {
                return ResultVO.failure("歌手不存在，请刷新后重试");
            }
            EliminateSingerParam singerParam = new EliminateSingerParam().setIds(singerRecordIds).setEliminateReason("移除白名单关联淘汰");
            Result<Void> result = singerInfoRemote.eliminateSinger(singerParam, appId, operator, false);
            //TODO Rcode 淘汰原因用枚举描述
            if (RpcResult.isFail(result)) {
                log.warn(" eliminateSinger is fail. appId:{}, singerId:{},singerType:{} operator:{}", appId, singerId, singerType, operator);
                return ResultVO.failure("成功移除白名单，关联淘汰非点唱厅的歌手失败！");
            }
        }
        return ResultVO.success();
    }

    /**
     * 获取需要移除关联移除的其他等级的歌手
     *
     * @param singerWhiteListConfigList 白名单配置
     * @param singerList                歌手列表
     * @param singerType                歌手等级
     * @return 歌手id
     */
    public static List<Long> getNeedRemoveSingerIds(List<SingerWhiteListConfig> singerWhiteListConfigList, List<SingerInfo> singerList, int singerType) {
        if (CollectionUtils.isEmpty(singerWhiteListConfigList)) {
            return singerList.stream().map(SingerInfo::getId).collect(Collectors.toList());
        }

        //取出当前要移除的歌手类型
        List<Long> ids = singerList.stream()
                .filter(singerInfo -> singerInfo.getSingerType() == singerType)
                .map(SingerInfo::getId)
                .collect(Collectors.toList());

        Map<String, SingerWhiteListConfig> singerWhiteListConfigMap = singerWhiteListConfigList.stream()
                .collect(Collectors.toMap(singerWhiteListConfig -> singerWhiteListConfig.getSingerId() + "_" + singerWhiteListConfig.getSingerType(), singerWhiteListConfig -> singerWhiteListConfig));
        //过滤出不在白名单中的歌手 信息
        List<Long> needRemoveIds = singerList.stream()
                .filter(singerInfo -> !singerWhiteListConfigMap.containsKey(singerInfo.getUserId() + "_" + singerInfo.getSingerType()))
                .map(SingerInfo::getId)
                .collect(Collectors.toList());
        ids.addAll(needRemoveIds);
        //去重
        ids = ids.stream().distinct().collect(Collectors.toList());
        return ids;
    }
}
