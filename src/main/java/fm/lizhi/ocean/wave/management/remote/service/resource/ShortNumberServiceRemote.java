package fm.lizhi.ocean.wave.management.remote.service.resource;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.request.RequestListShortNumber;
import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.response.ResponseListShortNumber;
import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.service.ShortNumberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class ShortNumberServiceRemote {

    @Autowired
    private ShortNumberService shortNumberService;

    public Result<List<ResponseListShortNumber>> listShortNumber(Integer appId) {
        RequestListShortNumber request = new RequestListShortNumber();
        request.setAppId(appId);
        return shortNumberService.listShortNumber(request);
    }
}
