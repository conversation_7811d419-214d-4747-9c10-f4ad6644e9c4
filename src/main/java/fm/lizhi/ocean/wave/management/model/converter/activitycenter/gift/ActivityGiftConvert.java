package fm.lizhi.ocean.wave.management.model.converter.activitycenter.gift;

import fm.lizhi.ocean.wave.management.model.param.activitycenter.gift.GetGiftsParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.gift.ListGiftParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.gift.GetGiftsResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.gift.ListGiftResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.gift.bean.GetGiftsBean;
import fm.lizhi.ocean.wavecenter.api.gift.bean.ListGiftBean;
import fm.lizhi.ocean.wavecenter.api.gift.request.RequestGetGifts;
import fm.lizhi.ocean.wavecenter.api.gift.request.RequestListGift;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityGiftConvert {

    RequestListGift toRequestListGift(ListGiftParam param, Integer appId);

    PageVO<ListGiftResult> toListGiftResults(PageBean<ListGiftBean> pageBean);

    RequestGetGifts toRequestGetGifts(GetGiftsParam param, Integer appId);

    List<GetGiftsResult> toGetGiftsResults(List<GetGiftsBean> beans);
}
