package fm.lizhi.ocean.wave.management.model.converter.anchor.singer;

import fm.lizhi.ocean.wave.management.model.param.anchor.singer.SaveApplyMenuConfigParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.UpdateAuditConfigParam;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.*;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.AddChatSenceConfigVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.EntranceRuleVO;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerChatSceneBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerPreAuditBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSaveApplyMenuConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestUpdateSingerAuditConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseApplyMenuConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerChatScene;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerEnumerateConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSongStyleConfig;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.model.SingerMenuConfigDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SingerConfigConvert {

    SingerConfigConvert INSTANCE = Mappers.getMapper(SingerConfigConvert.class);

    /**
     * 将歌手枚举配置响应转换为歌手枚举配置结果
     * @param response 歌手枚举配置响应
     * @return 歌手枚举配置结果
     */
    @Mapping(target = "auditStatus", source = "response.singerVerifyAuditStatus")
    SingerEnumerateConfigResult response2Result(ResponseSingerEnumerateConfig response, List<EntranceRuleVO> entranceRule);

    @Mapping(target = "operator", ignore = true)
    RequestUpdateSingerAuditConfig param2Request(UpdateAuditConfigParam param);

    List<SingerAuditConfigResult> auditConfigResp2Result(List<SingerPreAuditBean> auditConfigList);

    List<SingerChatSenceConfigResult> chatScenes2Result(List<ResponseSingerChatScene> target);

    List<SingerChatSceneBean> chatSenceVO2Request(List<AddChatSenceConfigVO> configList);

    SingerMenuConfigDTO toSingerMenuConfigDTO(SaveApplyMenuConfigParam param, Integer appId, String operator);

    ApplyMenuConfigResult toApplyMenuConfigResult(SingerMenuConfigDTO singerMenuConfigDTO);

    SongStyleNumResult toSongStyleNumResult(ResponseSongStyleConfig responseSongStyleConfig);
}
