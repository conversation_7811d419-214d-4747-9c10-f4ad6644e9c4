package fm.lizhi.ocean.wave.management.manager.singer;

import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.management.constants.message.PushConstants;
import fm.lizhi.ocean.wave.management.constants.message.SingerPushTopicEnum;
import fm.lizhi.ocean.wave.management.kafka.singer.producer.SingerKafkaProducer;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerStatusPushDTO;
import fm.lizhi.ocean.wave.server.common.push.manager.RomePushManager;
import fm.lizhi.ocean.wave.server.common.push.vo.PushVO;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.SingerPassMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/25 14:28
 */
@Slf4j
@Component
public class SingerPushManager {

    @Autowired
    private RomePushManager pushManager;

    @Autowired
    private SingerKafkaProducer singerKafkaProducer;


    /**
     * 推送歌手审核状态变更
     *
     * @param singerId 歌手id
     */
    public void pushVerifyStatusChange(int appId, long singerId) {
        try {
            String key = SingerPushTopicEnum.SINGER_VERIFY_STATUS_CHANGE.getKey(appId, singerId);
            // 发送罗马消息
            PushVO<SingerStatusPushDTO> pushVO = new PushVO<>();
            pushVO.setBiz(PushConstants.Biz.SINGER_VERIFY_STATUS_CHANGE);
            pushVO.setData(new SingerStatusPushDTO().setUserId(String.valueOf(singerId)));
            pushManager.asyncPushWithCompLowTimeliness(key, pushVO);
        } catch (Exception e) {
            log.error("发送消息失败, userId={}", singerId, e);
        }
    }

    /**
     * 发送Kafka消息
     *
     * @param appId    应用ID
     * @param singers  歌手列表
     * @param operator 操作类型
     */
    public void sendSingerPassKafkaMessage(int appId, List<SingerInfo> singers, String operator, String reason, Long transactionId) {
        for (SingerInfo singer : singers) {
            try {
                SingerTypeEnum singerType = SingerTypeEnum.getByType(singer.getSingerType());
                SingerPassMsg singerPassMsg = new SingerPassMsg()
                        .setAppId(appId)
                        .setSingerIds(Lists.newArrayList(singer.getUserId()))
                        .setSingerType(singerType.getType())
                        .setSongStyle(singer.getSongStyle())
                        .setOperator(operator)
                        .setReason(reason)
                        .setTransactionId(String.valueOf(transactionId));

                String message = JsonUtil.dumps(singerPassMsg);
                singerKafkaProducer.send("lz_topic_singer_pass_msg", String.valueOf(appId), message);
            } catch (Exception e) {
                log.error("Failed to send kafka message, appId:{}, singerType:{}，userId:{}", appId, singer.getSingerType(), singer.getUserId(), e);
            }
        }

    }

    /**
     * 歌手通过发送kafka消息
     *
     * @param appId         应用id
     * @param singerIds     歌手ID
     * @param singerType    歌手类型
     * @param songStyle     曲风
     * @param operator      操作者
     * @param reason        原因
     * @param transactionId 事务ID
     */
    public void sendSingerPassKafkaMessage(int appId, List<Long> singerIds, int singerType, String songStyle,
                                           String operator, String reason, Long transactionId) {
        try {
            SingerTypeEnum singerTypeEnum = SingerTypeEnum.getByType(singerType);
            SingerPassMsg singerPassMsg = new SingerPassMsg()
                    .setAppId(appId)
                    .setSingerIds(singerIds)
                    .setSingerType(singerTypeEnum.getType())
                    .setSongStyle(songStyle)
                    .setOperator(operator)
                    .setReason(reason)
                    .setTransactionId(String.valueOf(transactionId));

            String message = JsonUtil.dumps(singerPassMsg);
            singerKafkaProducer.send("lz_topic_singer_pass_msg", String.valueOf(appId), message);
        } catch (Exception e) {
            log.error("Failed to send kafka message, appId:{}, singerType:{}", appId, SingerTypeEnum.NEW.getType(), e);
        }
    }

}
