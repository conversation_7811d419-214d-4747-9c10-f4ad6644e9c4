package fm.lizhi.ocean.wave.management.manager.singer.handler;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.management.manager.singer.SingerVerifyApplyManager;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerVerifyConvert;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerAuditParamDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerExecuteAuditDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.UpdateSingerVerifyStatusParamDTO;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 歌手认证待审核状态处理器
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingerAuditWaitAuditHandler extends AbstractSingerAuditHandler {

    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;

    @Override
    public SingerExecuteAuditDTO auditHandle(SingerAuditParamDTO param, SingerVerifyRecord verifyRecord) {
        // 从待定状态流转回待审核状态
        UpdateSingerVerifyStatusParamDTO paramDTO = SingerVerifyConvert.INSTANCE.buildUpdateParam(verifyRecord, param, SingerAuditStatusEnum.WAIT_AUDIT.getStatus(),
                null, false, verifyRecord.getSingerType());
        boolean res = singerVerifyApplyManager.updateSingerVerifyRecordStatus(paramDTO);
        log.info("SingerAuditWaitAuditHandler.auditHandle update singer verify record status, paramDTO:{}, res:{}", JsonUtil.dumps(paramDTO), res);
        return res ? SingerExecuteAuditDTO.success() : SingerExecuteAuditDTO.failure("修改状态失败，请稍候重试!");
    }

    @Override
    public SingerAuditStatusEnum getAuditStatusEnum() {
        return SingerAuditStatusEnum.WAIT_AUDIT;
    }
}
