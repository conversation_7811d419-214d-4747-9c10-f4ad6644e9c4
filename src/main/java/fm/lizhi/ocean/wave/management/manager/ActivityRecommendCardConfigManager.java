package fm.lizhi.ocean.wave.management.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ActivityRecommendCardConfigConvert;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityRecommendCardParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityRecommendCardParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityRecommendCardConfigResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.remote.service.activitycenter.ActivityRecommendCardConfigServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityRecommendCardConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRecommendCard;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRecommendCard;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityRecommendCardConfigManager {

    @Autowired
    private ActivityRecommendCardConfigServiceRemote activityRecommendCardConfigServiceRemote;

    /**
     * 保存
     */
    public ResultVO<Void> save(SaveActivityRecommendCardParam param, Integer appId, String operator) {
        RequestSaveActivityRecommendCard request = ActivityRecommendCardConfigConvert.I.toRequestSaveActivityRecommendCard(param, appId, operator);
        Result<Void> result = activityRecommendCardConfigServiceRemote.save(request);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }

        return ResultVO.success();
    }

    /**
     * 更新
     */
    public ResultVO<Void> update(UpdateActivityRecommendCardParam param, Integer appId, String operator){
        RequestUpdateActivityRecommendCard request = ActivityRecommendCardConfigConvert.I.toRequestUpdateActivityRecommendCard(param, appId, operator);
        Result<Void> result = activityRecommendCardConfigServiceRemote.update(request);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success();
    }

    /**
     * 删除
     */
    public ResultVO<Void> delete(Long id, Integer appId, String operator) {
        Result<Void> result = activityRecommendCardConfigServiceRemote.delete(id, appId, operator);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success();
    }

    /**
     * 获取列表
     */
    public ResultVO<List<ActivityRecommendCardConfigResult>> list(Integer appId) {
        Result<List<ActivityRecommendCardConfigBean>> result = activityRecommendCardConfigServiceRemote.list(appId);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success(ActivityRecommendCardConfigConvert.I.ActivityRecommendCardConfigResults(result.target()));
    }

}
