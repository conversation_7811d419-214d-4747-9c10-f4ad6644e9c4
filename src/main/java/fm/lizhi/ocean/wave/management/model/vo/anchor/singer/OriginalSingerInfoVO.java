package fm.lizhi.ocean.wave.management.model.vo.anchor.singer;

import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OriginalSingerInfoVO {

     /**
     * 是否为原唱
     */
    private Boolean originalSinger;

    /**
     * 原唱链接
     */
    private String originalSongUrl;

    /**
     * 社交认证图片列表，最多三个
     */
    private List<String> socialVerifyImageList;
}
