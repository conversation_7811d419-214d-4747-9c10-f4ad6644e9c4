package fm.lizhi.ocean.wave.management.model.param.award.singer;

import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperatorEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 歌手装扮流水
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PageSingerDecorateFlowParam {



    @NotNull(message = "分页大小不能为空")
    @Min(value = 1, message = "分页大小不能小于1")
    private int pageSize;

    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于1")
    private int pageNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 装扮类型
     */
    private Integer decorateType;

    /**
     * 操作类型 1: 发放 2: 回收
     * @see SingerDecorateFlowOperateEnum
     */
    private Integer operateType;

    /**
     * 操作人, 不做模糊查询
     * @see SingerDecorateFlowOperatorEnum
     */
    private String operator;

    /**
     * 开始操作时间
     */
    private Long startOperateTime;

    /**
     * 结束操作时间
     */
    private Long endOperateTime;

}
