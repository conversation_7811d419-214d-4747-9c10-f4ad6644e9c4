package fm.lizhi.ocean.wave.management.config.apollo.singer;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.config.AbsBizConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 活动配置
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ConfigurationProperties(prefix = "singer")
public class SingerConfig extends AbsBizConfig<CommonSingerConfig> {

    private HySingerConfig hy;
    private PpSingerConfig pp;
    private XmSingerConfig xm;

    public SingerConfig() {
        HySingerConfig hyConfig = new HySingerConfig();
        PpSingerConfig ppConfig = new PpSingerConfig();
        XmSingerConfig xmConfig = new XmSingerConfig();
        this.hy = hyConfig;
        this.pp = ppConfig;
        this.xm = xmConfig;
        bizConfigMap.put(BusinessEvnEnum.HEI_YE.appId(), hyConfig);
        bizConfigMap.put(BusinessEvnEnum.PP.appId(), ppConfig);
        bizConfigMap.put(BusinessEvnEnum.XIMI.appId(), xmConfig);
    }

}
