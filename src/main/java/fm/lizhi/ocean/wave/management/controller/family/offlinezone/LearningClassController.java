package fm.lizhi.ocean.wave.management.controller.family.offlinezone;

import fm.lizhi.ocean.wave.management.manager.family.offlinezone.LearningClassManager;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.AddLearningClassParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.DeleteLearningClassParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.ListLearningClassParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.UpdateLearningClassParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.AddLearningClassResult;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListLearningClassResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 线下专区学习课堂控制器
 */
@RestController
@RequestMapping("/offline/learningClass")
@Slf4j
public class LearningClassController {

    @Autowired
    private LearningClassManager learningClassManager;

    /**
     * 新增学习课堂资料
     *
     * @param param 新增参数
     * @return 新增结果的VO
     */
    @PostMapping("/add")
    public ResultVO<AddLearningClassResult> addLearningClass(@RequestBody @Validated AddLearningClassParam param) {
        return learningClassManager.addLearningClass(param);
    }

    /**
     * 更新学习课堂资料
     *
     * @param param 更新参数
     * @return 更新结果的VO
     */
    @PostMapping("/update")
    public ResultVO<Void> updateLearningClass(@RequestBody @Validated UpdateLearningClassParam param) {
        return learningClassManager.updateLearningClass(param);
    }

    /**
     * 删除学习课堂资料
     *
     * @param param 删除参数
     * @return 删除结果的VO
     */
    @PostMapping("/delete")
    public ResultVO<Void> deleteLearningClass(@RequestBody @Validated DeleteLearningClassParam param) {
        return learningClassManager.deleteLearningClass(param);
    }

    /**
     * 列出学习课堂资料
     *
     * @param param 列出参数
     * @return 列出结果的VO
     */
    @GetMapping("/list")
    public ResultVO<PageVO<ListLearningClassResult>> listLearningClass(@Validated ListLearningClassParam param) {
        return learningClassManager.listLearningClass(param);
    }
}
