package fm.lizhi.ocean.wave.management.model.result.activitycenter;


import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 玩法配置
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ActivityToolsConfig {

    /**
     * 描述
     */
    private String toolDesc;

    /**
     * 类型
     */
    private int type;

    /**
     * 名称
     */
    private String name;

    /**
     * 工具类型, 1:玩法; 2: 工具
     */
    private Integer toolType;
}
