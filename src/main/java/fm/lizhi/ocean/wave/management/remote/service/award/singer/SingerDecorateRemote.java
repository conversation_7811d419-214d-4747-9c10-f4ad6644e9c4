package fm.lizhi.ocean.wave.management.remote.service.award.singer;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestBatchDeliverSingerAward;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestDeliverSingerAward;
import fm.lizhi.ocean.wavecenter.api.award.singer.serivce.SingerDecorateOperateService;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestGetDecorateInfo;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.service.DecorateService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 歌手装扮远程服务
 *
 * <AUTHOR>
 */
@Component
public class SingerDecorateRemote {

    @Autowired
    private SingerDecorateOperateService singerDecorateAwardOperate;
    @Autowired
    private DecorateService decorateService;

    /**
     * 发放歌手奖励
     *
     * @param appId      应用ID
     * @param singerId   歌手ID
     * @param singerType 歌手类型
     * @return 发放结果
     */
    public Result<Void> deliverSingerAward(int appId, long singerId, int singerType, String reason, String operator) {
        RequestDeliverSingerAward buildRequest = buildRequest(appId, singerId, singerType, SingerDecorateFlowOperateEnum.GRANT, reason, operator);
        return singerDecorateAwardOperate.singerDecorateAwardOperate(buildRequest);
    }

    /**
     * 回收歌手奖励
     *
     * @param appId      应用ID
     * @param singerId   歌手ID
     * @param singerType 歌手类型
     * @return 回收结果
     */
    public Result<Void> recoverSingerAward(int appId, long singerId, int singerType, String reason, String operator) {
        RequestDeliverSingerAward buildRequest = buildRequest(appId, singerId, singerType, SingerDecorateFlowOperateEnum.RECOVER, reason, operator);
        return singerDecorateAwardOperate.singerDecorateAwardOperate(buildRequest);
    }

    /**
     * 批量发放歌手奖励
     *
     * @param appId      应用ID
     * @param singerIds  歌手ID列表
     * @param singerType 歌手类型
     * @param reason     操作原因
     * @param operator   操作人
     * @return 结果
     */
    public Result<Void> batchDeliverSingerAward(int appId, List<Long> singerIds, int singerType, String reason, String operator) {
        //构建批量发放的请求参数
        RequestBatchDeliverSingerAward buildRequest = new RequestBatchDeliverSingerAward();
        buildRequest.setAppId(appId);
        buildRequest.setSingerIds(singerIds);
        buildRequest.setSingerType(singerType);
        buildRequest.setOperateType(SingerDecorateFlowOperateEnum.GRANT);
        buildRequest.setReason(reason);
        buildRequest.setOperator(operator);
        return singerDecorateAwardOperate.batchSingerDecorateAwardOperate(buildRequest);
    }


    public DecorateInfoBean getDecorateInfo(int appId, long decorateId, PlatformDecorateTypeEnum decorateType) {
        RequestGetDecorateInfo request = new RequestGetDecorateInfo();
        request.setAppId(appId);
        request.setDecorateId(decorateId);
        request.setDecorateType(decorateType.getType());
        Result<DecorateInfoBean> decorateInfo = decorateService.getDecorateInfo(request);
        if (RpcResult.isFail(decorateInfo)) {
            return null;
        }
        return decorateInfo.target();
    }

    private RequestDeliverSingerAward buildRequest(int appId, long singerId, int singerType, SingerDecorateFlowOperateEnum operateType,
                                                   String reason, String operator) {
        RequestDeliverSingerAward request = new RequestDeliverSingerAward();
        request.setAppId(appId);
        request.setSingerId(singerId);
        request.setSingerType(singerType);
        request.setOperateType(operateType);
        request.setReason(reason);
        request.setOperator(operator);
        return request;
    }

}
