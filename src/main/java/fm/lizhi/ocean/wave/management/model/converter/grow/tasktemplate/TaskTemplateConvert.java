package fm.lizhi.ocean.wave.management.model.converter.grow.tasktemplate;

import fm.lizhi.ocean.wave.management.model.param.grow.tasktemplate.TaskTemplateManageListParam;
import fm.lizhi.ocean.wave.management.model.param.grow.tasktemplate.TaskTemplateSaveParam;
import fm.lizhi.ocean.wave.management.model.vo.grow.tasktemplate.TaskTemplateManageListVO;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.request.RequestQueryTaskTemplate;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.request.RequestSaveTaskTemplate;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.response.ResponseQueryTaskTemplate;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface TaskTemplateConvert {
    TaskTemplateConvert INSTANCE = Mappers.getMapper(TaskTemplateConvert.class);

    /**
     * 转换
     *
     * @param param 参数
     * @return 结果
     */
    @Mapping(target = "operator", ignore = true)
    @Mapping(target = "appId", ignore = true)
    @Mapping(target = "taskTemplateList", source = "taskTemplateList")
    RequestSaveTaskTemplate convert(TaskTemplateSaveParam param);

    /**
     * 查询参数转换
     * @param param 查询参数
     * @return 查询请求
     */
    @Mapping(target = "appId", ignore = true)
    RequestQueryTaskTemplate queryParamToReq(TaskTemplateManageListParam param);

    /**
     * 查询结果响应对象转VO
     * @param response 查询响应
     * @return 任务模板管理列表VO
     */
    TaskTemplateManageListVO queryResultRespToVO(ResponseQueryTaskTemplate response);
} 