package fm.lizhi.ocean.wave.management.common.aspect;

import fm.lizhi.ocean.wave.management.util.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;

import java.util.StringJoiner;

/**
 * 远程调用异常切面
 */
@Aspect
@Slf4j
public class RemoteExceptionAspect {

    /**
     * 对{@link fm.lizhi.ocean.wave.management.remote.service}包下的所有Remote类返回值为Result的方法抛出的异常包装为Result返回
     *
     * @param joinPoint 切点
     * @return 如果方法调用抛出异常, 返回包装后的Result, 否则返回原方法的返回值
     * @throws Throwable 异常
     */
    @Around("execution(public fm.lizhi.commons.service.client.pojo.Result" +
            " fm.lizhi.ocean.wave.management.remote.service..*Remote.*(..))")
    public Object handleException(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            return joinPoint.proceed();
        } catch (Throwable e) {
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            String argsContent = buildArgsContent(methodSignature, joinPoint.getArgs());
            log.error("{} invoke error, args: {}", methodSignature.getName(), argsContent, e);
            return ResultUtils.failure(e);
        }
    }

    private String buildArgsContent(MethodSignature methodSignature, Object[] args) {
        try {
            String[] parameterNames = methodSignature.getParameterNames();
            StringJoiner stringJoiner = new StringJoiner("`");
            for (int index = 0; index < parameterNames.length; index++) {
                Object arg = index < args.length ? args[index] : null;
                stringJoiner.add(parameterNames[index] + "=" + arg);
            }
            return stringJoiner.toString();
        } catch (RuntimeException e) {
            log.warn("{} build args content failed", methodSignature.getName(), e);
            return "N/A";
        }
    }
}
