package fm.lizhi.ocean.wave.management.controller.anchor.singer;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import fm.lizhi.ocean.wave.management.manager.singer.SingerHallInfoManager;
import fm.lizhi.ocean.wave.management.manager.file.FileExportManager;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerHallInfoConvert;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.SingerHallInfoExcelParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.SingerHallInfoParam;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.SingerHallInfoResult;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.SingerHallSummaryInfoResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerHallExcelInfoVO;
import fm.lizhi.ocean.wave.management.model.vo.file.FileExportVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/singer/room")
@Slf4j
public class SingerHallInfoController {

    @Autowired
    private SingerHallInfoManager singerHallInfoManager;

    @Autowired
    private FileExportManager fileExportManager;

    @GetMapping("/detail")
    public ResultVO<PageVO<SingerHallInfoResult>> getSingerHallInfo(SingerHallInfoParam param) {
        Integer appId = SessionExtUtils.getAppId();
        return singerHallInfoManager.getSingerHallInfo(param, appId);
    }

    @GetMapping("/export")
    public void exportSingerHallInfo(SingerHallInfoExcelParam param, HttpServletResponse response) {
        Integer appId = SessionExtUtils.getAppId();
        FileExportVO<SingerHallExcelInfoVO> fileExportVO = new FileExportVO<>();
        fileExportVO.setFileName("歌手库-厅");
        fileExportVO.setHead(SingerHallExcelInfoVO.class);
        fileExportVO.setQueryAll(param.isQueryAll());
        fileExportVO.setPageNo(param.isQueryAll() ? 1 : param.getPageNo());
        fileExportVO.setPageSize(param.getPageSize());
        try {
            fileExportManager.exportToHttpResponse(fileExportVO, response, (fectPageNo, fectPageSize)-> {
                param.setPageNo(fectPageNo);
                param.setPageSize(fectPageSize);
                ResultVO<PageVO<SingerHallInfoResult>> result = singerHallInfoManager.getSingerHallInfo(param, appId);
                List<SingerHallExcelInfoVO> excelList = SingerHallInfoConvert.INSTANCE.singerHallInfosToResult2Excels(result.getData().getList());
                return PageVO.of(result.getData().getTotal(), excelList);
            });
        } catch (IOException e) {
            log.error("导出歌手库-厅失败", e);
            throw new RuntimeException("导出歌手库-厅失败");
        }
    }

    @GetMapping("/summary")
    public ResultVO<SingerHallSummaryInfoResult> getSingerHallSummaryInfo(@RequestParam(required = false, defaultValue = "0") long njId) {

        Integer appId = SessionExtUtils.getAppId();
        return singerHallInfoManager.getSingerHallSummaryInfo(njId, appId);
    }
}
