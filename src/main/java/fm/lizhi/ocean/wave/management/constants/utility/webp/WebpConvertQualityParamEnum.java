package fm.lizhi.ocean.wave.management.constants.utility.webp;

import fm.lizhi.ocean.wave.platform.api.platform.webp.constant.WebpConvertQualityOptionEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * webp转换质量参数枚举
 */
@AllArgsConstructor
@Getter
public enum WebpConvertQualityParamEnum {

    /**
     * 普通
     */
    NORMAL(WebpConvertQualityOptionEnum.NORMAL.getValue(), 3, true),
    /**
     * 最高质量
     */
    HIGHEST_QUALITY(WebpConvertQualityOptionEnum.HIGHEST_QUALITY.getValue(), 0, false),
    /**
     * 最小体积
     */
    MINIMUM_SIZE(WebpConvertQualityOptionEnum.MINIMUM_SIZE.getValue(), 5, true),
    ;

    /**
     * 转换质量选项值
     */
    private final Integer value;
    /**
     * 压缩级别, 0~6, 值越低, 文件越大
     */
    private final Integer compressLevel;
    /**
     * 是否启用混合压缩模式
     */
    private final Boolean compressMixed;

    /**
     * 根据转换质量选项值获取枚举
     *
     * @param value 转换质量选项值
     * @return 转换质量参数枚举
     */
    public static WebpConvertQualityParamEnum fromValue(Integer value) {
        for (WebpConvertQualityParamEnum paramEnum : WebpConvertQualityParamEnum.values()) {
            if (Objects.equals(value, paramEnum.getValue())) {
                return paramEnum;
            }
        }
        return null;
    }
}
