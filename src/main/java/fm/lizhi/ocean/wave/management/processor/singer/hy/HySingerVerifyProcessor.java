package fm.lizhi.ocean.wave.management.processor.singer.hy;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.processor.singer.ISingerVerifyProcessor;
import fm.lizhi.ocean.wave.management.remote.service.award.singer.SingerDecorateRemote;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateOperateReasonConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class HySingerVerifyProcessor implements ISingerVerifyProcessor {


    @Autowired
    private SingerDecorateRemote singerDecorateRemote;

    @Override
    public void recoverSingerAward(int appId, long singerId, int singerType, String operator) {
        //回收奖励
        Result<Void> recoverRes = singerDecorateRemote.recoverSingerAward(appId,
                singerId, singerType, SingerDecorateOperateReasonConstant.SINGER_TYPE_CHANGE, operator);
        if (recoverRes.rCode() != 0) {
            log.error("recover singer award failed, singerId:{}, singerType:{}, rCode:{}", singerId, singerType, recoverRes.rCode());
        }
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }
}
