package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityFodderClassificationEnum;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class PageActivityImageFodderParam {

    /**
     * 素材名称
     */
    private String name;

    /**
     * 素材分类，参考枚举值
     * @see ActivityFodderClassificationEnum
     */
    private Integer type;

    private int pageNo;

    private int pageSize;
}