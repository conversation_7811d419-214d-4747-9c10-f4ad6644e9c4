package fm.lizhi.ocean.wave.management.manager.singer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.config.apollo.singer.SingerConfig;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerConfigConvert;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.AddChatSenceConfigParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.SaveApplyMenuConfigParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.UpdateAuditConfigParam;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.*;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.EntranceRuleVO;
import fm.lizhi.ocean.wave.management.remote.service.anchor.singer.SingerConfigServiceRemote;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerChatSceneEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerAuditConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerChatScene;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerEnumerateConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSongStyleConfig;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.model.MenuVisibleRuleConfigDTO;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.model.SingerMenuConfigDTO;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.rediskey.SingerRedisKey;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.constant.SingerVisibleConditionEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class SingerConfigServiceManager {

    @Autowired
    private SingerConfigServiceRemote singerConfigServiceRemote;
    @Resource(name = "redis_ocean_wavecenter")
    private RedisClient redisClient;


    @Autowired
    private SingerConfig singerConfig;

    /**
     * 获取歌手枚举配置
     *
     * @param appId
     * @return
     */
    public ResultVO<SingerEnumerateConfigResult> getEnumerateConfig(Integer appId) {
        Result<ResponseSingerEnumerateConfig> result = singerConfigServiceRemote.getEnumerateConfig(appId);
        if (result.rCode() != 0) {
            return ResultVO.failure("获取歌手枚举配置失败");
        }
        List<EntranceRuleVO> entranceRule = Lists.newArrayList();
        for (SingerVisibleConditionEnum value : SingerVisibleConditionEnum.values()) {
            entranceRule.add(EntranceRuleVO.builder().ruleCode(value.getValue()).build());
        }
        return ResultVO.success(SingerConfigConvert.INSTANCE.response2Result(result.target(), entranceRule));
    }

    /**
     * 更新歌手审核配置
     *
     * @param param 参数
     * @return 结果
     */
    public ResultVO<Void> updateAuditConfig(UpdateAuditConfigParam param, String operator) {
        Result<Void> result = singerConfigServiceRemote.updateAuditConfig(param, operator);
        if (result.rCode() != 0) {
            return ResultVO.failure("更新歌手审核配置失败");
        }
        return ResultVO.success();
    }

    /**
     * 获取歌手审核配置
     *
     * @param appId 应用ID
     * @return 结果
     */
    public ResultVO<List<SingerAuditConfigResult>> getSingerAuditConfig(Integer appId) {
        Result<ResponseSingerAuditConfig> result = singerConfigServiceRemote.getSingerAuditConfig(appId);
        if (result.rCode() != 0) {
            return ResultVO.failure("查询歌手审核配置失败");
        }
        return ResultVO.success(SingerConfigConvert.INSTANCE.auditConfigResp2Result(result.target().getAuditConfigList()));
    }

    public ResultVO<Void> addChatSenceConfig(AddChatSenceConfigParam param, Integer appId) {
        Result<Void> result = singerConfigServiceRemote.addChatSenceConfig(param, appId);
        if (result.rCode() != 0) {
            return ResultVO.failure("添加私信场景配置失败");
        }
        return ResultVO.success();
    }

    public ResultVO<Void> deleteChatSenceConfig(String sceneCode, Integer appId) {
        Result<Void> result = singerConfigServiceRemote.deleteChatSenceConfig(sceneCode, appId);
        if (result.rCode() != 0) {
            return ResultVO.failure("删除私信场景配置失败");
        }
        return ResultVO.success();
    }

    public ResultVO<List<SingerChatSenceConfigResult>> getChatSenceConfigs(Integer appId, String sceneCode,
                                                                           Integer singerType) {
        Result<List<ResponseSingerChatScene>> result = singerConfigServiceRemote.getChatSenceConfigs(appId, sceneCode, singerType);
        if (result.rCode() != 0) {
            return ResultVO.failure("查询私信场景配置失败");
        }

        List<ResponseSingerChatScene> chatSceneList = result.target().stream()
                .filter(scene ->
                        !scene.getSceneCode().equals(SingerChatSceneEnum.HOST_PASS_AUDIT_HALL_NOT_PASS.getSceneCode())
                ).collect(Collectors.toList());

        return ResultVO.success(SingerConfigConvert.INSTANCE.chatScenes2Result(chatSceneList));
    }


    /**
     * 获取歌手申请菜单配置
     *
     * @param appId      应用ID
     * @param singerType 歌手类型
     * @return 菜单配置
     */
    public ResultVO<ApplyMenuConfigResult> getApplyMenuConfig(Integer appId, Integer singerType) {
        String key = SingerRedisKey.APPLY_MENU_CONFIG.getKey(appId, singerType, ConfigUtils.getEnvRequired().name());
        String value = redisClient.get(key);
        if (value == null) {
            return ResultVO.failure("获取歌手申请菜单配置失败");
        }
        SingerMenuConfigDTO singerMenuConfigDTO = JsonUtils.fromJsonString(value, SingerMenuConfigDTO.class);
        if (CollectionUtils.isEmpty(singerMenuConfigDTO.getBizRuleConfigs())) {
            singerMenuConfigDTO.setBizRuleConfigs(defaultMenuVisibleRuleConfigs());
        }
        //弱关联开关没开 or 资深 条件不展示
        if (!singerConfig.getBizConfig(appId).isLessRelevanceSwitch() || singerType != SingerTypeEnum.NEW.getType()) {
            singerMenuConfigDTO.setBizRuleConfigs(Collections.emptyList());
        }
        return ResultVO.success(SingerConfigConvert.INSTANCE.toApplyMenuConfigResult(singerMenuConfigDTO));
    }


    private List<MenuVisibleRuleConfigDTO> defaultMenuVisibleRuleConfigs() {
        List<MenuVisibleRuleConfigDTO> configs = Lists.newArrayList();
        for (SingerVisibleConditionEnum value : SingerVisibleConditionEnum.values()) {
            configs.add(MenuVisibleRuleConfigDTO.builder()
                    .ruleCode(value.getValue())
                    .enabled(false)
                    .count(0)
                    .build());
        }
        return configs;
    }

    /**
     * 保存歌手申请菜单配置
     *
     * @param param    菜单配置参数
     * @param appId    应用ID
     * @param operator 操作人
     * @return 结果
     */
    public ResultVO<Void> saveApplyMenuConfig(SaveApplyMenuConfigParam param, Integer appId, String operator) {
        SingerMenuConfigDTO dto = SingerConfigConvert.INSTANCE.toSingerMenuConfigDTO(param, appId, operator);
        // 开启的条件校验
        List<MenuVisibleRuleConfigDTO> enabledList = dto.getBizRuleConfigs().stream().filter(MenuVisibleRuleConfigDTO::getEnabled).collect(Collectors.toList());
        if(singerConfig.getBizConfig(appId).isLessRelevanceSwitch()
                && param.getEnabled()
                && param.getSingerType() == SingerTypeEnum.NEW.getType()
                && CollectionUtils.isEmpty(enabledList)) {
            return ResultVO.failure("可见权限条件必须开启一个");
        }
        // 周流水条件校验
        for (MenuVisibleRuleConfigDTO configDTO : enabledList) {
            String ruleCode = configDTO.getRuleCode();
            if(!Objects.equals(ruleCode, SingerVisibleConditionEnum.CONTRACT_ANCHOR_WEEKLY_FLOW.getValue())
                    && !Objects.equals(ruleCode, SingerVisibleConditionEnum.CONTRACT_ROOM_WEEKLY_FLOW.getValue())) {
                continue;
            }
            if(configDTO.getCount() == null || configDTO.getCount() <= 0) {
                return ResultVO.failure("周流水条件必须大于0");
            }
        }
        String key = SingerRedisKey.APPLY_MENU_CONFIG.getKey(appId, dto.getSingerType(), ConfigUtils.getEnvRequired().name());
        String result = redisClient.set(key, JSON.toJSONString(dto));
        if (!"OK".equals(result)) {
            return ResultVO.failure("保存歌手申请菜单配置失败");
        }
        return ResultVO.success();
    }

    /**
     * 获取曲风数量配置
     *
     * @param appId      应用ID
     * @return 结果
     */
    public ResultVO<SongStyleNumResult> getSongStyleCountConfig(Integer appId) {
        Result<ResponseSongStyleConfig> result = singerConfigServiceRemote.getSongStyleConfig(appId);
        SongStyleNumResult songStyleNumResult = SingerConfigConvert.INSTANCE.toSongStyleNumResult(result.target());
        return ResultVO.success(songStyleNumResult);
    }
}
