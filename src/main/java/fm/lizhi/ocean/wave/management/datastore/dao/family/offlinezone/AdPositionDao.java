package fm.lizhi.ocean.wave.management.datastore.dao.family.offlinezone;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.management.datastore.mapper.family.offlinezone.OfflineZoneAdPositionExtMapper;
import fm.lizhi.ocean.wave.management.model.converter.family.offlinezone.AdPositionConvert;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.AddAdPositionParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.DeleteAdPositionParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.ListAdPositionParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.UpdateAdPositionParam;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneAdPosition;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneAdPositionMapper;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 线下专区广告展位 Dao
 */
@Repository
@Slf4j
public class AdPositionDao {

    @Autowired
    private AdPositionConvert adPositionConvert;

    @Autowired
    private OfflineZoneAdPositionMapper offlineZoneAdPositionMapper;
    @Autowired
    private OfflineZoneAdPositionExtMapper offlineZoneAdPositionExtMapper;

    /**
     * 新增广告展位
     *
     * @param param 新增参数
     * @return 新增结果的ID
     */
    @Transactional
    public long addAdPosition(AddAdPositionParam param) {
        OfflineZoneAdPosition createAdPositionEntity = adPositionConvert.toCreateAdPositionEntity(param);
        offlineZoneAdPositionMapper.insert(createAdPositionEntity);
        log.info("insert OfflineZoneAdPosition, entity={}", createAdPositionEntity);
        return createAdPositionEntity.getId();
    }

    /**
     * 根据ID获取广告展位实体
     *
     * @param id 广告展位ID
     * @return 广告展位实体
     */
    public OfflineZoneAdPosition getAdPosition(long id) {
        OfflineZoneAdPosition getById = new OfflineZoneAdPosition();
        getById.setId(id);
        return offlineZoneAdPositionMapper.selectByPrimaryKey(getById);
    }

    /**
     * 更新广告展位
     *
     * @param param 更新参数
     */
    @Transactional
    public void updateAdPosition(UpdateAdPositionParam param) {
        OfflineZoneAdPosition updateAdPositionEntity = adPositionConvert.toUpdateAdPositionEntity(param);
        int updateAdPositionRows = offlineZoneAdPositionMapper.updateByPrimaryKey(updateAdPositionEntity);
        log.info("update OfflineZoneAdPosition, entity={}, updateRows={}", updateAdPositionEntity, updateAdPositionRows);
    }

    /**
     * 删除广告展位
     *
     * @param param 删除参数
     */
    @Transactional
    public void deleteAdPosition(DeleteAdPositionParam param) {
        Long id = param.getId();
        String operator = SessionUtils.getAccount();
        int deleteAdPositionRows = offlineZoneAdPositionExtMapper.deleteById(id, operator);
        log.info("delete OfflineZoneAdPosition, deleteRows={}, id={}, operator={}", deleteAdPositionRows, id, operator);
    }

    /**
     * 列出广告展位列表
     *
     * @param param 查询参数
     * @return 广告展位列表
     */
    public List<OfflineZoneAdPosition> listAdPosition(ListAdPositionParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String deployEnv = ConfigUtils.getEnvRequired().name();
        List<OfflineZoneAdPosition> list = offlineZoneAdPositionExtMapper.listAdPosition(param, appId, deployEnv);
        log.debug("list OfflineZoneAdPosition, param={}, list={}", param, list);
        return list;
    }
}
