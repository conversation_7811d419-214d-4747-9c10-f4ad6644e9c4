package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 获取用户家族信息结果
 */
@Data
public class GetUserInFamilyResult {

    /**
     * 用户id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 波段号
     */
    private String band;

    /**
     * 昵称
     */
    private String name;

    /**
     * 头像
     */
    private String photo;

    /**
     * 家族id, 如果不在家族中则为空
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyId;

    /**
     * 是否为家族长
     */
    private Boolean isFamily;

    /**
     * 是否为厅主
     */
    private Boolean isRoom;

    /**
     * 是否为主播
     */
    private Boolean isPlayer;
}
