package fm.lizhi.ocean.wave.management.remote.service.anchor.singer;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestImportHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestOperateHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestPageHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponsePageHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerHallApplyService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 点唱厅考核
 * <AUTHOR>
 */
@Component
public class SingerHallApplyServiceRemote {

    @Autowired
    private SingerHallApplyService singerHallApplyService;


    /**
     * 点唱厅考核分页
     */
    public Result<ResponsePageHallApply> pageHallApplyList(RequestPageHallApply request) {
        return singerHallApplyService.pageHallApplyList(request);
    }

    /**
     * 点唱厅操作
     */
    public Result<Void> operateHallApply(RequestOperateHallApply request) {
        return singerHallApplyService.operateHallApply(request);
    }

    /**
     * 点唱厅导入
     */
    public Result<List<String>> importHallApply(RequestImportHallApply request) {
        Result<List<Long>> result = singerHallApplyService.importHallApply(request);
        if (ResultUtils.isFailure(result)) {
            return ResultUtils.failure(result.rCode(), result.getMessage());
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, CollectionUtils.isEmpty(result.target()) ?
                new ArrayList<>():
                result.target().stream().map(String::valueOf).collect(Collectors.toList()));
    }
}
