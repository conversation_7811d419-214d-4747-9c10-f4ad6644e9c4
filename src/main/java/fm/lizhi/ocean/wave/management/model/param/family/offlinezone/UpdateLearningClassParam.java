package fm.lizhi.ocean.wave.management.model.param.family.offlinezone;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 更新学习课堂参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UpdateLearningClassParam extends SaveLearningClassParam {

    /**
     * 资料ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;
}
