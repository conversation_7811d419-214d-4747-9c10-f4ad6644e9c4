package fm.lizhi.ocean.wave.management.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ActivityReportDataConvert;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityReportDataDetailResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityReportDataGiftResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityReportDataPlayerResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityReportDataSummaryResult;
import fm.lizhi.ocean.wave.management.remote.service.activitycenter.ActivityReportDataServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataDetailBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataGiftBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataPlayerBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataSummaryBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class ActivityReportDataManager {

    @Autowired
    private ActivityReportDataServiceRemote activityReportDataServiceRemote;

    public ResultVO<ActivityReportDataSummaryResult> getReportSummary(Long activityId, Integer appId){
        Result<ActivityReportDataSummaryBean> result = activityReportDataServiceRemote.getReportSummary(activityId, appId);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success(ActivityReportDataConvert.I.toActivityReportDataSummaryResult(result.target()));
    }

    public ResultVO<List<ActivityReportDataDetailResult>> getReportDetail(Long activityId, Integer appId){
        Result<List<ActivityReportDataDetailBean>> result = activityReportDataServiceRemote.getReportDetail(activityId, appId);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success(ActivityReportDataConvert.I.toActivityReportDataDetailResults(result.target()));
    }

    public ResultVO<PageVO<ActivityReportDataPlayerResult>> pageReportPlayer(Long activityId, Integer appId, int pageNo, int pageSize){
        Result<PageBean<ActivityReportDataPlayerBean>> result = activityReportDataServiceRemote.pageReportPlayer(activityId, appId, pageNo, pageSize);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }

        return ResultVO.success(ActivityReportDataConvert.I.toActivityReportDataPlayerResultPageVO(result.target()));
    }

    public ResultVO<PageVO<ActivityReportDataGiftResult>> pageReportGift(Long activityId, Integer appId, int pageNo, int pageSize){
        Result<PageBean<ActivityReportDataGiftBean>> result = activityReportDataServiceRemote.pageReportGift(activityId, appId, pageNo, pageSize);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success(ActivityReportDataConvert.I.toActivityReportDataGiftResultPageVO(result.target()));
    }

}
