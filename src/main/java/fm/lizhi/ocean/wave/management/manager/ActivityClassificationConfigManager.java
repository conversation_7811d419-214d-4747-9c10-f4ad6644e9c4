package fm.lizhi.ocean.wave.management.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ActivityClassificationConfigConvert;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveBigClassParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveClassificationParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateBigClassParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateClassificationParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityBigClassResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityClassConfigResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.remote.service.activitycenter.ActivityClassificationConfigServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityBigClassBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityClassConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityBigClass;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityClassification;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityBigClass;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityClassification;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityClassificationConfigManager {

    @Autowired
    private ActivityClassificationConfigServiceRemote activityClassificationConfigServiceRemote;

    /**
     * 保存大类
     */
    public ResultVO<Void> saveBigClass(SaveBigClassParam param, Integer appId, String operator) {
        RequestSaveActivityBigClass request = ActivityClassificationConfigConvert.I.toRequestSaveActivityBigClass(param, appId, operator);
        Result<Void> result = activityClassificationConfigServiceRemote.saveBigClass(request);

        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success(result.target());
    }

    /**
     * 更新大类
     */
    public ResultVO<Void> updateBigClass(UpdateBigClassParam param, String operator, Integer appId) {
        RequestUpdateActivityBigClass request = ActivityClassificationConfigConvert.I.toRequestUpdateActivityBigClass(param, operator, appId);
        Result<Void> result = activityClassificationConfigServiceRemote.updateBigClass(request);

        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success(result.target());
    }

    /**
     * 删除大类
     */
    public ResultVO<Void> deleteBigClass(Long id, Integer appId, String operator) {
        Result<Void> result = activityClassificationConfigServiceRemote.deleteBigClass(id, appId, operator);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success(result.target());
    }

    /**
     * 获取大类列表
     */
    public ResultVO<List<ActivityBigClassResult>> getBigClassList(Integer appId) {
        Result<List<ActivityBigClassBean>> result = activityClassificationConfigServiceRemote.getBigClassList(appId);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success(ActivityClassificationConfigConvert.I.toActivityBigClassResults(result.target()));

    }

    /**
     * 保存分类
     */
    public ResultVO<Void> saveClassification(SaveClassificationParam param, String operator, Integer appId) {
        RequestSaveActivityClassification request = ActivityClassificationConfigConvert.I.toRequestSaveActivityClassification(param, operator, appId);
        Result<Void> result = activityClassificationConfigServiceRemote.saveClassification(request);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success(result.target());
    }

    /**
     * 更新分类
     * @param param
     * @param operator
     * @return
     */
    public ResultVO<Void> updateClassification(UpdateClassificationParam param, String operator, Integer appId) {
        RequestUpdateActivityClassification request = ActivityClassificationConfigConvert.I.toRequestUpdateActivityClassification(param, operator, appId);
        Result<Void> result = activityClassificationConfigServiceRemote.updateClassification(request);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }

        return ResultVO.success(result.target());
    }

    /**
     * 删除分类
     * @param id
     * @param appId
     * @param operator
     * @return
     */
    public ResultVO<Void> deleteClassification(Long id, Integer appId, String operator) {
        Result<Void> result = activityClassificationConfigServiceRemote.deleteClassification(id, appId, operator);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }

        return ResultVO.success(result.target());
    }

    /**
     * 获取分类累表
     */
    public ResultVO<List<ActivityClassConfigResult>> getClassificationList(Integer appId, Long bigClassId){
        Result<List<ActivityClassConfigBean>> result = activityClassificationConfigServiceRemote.getClassificationList(appId, bigClassId);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }

        return ResultVO.success(ActivityClassificationConfigConvert.I.toActivityClassConfigResults(result.target()));

    }

}
