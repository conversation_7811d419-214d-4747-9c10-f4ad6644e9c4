package fm.lizhi.ocean.wave.management.model.converter.award.family;

import fm.lizhi.ocean.wave.management.model.param.award.family.CreateFamilyLevelAwardRuleParam;
import fm.lizhi.ocean.wave.management.model.param.award.family.DeleteFamilyLevelAwardRuleParam;
import fm.lizhi.ocean.wave.management.model.param.award.family.GetFamilyLevelAwardRuleParam;
import fm.lizhi.ocean.wave.management.model.param.award.family.UpdateFamilyLevelAwardRuleParam;
import fm.lizhi.ocean.wave.management.model.vo.award.family.GetFamilyLevelAwardRuleVO;
import fm.lizhi.ocean.wave.management.model.vo.award.family.ListFamilyLevelAwardRuleVO;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.ListFamilyLevelAwardRuleBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.*;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyLevelAwardRule;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface FamilyLevelAwardRuleConvert {

    FamilyLevelAwardRuleConvert I = Mappers.getMapper(FamilyLevelAwardRuleConvert.class);

    RequestCreateFamilyLevelAwardRule toRequestCreateFamilyLevelAwardRule(CreateFamilyLevelAwardRuleParam param, Integer appId, String operator);

    RequestUpdateFamilyLevelAwardRule toRequestUpdateFamilyLevelAwardRule(UpdateFamilyLevelAwardRuleParam param, Integer appId, String operator);

    RequestDeleteFamilyLevelAwardRule toRequestDeleteFamilyLevelAwardRule(DeleteFamilyLevelAwardRuleParam param, String operator);

    @Mapping(target = "appId", source = "appId")
    RequestListFamilyLevelAwardRule toRequestListFamilyLevelAwardRule(Integer appId);

    List<ListFamilyLevelAwardRuleVO> toListFamilyLevelAwardRuleVOS(List<ListFamilyLevelAwardRuleBean> beans);

    RequestGetFamilyLevelAwardRule toRequestGetFamilyLevelAwardRule(GetFamilyLevelAwardRuleParam param);

    GetFamilyLevelAwardRuleVO toGetFamilyLevelAwardRuleVO(ResponseGetFamilyLevelAwardRule response);
}
