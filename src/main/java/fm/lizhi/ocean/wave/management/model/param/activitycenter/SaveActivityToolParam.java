package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class SaveActivityToolParam {


    /**
     * 工具名称
     */
    @NotNull(message = "工具名称不能为空")
    private String name;

    /**
     * 类型, 1:玩法; 2: 工具
     */
    @NotNull(message = "类型不能为空")
    private Integer type;

    /**
     * 玩法工具描述
     */
    @NotNull(message = "工具描述不能为空")
    private String toolDesc;

    /**
     * 状态 0: 下架; 1: 上架
     */
    @NotNull(message = "状态不能为空")
    private Integer status;


}
