package fm.lizhi.ocean.wave.management.remote.service.user;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.JsonUtils;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.common.bean.ContextRequest;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyDetailBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserFamilyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 用户家族服务远程调用
 */
@Component
@Slf4j
public class UserFamilyServiceRemote {

    @Autowired
    private UserFamilyService userFamilyService;

    /**
     * 获取用户在家族中的信息
     *
     * @param appId  业务ID
     * @param userId 用户ID
     * @return 用户在家族中的信息
     */
    public Result<UserInFamilyBean> getUserInFamily(int appId, long userId) {
        log.info("getUserInFamily appId: {}, userId: {}", appId, userId);
        Result<UserInFamilyBean> result = userFamilyService.getUserInFamily(appId, userId);
        if (ResultUtils.isSuccess(result)) {
            log.info("getUserInFamily success, target: {}", JsonUtils.toJsonString(result.target()));
        } else {
            log.info("getUserInFamily fail, rCode: {}, message: {}", result.rCode(), result.getMessage());
        }
        return result;
    }

    public Result<UserInFamilyDetailBean> getUserInFamilyDetail(long userId, ContextRequest contextRequest){
        return userFamilyService.getUserInFamilyDetail(userId, contextRequest);
    }
}
