package fm.lizhi.ocean.wave.management.model.converter.activitycenter;

import fm.lizhi.ocean.wave.management.model.converter.CommonConvert;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.*;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.*;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.activitycenter.UserActivitySimpleInfoExportVO;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestQueryUserActivitiesBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityInfoDetail;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestCancelActivity;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseBatchActivityAuditData;
import fm.lizhi.ocean.wavecenter.api.user.bean.PlayerSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR
        , imports = {ActivityApplyTypeEnum.class}
        , uses = {CommonConvert.class}
)
public interface ActivityApplyConvert {

    ActivityApplyConvert I = Mappers.getMapper(ActivityApplyConvert.class);

    /**
     * 构建拒绝申请参数
     *
     * @param param 请求参数
     * @return 构建参数
     */
    RequestActivityAuditReject buildRejectParamBean(ActivityAuditRejectParam param, String operator);

    @Mappings({
            @Mapping(target = "applyBrand", source = "userActivitiesParamVO.applyUserBand"),
            @Mapping(target = "njBrand", source = "userActivitiesParamVO.applyHallBand"),
            @Mapping(target = "minStartTime", source = "userActivitiesParamVO.startTime"),
            @Mapping(target = "maxStartTime", source = "userActivitiesParamVO.endTime"),
            @Mapping(target = "pageParam.pageNo", source = "userActivitiesParamVO.pageNo"),
            @Mapping(target = "pageParam.pageSize", source = "userActivitiesParamVO.pageSize"),
            @Mapping(target = "classIds", source = "userActivitiesParamVO.classId"),
            @Mapping(target = "classId", ignore = true),
    })
    RequestQueryUserActivitiesBean userActivitiesParamVO2Bean(QueryUserActivitiesParamVO userActivitiesParamVO, Integer appId);

    @Mappings({
            @Mapping(target = "applyBrand", source = "userActivitiesParamVO.applyUserBand"),
            @Mapping(target = "njBrand", source = "userActivitiesParamVO.applyHallBand"),
            @Mapping(target = "minStartTime", source = "userActivitiesParamVO.startTime"),
            @Mapping(target = "maxStartTime", source = "userActivitiesParamVO.endTime"),
            @Mapping(target = "pageParam", ignore = true),
            @Mapping(target = "classIds", source = "userActivitiesParamVO.classId"),
            @Mapping(target = "classId", ignore = true),
    })
    RequestQueryUserActivitiesBean userActivitiesExportParamVO2Bean(QueryUserActivitiesExportParamVO userActivitiesParamVO, Integer appId);

    @Mappings({
            @Mapping(target = "applyHallHostBand", source = "njBrand"),
            @Mapping(target = "applyHallHost", source = "njName"),
            @Mapping(target = "applyUserBand", source = "applicantBand"),
            @Mapping(target = "applyUserName", source = "applicantName"),
            @Mapping(target = "applyFamilyId", source = "familyId"),
            @Mapping(target = "applyFamilyName", source = "familyName"),
            @Mapping(target = "applyHallHostId", source = "njId"),
            @Mapping(target = "applyUserId", source = "applicantUid")
    })
    UserActivitySimpleInfoResult userActivitySimpleInfoBean2Result(UserActivitySimpleInfoBean bean);

    List<UserActivitySimpleInfoResult> userActivitySimpleInfoBeanList2Results(List<UserActivitySimpleInfoBean> beans);

    @Mappings({
            @Mapping(target = "applyHallHostUserId", source = "njId"),
            @Mapping(target = "applyHallHostBand", source = "njBrand"),
            @Mapping(target = "applyHallHost", source = "njName"),
            @Mapping(target = "applyUserId", source = "applicantUid"),
            @Mapping(target = "applyUserBand", source = "applicantBand"),
            @Mapping(target = "applyUserName", source = "applicantName"),
            @Mapping(target = "applyFamilyId", source = "familyId"),
            @Mapping(target = "applyFamilyName", source = "familyName"),
            @Mapping(target = "activityStatusStr", source = "activityStatus", qualifiedByName = "toActivityStatusStr"),
            @Mapping(target = "auditStatusStr", source = "auditStatus", qualifiedByName = "toAuditStatusStr"),
            @Mapping(target = "applyTypeStr", source = "applyType", qualifiedByName = "toApplyTypeStr"),
            @Mapping(target = "duration", source = "bean", qualifiedByName = "toDuration"),
    })
    UserActivitySimpleInfoExportVO userActivitySimpleInfoBean2ExcelVO(UserActivitySimpleInfoBean bean);

    List<UserActivitySimpleInfoExportVO> userActivitySimpleInfoBeanList2ExcelVOs(List<UserActivitySimpleInfoBean> beans);

    @Named("toActivityStatusStr")
    default String toActivityStatusStr(Integer activityStatus) {
        if (activityStatus == null) {
            return null;
        }
        if (ActivityStatusEnum.UN_START.getStatus().equals(activityStatus)) {
            return "未开始";
        }
        if (ActivityStatusEnum.START.getStatus().equals(activityStatus)) {
            return "进行中";
        }
        if (ActivityStatusEnum.END.getStatus().equals(activityStatus)) {
            return "已结束";
        }
        if (ActivityStatusEnum.INVALID.getStatus().equals(activityStatus)) {
            return "无效";
        }
        return null;
    }

    @Named("toAuditStatusStr")
    default String toAuditStatusStr(Integer auditStatusStr) {
        if (auditStatusStr == null) {
            return null;
        }
        if (ActivityAuditStatusEnum.WAITING_AUDIT.getStatus().equals(auditStatusStr)) {
            return "等待审核";
        }
        if (ActivityAuditStatusEnum.AUDIT_PASS.getStatus().equals(auditStatusStr)) {
            return "审核通过";
        }
        if (ActivityAuditStatusEnum.AUDIT_REJECTED.getStatus().equals(auditStatusStr)) {
            return "审核拒绝";
        }
        if (ActivityAuditStatusEnum.USER_CANCEL.getStatus().equals(auditStatusStr)) {
            return "用户取消";
        }
        if (ActivityAuditStatusEnum.OFFICIAL_CANCEL.getStatus().equals(auditStatusStr)) {
            return "官方取消";
        }
        return null;
    }

    @Named("toApplyTypeStr")
    default String toApplyTypeStr(Integer applyType) {
        if (applyType == null) {
            return null;
        }
        if (ActivityApplyTypeEnum.NJ_APPLY.getApplyType().equals(applyType)) {
            return "用户提报";
        }
        if (ActivityApplyTypeEnum.OFFICIAL_APPLY.getApplyType().equals(applyType)) {
            return "官方提报";
        }
        return null;
    }

    @Named("toDuration")
    default Integer toDuration(UserActivitySimpleInfoBean bean) {
        if (bean == null) {
            return null;
        }
        if (bean.getStartTime() == null || bean.getEndTime() == null) {
            return null;
        }
        if (bean.getStartTime() != null && bean.getEndTime() != null) {
            return (int) ((bean.getEndTime() - bean.getStartTime()) / 1000 / 60);
        }
        return null;
    }

    @Mappings({
            @Mapping(target = "userId", source = "id"),

    })
    ActivityUserInfoResult userBean2ActivityUserInfoResult(UserBean bean);

    @Mappings({
            @Mapping(target = "resourceStartTime", source = "extra.startTime"),
            @Mapping(target = "resourceEndTime", source = "extra.endTime"),
            @Mapping(target = "imageUrl", source = "resourceImageUrl"),
            @Mapping(target = "officialSeat", source = "extra.seat"),
    })
    FlowResourceResult ActivityFlowResourceDetailBean2Result(ActivityFlowResourceDetailBean activityFlowResourceDetailBean);

    @Mappings({
            @Mapping(target = "cover", source = "posterUrl"),
            @Mapping(target = "flowResources", source = "flowResourceDetails"),
            @Mapping(target = "activityTools", source = "activityToolList"),
            @Mapping(target = "njInfo.userId", source = "njInfo.id"),

    })
    UserActivityDetailResult userActivityDetailResult2Result(ResponseActivityInfoDetail responseActivityInfoDetail);

    BackGroundInfoResult decorateBean2BackGroundInfoResult(DecorateBean bean);

    List<BackGroundInfoResult> decorateBeans2BackGroundInfoResults(List<DecorateBean> beans);

    RequestActivityAuditAgree agreeActivityApplyParam2Bean(ActivityAuditAgreeParam activityAuditAgreeParam, Integer appId, String operator);

    @Mapping(target = "id", source = "userBean.id")
    @Mapping(target = "isFamily", source = "userInFamilyBean.family")
    @Mapping(target = "isRoom", source = "userInFamilyBean.room")
    @Mapping(target = "isPlayer", source = "userInFamilyBean.player")
    GetUserInFamilyResult toGetUserInFamilyResult(UserBean userBean, UserInFamilyBean userInFamilyBean);

    ListRoomPlayerResult toListRoomPlayerResult(PlayerSignBean playerSignBean);

    @Mapping(target = "applyType", expression = "java(ActivityApplyTypeEnum.OFFICIAL_APPLY)")
    @Mapping(target = "maxSeatCount", ignore = true)
    @Mapping(target = "applicantUid", source = "param.njId")
    @Mapping(target = "auxiliaryPropUrl", source = "param.auxiliaryPropUrls")
    @Mapping(target = "activityTool", source = "param.activityTools")
    @Mapping(target = "processList", source = "param.processes", qualifiedByName = "toRequestActivityApplyProcessBeans")
    @Mapping(target = "roomAnnouncementImgUrl", source = "param.roomAnnouncementImages")
    @Mapping(target = "activityId", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "model", ignore = true)
    RequestActivityApplyBean toRequestActivityApplyBean(CreateActivityApplyParam param, Integer appId);

    @Named("toRequestActivityApplyProcessBeans")
    default List<ActivityProcessBean> toRequestActivityApplyProcessBeans(List<CreateActivityApplyParam.Process> processes) {
        if (CollectionUtils.isEmpty(processes)) {
            return Collections.emptyList();
        }
        ArrayList<ActivityProcessBean> activityProcessBeans = new ArrayList<>(processes.size());
        for (CreateActivityApplyParam.Process process : processes) {
            activityProcessBeans.add(toRequestActivityApplyProcessBean(process));
        }
        return activityProcessBeans;
    }

    @Mapping(target = "id", ignore = true)
    ActivityProcessBean toRequestActivityApplyProcessBean(CreateActivityApplyParam.Process process);

    @BeanMapping(unmappedSourcePolicy = ReportingPolicy.ERROR, unmappedTargetPolicy = ReportingPolicy.IGNORE)
    @Mapping(target = "pageParam.pageNo", source = "param.pageNo")
    @Mapping(target = "pageParam.pageSize", source = "param.pageSize")
    RequestQueryActivityListBean toRequestQueryActivityListBean(PageActivityCalendarParam param, Integer appId);

    PageVO<PageActivityCalendarResult> toPageActivityCalendarResultPageVO(ResponseQueryActivityListBean responseBean);

    @Mapping(target = "startDate", expression = "java(new java.util.Date(param.getStartDate()))")
    @Mapping(target = "endDate", expression = "java(new java.util.Date(param.getEndDate()))")
    RequestGetOfficialSeatTimeBean toRequestGetOfficialSeatTimeBean(ListOfficialSeatParam param, Integer appId);

    ListOfficialSeatResult toListOfficialSeatResult(ResponseGetOfficialSeatTimeBean resp);

    RequestCancelActivity toRequestActivityCancelBean(CancelActivityParam param, Integer appId, String operator);

    @Mappings({
            @Mapping(target = "processList", source = "param.processes"),
            @Mapping(target = "roomAnnouncementImgUrl", source = "param.roomAnnouncementImages"),
            @Mapping(target = "activityTool", source = "param.activityTools"),
            @Mapping(target = "auxiliaryPropUrl", source = "param.auxiliaryPropUrls"),
            @Mapping(target = "applyType", expression = "java(fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum.getApplyTypeEnum(param.getApplyType()))"),
            @Mapping(target = "model", ignore = true),
            @Mapping(target = "giftIds", ignore = true),
            @Mapping(target = "roomMarkId", ignore = true),
            @Mapping(target = "roomMarkUrl", ignore = true)
    })
    RequestActivityModifyBean toRequestActivityModifyBean(ModifyActivityApplyParam param, Integer appId);

    /**
     * 批量同意活动申请参数转换
     */
    RequestBatchActivityAuditAgree batchAgreeActivityApplyParam2Beans(BatchActivityAuditAgreeParam param, Integer appId, String operator);

    /**
     * 批量拒绝活动申请参数转换
     */
    RequestBatchActivityAuditReject batchRejectActivityApplyParam2Beans(BatchActivityAuditRejectParam param, Integer appId, String operator);

    BatchActivityAuditResult toBatchActivityAuditResult(ResponseBatchActivityAuditData target);
}
