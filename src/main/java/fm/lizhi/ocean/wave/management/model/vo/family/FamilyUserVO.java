package fm.lizhi.ocean.wave.management.model.vo.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/4/10 17:52
 */
@Data
@Accessors(chain = true)
public class FamilyUserVO extends FamilyVO{

    /**
     * 公会ID, 冗余一个额外的字段，为了前端统一
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyId;

    /**
     * 家族长ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyUserId;

    /**
     * 家族长名称
     */
    private String familyUserName;

}
