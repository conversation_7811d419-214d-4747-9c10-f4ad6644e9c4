package fm.lizhi.ocean.wave.management.manager.singer.handler;

import fm.lizhi.ocean.wave.management.manager.common.handler.BaseHandlerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class SingerAuditStatusHandlerFactory extends BaseHandlerFactory<Integer, SingerAuditStatusHandler> {

    @Autowired
    private List<SingerAuditStatusHandler> filters;

    @Override
    public void registerAllHandlers() {
        for (SingerAuditStatusHandler handler : filters) {
            registerHandler(handler.getAuditStatusEnum().getStatus(), handler);
        }
    }
}
