package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import lombok.Data;

import java.util.List;

/**
 * @description:活动申请同意入参
 * @author: guoyibin
 * @create: 2024/10/24 20:38
 */
@Data
public class ActivityAuditAgreeParam {

    private Long activityId;

    private List<FlowResourceInfo> flowResourceList;

    private Integer version;

    @Data
    public static class FlowResourceInfo {
        /**
         * 申请资源ID1
         */
        private Long flowResourceId;

        /**
         * 审批状态
         */
        private Integer status;
    }
}
