package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * @description: 流量资源
 * @author: guoyibin
 * @create: 2024/10/23 21:44
 */
@Data
public class FlowResourceResult {

    /**
     * 资源ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 资源图片地址
     */
    private String imageUrl;

    /**
     * 资源开始时间
     */
    private Long resourceStartTime;

    /**
     * 资源结束时间
     */
    private Long resourceEndTime;

    /**
     * 资源编码
     */
    private String resourceCode;

    /**
     * 审核状态
     */
    private Integer resourceAuditStatus;

    /**
     * 资源发放状态
     * 发放状态，0：未发放，1：发放失败，2：发放成功
     */
    private Integer giveStatus;

    /**
     * 官频位座位号, 当资源类型为官频位时, 该字段有值
     */
    private Integer officialSeat;
}
