package fm.lizhi.ocean.wave.management.controller.activitycenter.ai;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.godzilla.api.constant.ActivityPlanImageSceneEnum;
import fm.lizhi.ocean.godzilla.api.constant.ActivityPlanImageStyleEnum;
import fm.lizhi.ocean.godzilla.api.model.request.activityplan.*;
import fm.lizhi.ocean.godzilla.api.model.response.activityplan.*;
import fm.lizhi.ocean.godzilla.api.service.AiActivityPlanImageService;
import fm.lizhi.ocean.wave.management.config.apollo.ActivityConfig;
import fm.lizhi.ocean.wave.management.constants.activitycenter.ai.BusinessImageTypeEnum;
import fm.lizhi.ocean.wave.management.manager.common.CommonCdnManager;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ai.ActivityAiConvert;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.ai.*;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ai.*;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.service.DecorateManagementService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeoutException;

@RestController
@RequestMapping("/activity/ai")
@Slf4j
public class ActivityAiController {

    @Autowired
    private ActivityConfig activityConfig;

    @Autowired
    private ActivityAiConvert activityAiConvert;

    @Autowired
    private AiActivityPlanImageService aiActivityPlanImageService;

    @Autowired
    private CommonCdnManager commonCdnManager;

    @Autowired
    private DecorateManagementService decorateManagementService;

    /**
     * 获取AI图片风格枚举
     *
     * @return AI图片风格枚举
     */
    @GetMapping("/getImageStyles")
    public ResultVO<GetAiImageStylesResult> getAiImageStyles() {
        // 如果配置不为空，则优先使用配置中的风格
        try {
            List<ActivityConfig.Ai.ImageStyle> imageStyleConfigs = activityConfig.getAi().getImageStyles();
            if (CollectionUtils.isNotEmpty(imageStyleConfigs)) {
                List<GetAiImageStylesResult.ImageStyle> aiImageStyles = activityAiConvert.toAiImageStyles(imageStyleConfigs);
                GetAiImageStylesResult getAiImageStylesResult = new GetAiImageStylesResult();
                getAiImageStylesResult.setImageStyles(aiImageStyles);
                return ResultVO.success(getAiImageStylesResult);
            }
        } catch (Exception e) {
            log.warn("getAiImageStyles from config failed", e);
        }
        // 配置为空或获取失败，使用枚举值
        ActivityPlanImageStyleEnum[] imageStyleEnums = ActivityPlanImageStyleEnum.values();
        GetAiImageStylesResult getAiImageStylesResult = activityAiConvert.toGetAiImageStylesResult(imageStyleEnums);
        return ResultVO.success(getAiImageStylesResult);
    }

    /**
     * 获取AI图片场景枚举
     *
     * @return AI图片场景枚举
     */
    @GetMapping("/getImageScenes")
    public ResultVO<GetAiImageScenesResult> getAiImageScenes() {
        // 如果配置不为空，则优先使用配置中的场景
        try {
            List<ActivityConfig.Ai.ImageScene> imageSceneConfigs = activityConfig.getAi().getImageScenes();
            if (CollectionUtils.isNotEmpty(imageSceneConfigs)) {
                List<GetAiImageScenesResult.ImageScene> aiImageScenes = activityAiConvert.toAiImageScenes(imageSceneConfigs);
                GetAiImageScenesResult getAiImageScenesResult = new GetAiImageScenesResult();
                getAiImageScenesResult.setImageScenes(aiImageScenes);
                return ResultVO.success(getAiImageScenesResult);
            }
        } catch (Exception e) {
            log.warn("getAiImageScenes from config failed", e);
        }
        // 配置为空或获取失败，使用枚举值
        ActivityPlanImageSceneEnum[] imageSceneEnums = ActivityPlanImageSceneEnum.values();
        GetAiImageScenesResult getAiImageScenesResult = activityAiConvert.toGetAiImageScenesResult(imageSceneEnums);
        return ResultVO.success(getAiImageScenesResult);
    }

    /**
     * 润色文生图提示词
     *
     * @param param 润色文生图提示词参数
     * @return 润色文生图提示词结果
     */
    @PostMapping("/polishTextToImagePrompt")
    public ResultVO<PolishTextToImagePromptResult> polishTextToImagePrompt(@RequestBody @Validated PolishTextToImagePromptParam param) {
        log.info("polishTextToImagePrompt, param: {}", param);
        try {
            RequestActivityPlanPolishTextToImagePrompt request = activityAiConvert.toRequestActivityPlanPolishTextToImagePrompt(param);
            Result<ResponseActivityPlanPolishTextToImagePrompt> result = aiActivityPlanImageService.polishTextToImagePrompt(request);
            if (ResultUtils.isFailure(result)) {
                log.info("invoke polishTextToImagePrompt fail, rCode: {}, message: {}", result.rCode(), result.getMessage());
                return ResultVO.failure(result);
            }
            ResponseActivityPlanPolishTextToImagePrompt response = result.target();
            log.info("invoke polishTextToImagePrompt success, response: {}", response);
            PolishTextToImagePromptResult polishTextToImagePromptResult = activityAiConvert.toPolishTextToImagePromptResult(response);
            return ResultVO.success(polishTextToImagePromptResult);
        } catch (RuntimeException e) {
            if (ExceptionUtils.indexOfThrowable(e, TimeoutException.class) >= 0) {
                log.debug("invoke polishTextToImagePrompt timeout", e);
                log.info("invoke polishTextToImagePrompt timeout, message: {}", e.getMessage());
                return ResultVO.failure("请求超时，请稍后再试");
            }
            log.error("invoke polishTextToImagePrompt error", e);
            return ResultVO.failure("请求失败，请稍后再试");
        }
    }

    /**
     * 文生图
     *
     * @param param 文生图参数
     * @return 文生图结果
     */
    @PostMapping("/textToImage")
    public ResultVO<TextToImageResult> textToImage(@RequestBody @Validated TextToImageParam param) {
        log.info("textToImage, param: {}", param);
        try {
            RequestActivityPlanSubmitTextToImageTask request = activityAiConvert.toRequestActivityPlanSubmitTextToImageTask(param);
            Result<ResponseActivityPlanSubmitTextToImageTask> result = aiActivityPlanImageService.submitTextToImageTask(request);
            if (ResultUtils.isFailure(result)) {
                log.info("invoke textToImage fail, rCode: {}, message: {}", result.rCode(), result.getMessage());
                return ResultVO.failure(result);
            }
            ResponseActivityPlanSubmitTextToImageTask response = result.target();
            log.info("invoke textToImage success, response: {}", response);
            TextToImageResult textToImageResult = activityAiConvert.toTextToImageResult(response);
            return ResultVO.success(textToImageResult);
        } catch (RuntimeException e) {
            if (ExceptionUtils.indexOfThrowable(e, TimeoutException.class) >= 0) {
                log.debug("invoke textToImage timeout", e);
                log.info("invoke textToImage timeout, message: {}", e.getMessage());
                return ResultVO.failure("请求超时，请稍后再试");
            }
            log.error("invoke textToImage error", e);
            return ResultVO.failure("请求失败，请稍后再试");
        }
    }

    /**
     * 部分擦除
     *
     * @param param 部分擦除参数
     * @return 部分擦除结果
     */
    @PostMapping("/erasureImage")
    public ResultVO<ErasureImageResult> erasureImage(@RequestBody @Validated ErasureImageParam param) {
        log.info("erasureImage, param: {}", param);
        try {
            RequestActivityPlanSubmitErasureImageTask request = activityAiConvert.toRequestActivityPlanSubmitErasureImageTask(param);
            Result<ResponseActivityPlanSubmitErasureImageTask> result = aiActivityPlanImageService.submitErasureImageTask(request);
            if (ResultUtils.isFailure(result)) {
                log.info("invoke erasureImage fail, rCode: {}, message: {}", result.rCode(), result.getMessage());
                return ResultVO.failure(result);
            }
            ResponseActivityPlanSubmitErasureImageTask response = result.target();
            log.info("invoke erasureImage success, response: {}", response);
            if (!response.getResultImage().isSuccess()) {
                return activityAiConvert.toFailedErasureImageResultVO(response);
            }
            ErasureImageResult erasureImageResult = activityAiConvert.toSuccessErasureImageResult(response);
            return ResultVO.success(erasureImageResult);
        } catch (RuntimeException e) {
            if (ExceptionUtils.indexOfThrowable(e, TimeoutException.class) >= 0) {
                log.debug("invoke erasureImage timeout", e);
                log.info("invoke erasureImage timeout, message: {}", e.getMessage());
                return ResultVO.failure("请求超时，请稍后再试");
            }
            log.error("invoke erasureImage error", e);
            return ResultVO.failure("请求失败，请稍后再试");
        }
    }

    /**
     * 调整尺寸
     *
     * @param param 调整尺寸参数
     * @return 调整尺寸结果
     */
    @PostMapping("/expandImage")
    public ResultVO<ExpandImageResult> expandImage(@RequestBody @Validated ExpandImageParam param) {
        log.info("expandImage, param: {}", param);
        try {
            RequestActivityPlanSubmitExpandImageTask request = activityAiConvert.toRequestActivityPlanSubmitExpandImageTask(param);
            Result<ResponseActivityPlanSubmitExpandImageTask> result = aiActivityPlanImageService.submitExpandImageTask(request);
            if (ResultUtils.isFailure(result)) {
                log.info("invoke expandImage fail, rCode: {}, message: {}", result.rCode(), result.getMessage());
                return ResultVO.failure(result);
            }
            ResponseActivityPlanSubmitExpandImageTask response = result.target();
            log.info("invoke expandImage success, response: {}", response);
            if (!response.getResultImage().isSuccess()) {
                return activityAiConvert.toFailedExpandImageResultVO(response);
            }
            ExpandImageResult expandImageResult = activityAiConvert.toSuccessExpandImageResult(response);
            return ResultVO.success(expandImageResult);
        } catch (RuntimeException e) {
            if (ExceptionUtils.indexOfThrowable(e, TimeoutException.class) >= 0) {
                log.debug("invoke expandImage timeout", e);
                log.info("invoke expandImage timeout, message: {}", e.getMessage());
                return ResultVO.failure("请求超时，请稍后再试");
            }
            log.error("invoke expandImage error", e);
            return ResultVO.failure("请求失败，请稍后再试");
        }
    }

    /**
     * 背景去除
     *
     * @param param 背景去除参数
     * @return 背景去除结果
     */
    @PostMapping("/removeImageBackground")
    public ResultVO<RemoveImageBackgroundResult> removeImageBackground(@RequestBody @Validated RemoveImageBackgroundParam param) {
        log.info("removeImageBackground, param: {}", param);
        try {
            RequestActivityPlanSubmitRemoveImageBackgroundTask request = activityAiConvert.toRequestActivityPlanSubmitRemoveImageBackgroundTask(param);
            Result<ResponseActivityPlanSubmitRemoveImageBackgroundTask> result = aiActivityPlanImageService.submitRemoveImageBackgroundTask(request);
            if (ResultUtils.isFailure(result)) {
                log.info("invoke removeImageBackground fail, rCode: {}, message: {}", result.rCode(), result.getMessage());
                return ResultVO.failure(result);
            }
            ResponseActivityPlanSubmitRemoveImageBackgroundTask response = result.target();
            log.info("invoke removeImageBackground success, response: {}", response);
            RemoveImageBackgroundResult removeImageBackgroundResult = activityAiConvert.toRemoveImageBackgroundResult(response);
            return ResultVO.success(removeImageBackgroundResult);
        } catch (RuntimeException e) {
            if (ExceptionUtils.indexOfThrowable(e, TimeoutException.class) >= 0) {
                log.debug("invoke removeImageBackground timeout", e);
                log.info("invoke removeImageBackground timeout, message: {}", e.getMessage());
                return ResultVO.failure("请求超时，请稍后再试");
            }
            log.error("invoke removeImageBackground error", e);
            return ResultVO.failure("请求失败，请稍后再试");
        }
    }

    /**
     * 保存业务图片素材
     *
     * @param param 保存业务图片素材参数
     * @return 保存业务图片素材结果
     */
    @PostMapping("saveBusinessImage")
    public ResultVO<SaveBusinessImageResult> saveBusinessImage(@RequestBody @Validated SaveBusinessImageParam param) {
        log.info("saveBusinessImage, param: {}", param);
        Integer appId = SessionExtUtils.getAppId();
        Validate.notNull(appId);
        Integer imageType = param.getImageType();
        // 房间背景
        if (Objects.equals(imageType, BusinessImageTypeEnum.ROOM_BACKGROUND.getValue())) {
            return createRoomBackground(appId, param);
        }
        // 头像框
        if (Objects.equals(imageType, BusinessImageTypeEnum.AVATAR_WIDGET.getValue())) {
            return createAvatarWidget(appId, param);
        }
        return ResultVO.failure("暂不支持该类型的素材保存, imageType: " + imageType);
    }

    private ResultVO<SaveBusinessImageResult> createRoomBackground(Integer appId, SaveBusinessImageParam param) {
        // 先转存素材资源
        Result<SaveBusinessImageParam.RoomBackground> transferResult = commonCdnManager.transferWaveToBusinessByBean(param.getRoomBackground(), appId);
        if (ResultUtils.isFailure(transferResult)) {
            return ResultVO.failure(transferResult);
        }
        SaveBusinessImageParam.RoomBackground roomBackground = transferResult.target();
        // 再请求保存素材
        RequestCreateRoomBackground request = activityAiConvert.toRequestCreateRoomBackground(appId, roomBackground);
        Result<ResponseCreateRoomBackground> createResult = decorateManagementService.createRoomBackground(request);
        int rCode = createResult.rCode();
        if (rCode == CommonService.PARAM_ERROR) {
            return ResultVO.failure(StringUtils.defaultIfBlank(createResult.getMessage(), "参数错误"));
        }
        if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return ResultVO.failure(StringUtils.defaultIfBlank(createResult.getMessage(), "保存失败"));
        }
        ResponseCreateRoomBackground createResponse = createResult.target();
        return ResultVO.success(activityAiConvert.toSaveBusinessImageResult(createResponse));
    }

    private ResultVO<SaveBusinessImageResult> createAvatarWidget(Integer appId, SaveBusinessImageParam param) {
        // 先转存素材资源
        Result<SaveBusinessImageParam.AvatarWidget> transferResult = commonCdnManager.transferWaveToBusinessByBean(param.getAvatarWidget(), appId);
        if (ResultUtils.isFailure(transferResult)) {
            return ResultVO.failure(transferResult);
        }
        SaveBusinessImageParam.AvatarWidget avatarWidget = transferResult.target();
        // 再请求保存素材
        RequestCreateAvatarWidget request = activityAiConvert.toRequestCreateAvatarWidget(appId, avatarWidget);
        Result<ResponseCreateAvatarWidget> createResult = decorateManagementService.createAvatarWidget(request);
        int rCode = createResult.rCode();
        if (rCode == CommonService.PARAM_ERROR) {
            return ResultVO.failure(StringUtils.defaultIfBlank(createResult.getMessage(), "参数错误"));
        }
        if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return ResultVO.failure(StringUtils.defaultIfBlank(createResult.getMessage(), "保存失败"));
        }
        ResponseCreateAvatarWidget createResponse = createResult.target();
        return ResultVO.success(activityAiConvert.toSaveBusinessImageResult(createResponse));
    }
}
