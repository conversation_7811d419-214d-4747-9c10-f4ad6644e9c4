package fm.lizhi.ocean.wave.management.model.result.utility.webp;

import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.platform.api.platform.webp.constant.WebpConvertQualityOptionEnum;
import lombok.Data;
import org.apache.commons.lang3.math.NumberUtils;

import java.beans.Transient;
import java.util.Objects;

/**
 * webp异步转换结果
 */
@Data
public class WebpAsyncConvertResult {

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 业务请求id, 由业务方生成并保证唯一
     */
    private String bizRequestId;

    /**
     * 业务类型, 由业务方定义, 用于分类
     */
    private String bizType;

    /**
     * 源文件类型
     */
    private String sourceType;

    /**
     * 源文件路径, 斜杆开头的相对路径
     */
    private String sourcePath;

    /**
     * 转换质量选项
     *
     * @see WebpConvertQualityOptionEnum
     */
    private Integer qualityOption;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 错误消息
     */
    private String msg;

    /**
     * 转换后的webp文件路径, 斜杆开头的相对路径
     */
    private String webpPath;

    /**
     * 转换后的webp文件SHA256值
     */
    private String webpSha;

    @Transient
    public boolean isFailure() {
        return !Objects.equals(code, (int) GeneralRCode.GENERAL_RCODE_SUCCESS);
    }
}
