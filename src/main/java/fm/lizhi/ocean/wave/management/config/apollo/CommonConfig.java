package fm.lizhi.ocean.wave.management.config.apollo;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;

/**
 * 公共配置
 */
@ConfigurationProperties(prefix = "common")
@Data
public class CommonConfig {

    /**
     * restTemplate配置
     */
    private RestTemplate restTemplate = new RestTemplate();

    /**
     * Wave CDN配置
     */
    private WaveCdn waveCdn = new WaveCdn();

    /**
     * restTemplate配置
     */
    @Data
    public static class RestTemplate {

        /**
         * 最大连接数
         */
        private int maxRequests = 1000;

        /**
         * 最大每个主机连接数
         */
        private int maxRequestsPerHost = 20;

        /**
         * 连接超时时间
         */
        private Duration connectTimeout = Duration.ofSeconds(10);

        /**
         * 读取超时时间
         */
        private Duration readTimeout = Duration.ofSeconds(60);

        /**
         * 写入超时时间
         */
        private Duration writeTimeout = Duration.ofSeconds(60);
    }

    /**
     * Wave CDN配置
     */
    @Data
    public static class WaveCdn {

        /**
         * 应用id
         */
        private Integer appId = 1;

        /**
         * 应用名称
         */
        private String hostApp = "";

        /**
         * 罗马上传地址, 例如{@code https://upload-wavecenter.lzpscn1.com}, 实际上只有前端使用, 这里只是为了方便比对
         */
        private String romeAddress = "http://romefs.yfxn.lizhi.fm";

        /**
         * 罗马上传内网地址, 例如{@code http://upload-wavecenter.lzfm.com/}
         */
        private String romeInnerAddress = "http://romefs.yfxn.lizhi.fm";

        /**
         * CDN地址, 例如{@code https://download-wavecenter.lzpscn1.com}
         */
        private String cdnHost = "http://romefs.yfxn.lizhi.fm";

        /**
         * CDN内网地址, 例如{@code http://download-wavecenter.lzfm.com}
         */
        private String cdnInnerHost = "http://romefs.yfxn.lizhi.fm";

        /**
         * CDN外网地址, 给第三方访问, 例如{@code http://cdnofficeout.lz225.com}. 如果为空则取{@link #cdnHost}相同配置.
         */
        private String cdnOuterHost = "http://cdnofficeout.lz225.com";
    }
}
