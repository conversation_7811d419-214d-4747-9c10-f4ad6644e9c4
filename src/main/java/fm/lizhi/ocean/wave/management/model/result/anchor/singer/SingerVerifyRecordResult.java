package fm.lizhi.ocean.wave.management.model.result.anchor.singer;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.OriginalSingerInfoVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerFamilyInfoVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerUserInfoVO;
import lombok.Data;


@Data
public class SingerVerifyRecordResult {

    /**
     * 记录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

     /**
     * 歌手信息
     */
    private SingerUserInfoVO userInfo;

    /**
     * 家族信息
     */
    private SingerFamilyInfoVO familyInfo;

    /**
     * 歌曲名称
     */
    private String songName;

    /**
     * 歌曲风格
     */
    private String songStyle;

    /**
     * 签约信息
     */
    private SingerUserInfoVO njInfo;

    /**
     * 原唱歌手信息
     */
    private OriginalSingerInfoVO originalSingerInfo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 是否通过审核
     */
    private Boolean hasPassVerify;

    /**
     * 是否在黑名单中
     */
    private Boolean inBlackList;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 审核时间
     */
    private Long auditTime;

    /**
     * 音频地址
     */
    private String audioPath;

    /**
     * 预审核不过原因
     */
    private String preAuditRejectReason;

    /**
     * 联系方式
     */
    private String contactNumber;
}
