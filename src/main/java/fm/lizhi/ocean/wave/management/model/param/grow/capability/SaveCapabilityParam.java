package fm.lizhi.ocean.wave.management.model.param.grow.capability;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025/5/30 17:49
 */
@Data
public class SaveCapabilityParam {

    /**
     * 仅编辑的时候需要
     */
    private Long id;

    /**
     * 能力名称
     */
    @NotBlank(message = "能力名称不可为空")
    private String name;

    /**
     * 能力项编码
     */
    @NotBlank(message = "能力项编码不可为空")
    private String code;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 操作人
     */
    private String operator;

}
