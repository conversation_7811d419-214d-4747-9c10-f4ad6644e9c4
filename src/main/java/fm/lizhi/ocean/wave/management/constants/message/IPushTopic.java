package fm.lizhi.ocean.wave.management.constants.message;

import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2024/4/12 17:44
 */
public interface IPushTopic {

    String getPrefix();

    String getName();

    default String getKey(Object... args) {
        StringJoiner sj = new StringJoiner("_");
        sj.add(this.getPrefix());
        sj.add(this.getName());
        for (Object o : args) {
            sj.add(o.toString());
        }
        return sj.toString();
    }
}
