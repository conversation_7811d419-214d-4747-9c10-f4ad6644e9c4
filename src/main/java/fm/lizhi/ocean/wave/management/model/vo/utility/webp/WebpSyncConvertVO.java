package fm.lizhi.ocean.wave.management.model.vo.utility.webp;

import fm.lizhi.ocean.wave.platform.api.platform.webp.constant.WebpConvertQualityOptionEnum;
import lombok.Data;

import java.beans.Transient;

/**
 * webp同步转换结果VO
 */
@Data
public class WebpSyncConvertVO {

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 业务请求id, 由业务方生成并保证唯一
     */
    private String bizRequestId;

    /**
     * 业务类型, 由业务方定义, 用于分类
     */
    private String bizType;

    /**
     * 源文件类型
     */
    private String sourceType;

    /**
     * 源文件路径, 斜杆开头的相对路径
     */
    private String sourcePath;

    /**
     * 转换质量选项
     *
     * @see WebpConvertQualityOptionEnum
     */
    private Integer qualityOption;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 错误消息
     */
    private String msg;

    /**
     * 转换后的webp文件路径, 斜杆开头的相对路径
     */
    private String webpPath;

    /**
     * 转换后的webp文件URL
     */
    private String webpUrl;

    /**
     * 转换后的webp文件SHA256值
     */
    private String webpSha;

    @Transient
    public boolean isSuccess() {
        return code == 0;
    }

    @Transient
    public boolean isFailure() {
        return !isSuccess();
    }
}
