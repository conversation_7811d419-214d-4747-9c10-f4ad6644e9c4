package fm.lizhi.ocean.wave.management.model.vo.grow.capability;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/5/30 18:03
 */
@Data
@Accessors(chain = true)
public class CapabilityManageVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 能力项编码
     */
    private String code;

    /**
     * 最近修改人
     */
    private String modifyUser;

}
