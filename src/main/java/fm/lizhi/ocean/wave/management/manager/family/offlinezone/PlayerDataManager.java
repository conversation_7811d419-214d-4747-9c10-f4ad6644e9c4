package fm.lizhi.ocean.wave.management.manager.family.offlinezone;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.dao.PlayerDataDao;
import fm.lizhi.ocean.wave.management.manager.UserManager;
import fm.lizhi.ocean.wave.management.model.converter.family.offlinezone.PlayerDataConvert;
import fm.lizhi.ocean.wave.management.model.dto.family.offlinezone.ListPlayerDataDTO;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.BatchResetPlayerDataParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.ListPlayerDataParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListPlayerDataResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneProtectionBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneProtectionWithStatusBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.ProtectionStatusEnums;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestBatchResetAgreementValidity;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneProtectionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 线下主播数据业务逻辑层
 * <AUTHOR>
 */
@Slf4j
@Service
public class PlayerDataManager {

    @Autowired
    private PlayerDataDao playerDataDao;

    @Autowired
    private OfflineZoneProtectionService offlineZoneProtectionService;

    @Autowired
    private UserManager userManager;

    @Autowired
    private UserCommonService userCommonService;

    /**
     * 分页查询线下主播数据列表（带保护状态）
     *
     * @param param 查询参数
     * @return 分页结果
     */
    public ResultVO<PageVO<ListPlayerDataResult>> listPlayerDataWithProtection(ListPlayerDataParam param) {
        try {
            // 参数校验
            if (param == null) {
                return ResultVO.failure("查询参数不能为空");
            }
            
            if (param.getAppId() == null) {
                return ResultVO.failure("应用ID不能为空");
            }

            if (StringUtils.isNotEmpty(param.getPlayerBand())){
                Optional<UserBean> user = userManager.getUserByBand(param.getAppId(), param.getPlayerBand());
                if (!user.isPresent()) {
                    log.info("get user by band fail, band: {}", param.getPlayerBand());
                    return ResultVO.success(PageVO.of(0, new ArrayList<>()));
                }

                user.ifPresent(userBean -> param.setPlayerId(userBean.getId()));
            }

            if (StringUtils.isNotEmpty(param.getNjBand())){
                Optional<UserBean> user = userManager.getUserByBand(param.getAppId(), param.getNjBand());
                if (!user.isPresent()) {
                    log.info("get nj by band fail, band: {}", param.getNjBand());
                    return ResultVO.success(PageVO.of(0, new ArrayList<>()));
                }
                user.ifPresent(userBean -> param.setNjId(userBean.getId()));
            }
            
            // 设置默认时间范围为上周（如果未指定时间）
            if (param.getStartWeekDate() == null && param.getEndWeekDate() == null) {
                setDefaultLastWeekTimeRange(param);
            }
            
            // 执行查询
            PageList<ListPlayerDataDTO> pageList = playerDataDao.listPlayerDataWithProtection(param);
            
            // 处理查询结果
            if (pageList != null && !pageList.isEmpty()) {
                // 处理保护状态逻辑
                processProtectionStatus(pageList, param);

                Set<Long> userIdSet = pageList.stream().map(ListPlayerDataDTO::getUserId).collect(Collectors.toSet());
                Set<Long> njIdSet = pageList.stream().map(ListPlayerDataDTO::getNjId).collect(Collectors.toSet());
                userIdSet.addAll(njIdSet);

                Result<List<UserBean>> userListResult = userCommonService.getUserByIds(param.getAppId(), new ArrayList<>(userIdSet));
                List<UserBean> userBeanList = new ArrayList<>();
                if (RpcResult.isFail(userListResult)) {
                    log.warn("查询用户信息失败, userIdSet: {}", userIdSet);
                } else {
                    userBeanList = userListResult.target();
                }

                return ResultVO.success(PageVO.of(pageList.getTotal(), PlayerDataConvert.I.convertListPlayerDataResultList(pageList, userBeanList)));
            }

            return ResultVO.success(PageVO.of(0, new ArrayList<>()));
        } catch (Exception e) {
            log.error("查询线下主播数据失败. param: {}", JsonUtil.dumps(param), e);
            return ResultVO.failure("查询线下主播数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置默认的上周时间范围
     * @param param 查询参数
     */
    private void setDefaultLastWeekTimeRange(ListPlayerDataParam param) {
        // 获取上一周周一的时间，要求格式是 yyyy-MM-dd
        LocalDate today = LocalDate.now();
        LocalDate lastMonday = today.minusWeeks(1)
                .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate lastSunday = today.minusWeeks(1)
                .with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

        param.setStartWeekDate(lastMonday.toString());
        param.setEndWeekDate(lastSunday.toString());
    }


    /**
     * 处理保护状态逻辑
     *
     * @param pageList 分页数据
     * @param param
     */
    private void processProtectionStatus(PageList<ListPlayerDataDTO> pageList, ListPlayerDataParam param) {
        // 根据 familyId 和 njId 分组，然后调用 batchGetProtectionStatus
        Map<Long, Map<Long, List<ListPlayerDataDTO>>> familyNjIdMap = pageList.stream()
                .collect(Collectors.groupingBy(ListPlayerDataDTO::getFamilyId,
                        Collectors.groupingBy(ListPlayerDataDTO::getNjId)));

        // 遍历每个分组，调用保护状态查询接口
        for (Map.Entry<Long, Map<Long, List<ListPlayerDataDTO>>> familyEntry : familyNjIdMap.entrySet()) {
            Long familyId = familyEntry.getKey();
            Map<Long, List<ListPlayerDataDTO>> njIdMap = familyEntry.getValue();
            for (Map.Entry<Long, List<ListPlayerDataDTO>> njEntry : njIdMap.entrySet()) {
                Long njId = njEntry.getKey();
                List<ListPlayerDataDTO> playerDataList = njEntry.getValue();
                // 调用保护状态查询接口
                Result<Map<Long, OfflineZoneProtectionWithStatusBean>> result = offlineZoneProtectionService.batchGetProtectionStatus(param.getAppId(), familyId, njId,
                        playerDataList.stream().map(ListPlayerDataDTO::getUserId).collect(Collectors.toSet())
                );

                if (RpcResult.isFail(result)) {
                    log.warn("查询保护状态失败, familyId: {}, njId: {}", familyId, njId);
                    continue;
                }
                Map<Long, OfflineZoneProtectionWithStatusBean> protectionStatusMap = result.target();
                playerDataList.forEach(playerData -> {
                    OfflineZoneProtectionWithStatusBean protectionStatus = protectionStatusMap.get(playerData.getUserId());
                    if (protectionStatus != null) {
                        playerData.setProtectionId(playerData.getProtectionId());
                        playerData.setProtectionStatus(protectionStatus.getProtectionStatus());
                        playerData.setProtection(protectionStatus.getProtection());
                        playerData.setAgreementStartTime(Optional.ofNullable(protectionStatus.getProtectionDetail()).map(OfflineZoneProtectionBean::getAgreementStartTime).orElse(null));
                        playerData.setAgreementEndTime(Optional.ofNullable(protectionStatus.getProtectionDetail()).map(OfflineZoneProtectionBean::getAgreementEndTime).orElse(null));
                    }
                });
            }
        }
    }

    public ResultVO<Void> batchResetPlayerData(BatchResetPlayerDataParam param, String operator, Integer appId) {
        RequestBatchResetAgreementValidity request = PlayerDataConvert.I.buildBatchResetAgreementValidity(param, appId, operator);

        Result<Void> result = offlineZoneProtectionService.batchResetAgreementValidity(request);
        if (RpcResult.isFail(result)) {
            log.warn("重置协议有效期失败, param: {}, appId: {}, operator: {}", param, appId, operator);
            return ResultVO.failure(result.rCode(), result.getMessage());
        }
        return ResultVO.success();
    }
}