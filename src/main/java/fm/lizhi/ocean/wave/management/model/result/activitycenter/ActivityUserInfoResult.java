package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * @description: 活动用户信息(主持 / 陪档)
 * @author: guoyibin
 * @create: 2024/10/23 21:21
 */
@Data
public class ActivityUserInfoResult {

    /**
     * 活动用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 活动用户名
     */
    private String name;

    /**
     * 活动用户波段号
     */
    private String band;

    /**
     * 活动用户头像
     */
    private String photo;
}
