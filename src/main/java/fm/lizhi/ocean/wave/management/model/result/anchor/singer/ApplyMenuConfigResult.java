package fm.lizhi.ocean.wave.management.model.result.anchor.singer;

import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.model.MenuVisibleRuleConfigDTO;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * 申请菜单配置结果
 */
@Data
public class ApplyMenuConfigResult {

    /**
     * 歌手类型
     */
    private Integer singerType;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 菜单配置
     */
    @Valid
    private List<MenuVisibleRuleConfigDTO> bizRuleConfigs;
}