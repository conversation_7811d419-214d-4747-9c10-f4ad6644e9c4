package fm.lizhi.ocean.wave.management.model.converter.family.offlinezone;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wave.management.model.converter.CommonConvert;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.GetFamilyRatingResult;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListFamilyRatingResult;
import fm.lizhi.ocean.wave.management.model.vo.family.offlinezone.OfflineLevelInfoVO;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataFamilyWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneFamilyRating;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelConfig;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        uses = CommonConvert.class
)
public interface FamilyRatingConvert {

    FamilyRatingConvert I = Mappers.getMapper(FamilyRatingConvert.class);

    default List<ListFamilyRatingResult> toListFamilyRatingResultPageByRating(PageList<OfflineZoneFamilyRating> pageList, List<OfflineZoneDataFamilyWeek> familyWeekList, List<OfflineZoneLevelConfig> levelConfigList){
        Map<Long, OfflineZoneFamilyRating> familyRatingMap = pageList.stream().collect(Collectors.toMap(OfflineZoneFamilyRating::getFamilyId, e -> e));
        Map<Long, OfflineZoneLevelConfig> levelConfigMap = levelConfigList.stream().collect(Collectors.toMap(OfflineZoneLevelConfig::getId, e -> e));

        return familyWeekList.stream().map( e -> {
            OfflineZoneFamilyRating familyRating = familyRatingMap.get(e.getFamilyId());
            String operator = familyRating != null ? familyRating.getOperator() : null;
            ListFamilyRatingResult result = toListFamilyRatingResult(e, operator);
            if (familyRating != null) {
                OfflineLevelInfoVO vo = new OfflineLevelInfoVO();
                vo.setId(familyRating.getLevelId());
                OfflineZoneLevelConfig levelConfig = levelConfigMap.get(familyRating.getLevelId());
                if (levelConfig != null) {
                    vo.setLevelName(levelConfig.getLevelName());
                }
                result.setLevelInfo(vo);
            }
            return result;
        }).collect(Collectors.toList());
    }


    default List<ListFamilyRatingResult> toListFamilyRatingResultPageByDataWeek(PageList<OfflineZoneDataFamilyWeek> familyWeekList, List<OfflineZoneFamilyRating> ratingList, List<OfflineZoneLevelConfig> levelConfigList){
        Map<Long, OfflineZoneFamilyRating> familyRatingMap = ratingList.stream().collect(Collectors.toMap(OfflineZoneFamilyRating::getFamilyId, e -> e));
        Map<Long, OfflineZoneLevelConfig> levelConfigMap = levelConfigList.stream().collect(Collectors.toMap(OfflineZoneLevelConfig::getId, e -> e));

        return familyWeekList.stream().map( e -> {
            OfflineZoneFamilyRating familyRating = familyRatingMap.get(e.getFamilyId());
            String operator = familyRating != null ? familyRating.getOperator() : null;
            ListFamilyRatingResult result = toListFamilyRatingResult(e, operator);
            if (familyRating != null) {
                OfflineLevelInfoVO vo = new OfflineLevelInfoVO();
                vo.setId(familyRating.getLevelId());
                OfflineZoneLevelConfig levelConfig = levelConfigMap.get(familyRating.getLevelId());
                if (levelConfig != null) {
                    vo.setLevelName(levelConfig.getLevelName());
                }
                result.setLevelInfo(vo);
            }
            return result;
        }).collect(Collectors.toList());
    }


    @Mapping(target = "familyInfo.id", source = "familyWeek.familyId")
    @Mapping(target = "familyInfo.name", source = "familyWeek.familyName")
    @Mapping(target = "levelInfo", ignore = true)
    ListFamilyRatingResult toListFamilyRatingResult(OfflineZoneDataFamilyWeek familyWeek, String operator);

    /**
     * 将公会评级实体转换为公会评级结果
     *
     * @param entity    公会评级实体
     * @param levelName 等级名称
     * @return 公会评级结果
     */
    @BeanMapping(unmappedTargetPolicy = ReportingPolicy.ERROR)
    GetFamilyRatingResult toGetFamilyRatingResult(OfflineZoneFamilyRating entity, String levelName);
}
