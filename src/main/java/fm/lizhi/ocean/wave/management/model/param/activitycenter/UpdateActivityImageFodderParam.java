package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityFodderClassificationEnum;
import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class UpdateActivityImageFodderParam {

    private Long id;

    /**
     * 素材名称
     */
    private String name;

    /**
     * 素材分类，参考枚举值
     * @see ActivityFodderClassificationEnum
     */
    private Integer type;

    /**
     * 素材颜色，包含#
     */
    private String color;


    /**
     * 素材宽高比
     */
    private String scale;

    /**
     * 素材图片地址，斜杠开头
     */
    private String imageUrl;

}