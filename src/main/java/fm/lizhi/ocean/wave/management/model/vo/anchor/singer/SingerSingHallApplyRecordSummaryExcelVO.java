package fm.lizhi.ocean.wave.management.model.vo.anchor.singer;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Data;

@Data
public class SingerSingHallApplyRecordSummaryExcelVO {

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 应用ID
     */
    @ExcelProperty(value = "应用ID")
    private Integer appId;

    /**
     * 家族ID
     */
    @ExcelProperty(value = "家族ID")
    private Long familyId;

    /**
     * 家族名称
     */
    @ExcelProperty(value = "家族名称")
    private String familyName;

    /**
     * 家族长ID
     */
    @ExcelProperty(value = "家族长ID")
    private Long userId;

    /**
     * C_FAMILY=PGC
     * P_FAMILY=UGC
     */
    @ExcelProperty(value = "家族类型")
    private String familyType;

    /**
     * 家族简介
     */
    @ExcelProperty(value = "家族简介")
    private String familyNote;

    /**
     * 家族头像
     */
    @ExcelProperty(value = "家族头像")
    private String familyIconUrl;

    /**
     * 用户类型
     * USER(0, "普通用户"),
     * ADMIN(1, "管理员"),
     * PLAYER(2, "成员"),
     * FAMILY(3,"家族长");
     */
    @ExcelProperty(value = "用户类型")
    private Integer userType;
    /**
     * 厅主ID
     */
    @ExcelProperty(value = "厅主ID")
    private Long njId;


       /**
     * 昵称
     */
    @ExcelProperty(value = "厅主昵称")
    private String njName;
    /**
     * 波段号
     */
    @ExcelProperty(value = "厅主波段号")
    private String band;

    /**
     * 头像
     */
    @ExcelProperty(value = "厅主头像")
    private String photo;

    /**
     * 审核状态，0-审核中，1-审核通过，2-审核未通过
     */
    @ExcelProperty(value = "审核状态")
    private Integer auditStatus;


    /**
     * 认证歌手数
     */
    @ExcelProperty(value = "认证歌手数")
    private Long singerAuthCnt;

    /**
     * 优质歌手数
     */
    @ExcelProperty(value = "优质歌手数")
    private Long seniorSingerAuthCnt;

    /**
     * 提交时间
     */
    @ExcelProperty(value = "提交时间")
    private Long applyTime;

    /**
     * 添加时间
     */
    @ExcelProperty(value = "添加时间")
    private Long createTime;

    /**
     * 操作人
     */
    @ExcelProperty(value = "操作人")
    private String operator;
}
