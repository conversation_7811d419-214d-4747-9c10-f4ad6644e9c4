package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量活动申请拒绝入参
 * <AUTHOR>
 */
@Data
public class BatchActivityAuditRejectParam {

    /**
     * 拒绝原因
     */
    private String reason;

    /**
     * 活动列表
     */
    @NotEmpty(message = "活动列表不能为空")
    private List<BatchActivityInfoParam> activity;

}
