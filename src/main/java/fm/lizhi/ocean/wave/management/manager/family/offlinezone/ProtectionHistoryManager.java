package fm.lizhi.ocean.wave.management.manager.family.offlinezone;

import fm.lizhi.ocean.wave.management.datastore.dao.family.offlinezone.OfflineZoneProtectionDao;
import fm.lizhi.ocean.wave.management.model.converter.family.offlinezone.ProtectionHistoryConvert;
import fm.lizhi.ocean.wave.management.model.dto.family.offlinezone.ProtectionHistoryDTO;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.GetProtectionHistoryParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ProtectionHistoryResult;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtectionHistory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 跳槽保护协议历史记录Manager
 * <AUTHOR>
 */
@Component
@Slf4j
public class ProtectionHistoryManager {

    @Autowired
    private OfflineZoneProtectionDao protectionDao;

    @Autowired
    private ProtectionHistoryConvert protectionHistoryConvert;

    /**
     * 查询跳槽保护协议历史记录列表（不分页）
     *
     * @param param 查询参数
     * @return 历史记录列表
     */
    public List<ProtectionHistoryResult> listProtectionHistory(GetProtectionHistoryParam param) {
        // 查询历史记录（不分页）
        List<OfflineZoneProtectionHistory> historyList = protectionDao.listProtectionHistoryByParam(param);
        
        // 转换为DTO
        List<ProtectionHistoryDTO> dtoList = protectionHistoryConvert.entityListToDTOList(historyList);
        
        // 转换为Result
        return protectionHistoryConvert.dtoListToResultList(dtoList);
    }



}