package fm.lizhi.ocean.wave.management.controller.anchor.singer;

import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerWhiteListDao;
import fm.lizhi.ocean.wave.management.manager.singer.SingerInfoManager;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.AddSingerWhiteParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.DeleteSingerWhiteParam;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.remote.service.anchor.singer.SingerInfoRemote;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/singer")
@Slf4j
public class SingerWhiteListController {

    @Autowired
    private SingerWhiteListDao singerWhiteListDao;

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private SingerInfoRemote singerInfoRemote;

    /**
     * 添加白名单
     */
    @PostMapping("/white/save")
    public ResultVO<Void> addWhiteList(@RequestBody @Valid AddSingerWhiteParam param) {
        Integer appId = SessionExtUtils.getAppId();
        if (singerWhiteListDao.insertSingerWhiteList(appId, param.getSingerId(), param.getSingerType())) {
            return ResultVO.success();
        }
        return ResultVO.failure("添加白名单失败");
    }

    /**
     * 移除白名单
     */
    @PostMapping("/white/delete")
    public ResultVO<Void> deleteWhiteList(@RequestBody @Valid DeleteSingerWhiteParam param) {
        Integer appId = SessionExtUtils.getAppId();
        //操作人
        String operator = SessionUtils.getAccount();
        ResultVO<Void> res = singerInfoManager.eliminateSingerAfterRemoveWhiteList(appId, param.getSingerId(), param.getSingerType(), operator);
        if (res.isFailure()) {
            return ResultVO.failure(res);
        }
        //移除白名单
        if (singerWhiteListDao.removeSingerWhiteList(appId, param.getSingerId(), param.getSingerType())) {
            return ResultVO.success();
        }
        return ResultVO.failure("移除白名单失败");
    }
}
