package fm.lizhi.ocean.wave.management.datastore.mapper.family.offlinezone;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRight;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface OfflineZoneLevelRightExtMapper {

    @Delete("UPDATE `offline_zone_level_right`\n" +
            "SET `deleted` = 1, `operator` = #{operator}, `modify_time` = NOW()\n" +
            "WHERE `id` = #{id} AND `deleted` = 0")
    int deleteById(@Param("id") long id, @Param("operator") String operator);

    @Select("<script>\n" +
            "  SELECT * FROM `offline_zone_level_right`\n" +
            "  WHERE `id` IN\n" +
            "    <foreach item=\"id\" collection=\"ids\" open=\"(\" separator=\",\" close=\")\">\n" +
            "      #{id}\n" +
            "    </foreach>\n" +
            "</script>")
    List<OfflineZoneLevelRight> selectByIds(@Param("ids") List<Long> ids);

    @Select("SELECT * FROM `offline_zone_level_right`\n" +
            "WHERE `app_id` = #{appId} AND `deploy_env` = #{deployEnv} AND `deleted` = 0\n" +
            "ORDER BY `create_time` DESC")
    List<OfflineZoneLevelRight> listLevelRights(@Param("appId") Integer appId, @Param("deployEnv") String deployEnv);
}
