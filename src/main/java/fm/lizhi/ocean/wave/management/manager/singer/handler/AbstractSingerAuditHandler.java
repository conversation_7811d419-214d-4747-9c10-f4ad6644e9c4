package fm.lizhi.ocean.wave.management.manager.singer.handler;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerRedisDao;
import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerVerifyApplyDao;
import fm.lizhi.ocean.wave.management.manager.singer.SingerPushManager;
import fm.lizhi.ocean.wave.management.manager.singer.machine.SingerAuditStatusModel;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerAuditParamDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerExecuteAuditDTO;
import fm.lizhi.ocean.wave.management.remote.service.user.UserFamilyServiceRemote;
import fm.lizhi.ocean.wave.management.util.RedisLock;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

/**
 * 歌手认证状态处理器抽象类
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractSingerAuditHandler implements SingerAuditStatusHandler {

    @Autowired
    private SingerRedisDao singerRedisDao;

    @Autowired
    private SingerVerifyApplyDao singerVerifyApplyDao;

    @Autowired
    private UserFamilyServiceRemote userFamilyServiceRemote;

    @Autowired
    private SingerPushManager singerPushManager;

    @Override
    public SingerExecuteAuditDTO executeAudit(SingerAuditParamDTO param, SingerVerifyRecord verifyRecord) {
        try (RedisLock lock = singerRedisDao.tryGetSingerUpdateLock(verifyRecord.getAppId(), verifyRecord.getNjId())) {
            if (!lock.tryLock()) {
                log.warn("executeAudit.tryGetSingerUpdateLock，userId:{}, appId:{}", verifyRecord.getUserId(), verifyRecord.getAppId());
                //加锁失败，结束
                return SingerExecuteAuditDTO.failure("记录信息发生变更，请刷新后重试！");
            }
            if (!isAllowStatusChange(verifyRecord.getAuditStatus(), param.getTargetAuditStatus())) {
                log.warn("executeAudit.isAllowStatusChange fail，userId:{}, auditStatus:{},targetAuditStatus:{}",
                        verifyRecord.getUserId(), verifyRecord.getAuditStatus(), param.getTargetAuditStatus());
                SingerAuditStatusEnum originalStatus = SingerAuditStatusEnum.getByType(verifyRecord.getAuditStatus());
                SingerAuditStatusEnum targetStatus = SingerAuditStatusEnum.getByType(param.getTargetAuditStatus());
                if (originalStatus == null || targetStatus == null) {
                    return SingerExecuteAuditDTO.failure("非法状态，无法流转到目标状态");
                }
                String reason = String.format("无法从:%s 状态流转到：%s 状态", originalStatus.getName(), targetStatus.getName());
                return SingerExecuteAuditDTO.failure(reason);
            }

            //校验歌手的签约厅信息，更新家族和签约厅ID
            Optional<Boolean> updateRes = updateSingerSignInfo(verifyRecord);
            if (updateRes.isPresent() && !updateRes.get()) {
                return SingerExecuteAuditDTO.failure("修改签约厅信息失败，请稍候重试!");
            }
            //如果厅信息发生变更，重新查询认证信息
            verifyRecord = updateRes.isPresent() ? singerVerifyApplyDao.getSingerVerifyRecordById(verifyRecord.getId()) : verifyRecord;
            //具体审核执行操作
            SingerExecuteAuditDTO singerExecuteAuditDTO = auditHandle(param, verifyRecord);
            if (singerExecuteAuditDTO.isSuccess()) {
                singerPushManager.pushVerifyStatusChange(verifyRecord.getAppId(), verifyRecord.getUserId());
            }
            return singerExecuteAuditDTO;
        } catch (Exception e) {
            log.error("AbstractSingerAuditHandler.executeAudit happen error: param={}", JsonUtil.dumps(param), e);
            return SingerExecuteAuditDTO.failure("发生未知异常，请联系管理员");
        }
    }

    /**
     * 状态流转处理
     *
     * @param param        请求参数
     * @param verifyRecord 认证记录
     * @return 是否处理成功
     */
    abstract SingerExecuteAuditDTO auditHandle(SingerAuditParamDTO param, SingerVerifyRecord verifyRecord);

    /**
     * 状态流转是否允许
     *
     * @param currentStatus 当前状态
     * @param targetStatus  目标状态
     * @return 是否允许
     */
    protected boolean isAllowStatusChange(Integer currentStatus, Integer targetStatus) {
        return SingerAuditStatusModel.isAllowStatusChange(currentStatus, targetStatus);
    }

    /**
     * 更新歌手的签约厅信息
     *
     * @param verifyRecord 歌手认证记录
     * @return 是否更新成功
     */
    protected Optional<Boolean> updateSingerSignInfo(SingerVerifyRecord verifyRecord) {
        try {
            Result<UserInFamilyBean> result = userFamilyServiceRemote.getUserInFamily(verifyRecord.getAppId(), verifyRecord.getUserId());
            //判断失败
            if (ResultUtils.isFailure(result)) {
                log.warn("updateSingerSignInfo.getUserInFamily fail, userId:{}", verifyRecord.getUserId());
                return Optional.of(Boolean.FALSE);
            }
            UserInFamilyBean userInFamilyBean = result.target();
            if (verifyRecord.getNjId().equals(userInFamilyBean.getNjId()) && verifyRecord.getFamilyId().equals(userInFamilyBean.getFamilyId())) {
                //签约信息没有变化，不用更新
                return Optional.empty();
            }
            long njId = userInFamilyBean.getNjId() == null ? 0L : userInFamilyBean.getNjId();
            long familyId = userInFamilyBean.getFamilyId() == null ? 0L : userInFamilyBean.getFamilyId();
            boolean res = singerVerifyApplyDao.updateFamilyIdAndNjId(verifyRecord.getAppId(), verifyRecord.getId(), verifyRecord.getUserId(), verifyRecord.getSingerType(), familyId, njId);
            log.info("updateSingerSignInfo, userId:{}, oldFamilyId:{}, oldNjId:{}, newFamilyId:{}, newNjId:{}, res:{}", verifyRecord.getUserId(), verifyRecord.getFamilyId(), verifyRecord.getNjId(), familyId, njId, res);
            return Optional.of(res);
        } catch (Exception e) {
            //歌手类型也要打印出来
            log.error("updateSingerSignInfo happen error, userId:{}, singerType:{}", verifyRecord.getUserId(), verifyRecord.getSingerType(), e);
            return Optional.of(Boolean.FALSE);
        }
    }

}
