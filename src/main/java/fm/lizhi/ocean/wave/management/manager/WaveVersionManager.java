package fm.lizhi.ocean.wave.management.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.management.model.param.platform.SyncGrayGroupUserParam;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.remote.service.platform.WaveVersionManagementServiceRemote;
import fm.lizhi.ocean.wave.management.remote.service.user.UserCommonServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.platform.api.platform.version.constant.GrayGroupSyncTypeEnum;
import fm.lizhi.ocean.wave.platform.api.platform.version.response.ResponseGetGrayGroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 创作者版本管理器
 */
@Component
@Slf4j
public class WaveVersionManager {

    @Autowired
    private WaveVersionManagementServiceRemote waveVersionManagementServiceRemote;

    @Autowired
    private UserCommonServiceRemote userCommonServiceRemote;

    /**
     * 同步灰度分组成员
     *
     * @param param 同步灰度分组成员参数
     * @return 同步灰度分组成员结果
     */
    public ResultVO<Void> syncGrayGroupUser(SyncGrayGroupUserParam param) {
        Long groupId = param.getGroupId();
        String operator = param.getOperator();
        Result<ResponseGetGrayGroup> result = waveVersionManagementServiceRemote.getGrayGroup(groupId);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result.rCode(), result.getMessage());
        }
        ResponseGetGrayGroup grayGroup = result.target();
        if (Objects.equals(grayGroup.getSyncType(), GrayGroupSyncTypeEnum.NONE.getValue())) {
            return ResultVO.failure("灰度组配置了不需要同步, 请手动添加用户");
        }
        Result<List<Long>> fetchResult = fetchGrayGroupUserIds(grayGroup);
        if (ResultUtils.isFailure(fetchResult)) {
            return ResultVO.failure(fetchResult.rCode(), fetchResult.getMessage());
        }
        List<Long> userIds = fetchResult.target();
        if (CollectionUtils.isEmpty(userIds)) {
            return ResultVO.failure("灰度组同步用户为空");
        }
        Integer syncAppId = grayGroup.getSyncAppId();
        Result<Void> replaceResult = waveVersionManagementServiceRemote.replaceGrayGroupUsers(groupId, syncAppId, userIds, operator);
        if (ResultUtils.isFailure(replaceResult)) {
            return ResultVO.failure(replaceResult.rCode(), replaceResult.getMessage());
        }
        return ResultVO.success();
    }

    private Result<List<Long>> fetchGrayGroupUserIds(ResponseGetGrayGroup grayGroup) {
        Integer syncType = grayGroup.getSyncType();
        Integer syncAppId = grayGroup.getSyncAppId();
        if (Objects.equals(syncType, GrayGroupSyncTypeEnum.FAMILY_PLAYER.getValue())) {
            Long syncFamilyId = grayGroup.getSyncFamilyId();
            return userCommonServiceRemote.getAllSignedGuildPlayerIds(syncFamilyId, syncAppId);
        }
        if (Objects.equals(syncType, GrayGroupSyncTypeEnum.ROOM_PLAYER.getValue())) {
            Long syncNjId = grayGroup.getSyncNjId();
            return userCommonServiceRemote.getAllSignedRoomPlayerIds(syncNjId, syncAppId);
        }
        return ResultUtils.failure(GeneralRCode.GENERAL_RCODE_UNKNOWN_ERROR, "灰度组同步参数配置错误");
    }
}
