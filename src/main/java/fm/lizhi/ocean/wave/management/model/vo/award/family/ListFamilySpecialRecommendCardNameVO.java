package fm.lizhi.ocean.wave.management.model.vo.award.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 列出公会特殊推荐卡名单的结果VO
 */
@Data
public class ListFamilySpecialRecommendCardNameVO {

    /**
     * 记录id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 用户id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 推荐卡发放数量
     */
    private Integer number;

    /**
     * 创建时间, 毫秒时间戳
     */
    private Long createTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改时间, 毫秒时间戳
     */
    private Long modifyTime;

    /**
     * 修改者
     */
    private String modifier;
}
