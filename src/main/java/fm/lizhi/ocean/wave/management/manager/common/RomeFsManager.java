package fm.lizhi.ocean.wave.management.manager.common;

import fm.lizhi.common.romefs.javasdk.config.RomeFsConfig;
import fm.lizhi.common.romefs.javasdk.exception.RomeFsException;
import fm.lizhi.common.romefs.javasdk.service.CustomMultipartUploadService;
import fm.lizhi.common.romefs.javasdk.service.PutObjectService;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.management.model.param.common.RomeFsPutParam;
import fm.lizhi.ocean.wave.management.model.result.common.RomeFsPutResult;
import fm.lizhi.ocean.wave.management.util.JsonUtils;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.TreeMap;

/**
 * 罗马文件上传管理器
 */
@Component
@Slf4j
public class RomeFsManager {

    /**
     * 上传文件. 自动根据文件大小分片上传和重试, 在重试次数内会屏蔽异常, 超过重试次数会抛出异常. 参考
     * <a href="https://lizhi2021.feishu.cn/wiki/MB2rwxahqimokGky8RwceMhlnvf">罗马文件上传-快速开始(服务端)</a>
     *
     * @param param 上传参数
     * @return 上传结果
     */
    public Result<RomeFsPutResult> putObject(RomeFsPutParam param) {
        try {
            log.info("Start put object, romeFsPutParam={}", JsonUtils.toJsonString(param));
            RomeFsConfig romeFsConfig = param.getRomeFsConfig();
            String accessModifier = param.getAccessModifier();
            Path localPath = param.getLocalPath();
            String fileName = resolveFileName(param.getFileName(), localPath);
            String contentType = resolveContentType(param.getContentType(), fileName);
            int retries = Math.max(param.getRetries(), 0);
            long fileSize = Files.size(localPath);
            int partBytes = param.getPartBytes();
            String filePath;
            if (fileSize <= partBytes) {
                log.info("Use single upload, fileSize={}", fileSize);
                filePath = singleUpload(romeFsConfig, accessModifier, contentType, fileName, localPath, retries);
            } else {
                log.info("Use multipart upload, fileSize={}, partBytes={}", fileSize, partBytes);
                filePath = multipartUpload(romeFsConfig, accessModifier, contentType, fileName, localPath, retries, partBytes);
            }
            RomeFsPutResult romeFsPutResult = new RomeFsPutResult();
            romeFsPutResult.setFilePath(filePath);
            log.info("Put object success, filePath={}, fileSize={}", filePath, fileSize);
            return ResultUtils.success(romeFsPutResult);
        } catch (Exception e) {
            log.error("Put object error, romeFsPutParam={}", JsonUtils.toJsonString(param), e);
            return ResultUtils.failure(GeneralRCode.GENERAL_RCODE_UNKNOWN_ERROR, "RomeFs Put object error");
        }
    }

    private String resolveFileName(String fileName, Path localPath) {
        if (StringUtils.isNotBlank(fileName)) {
            return fileName;
        }
        return localPath.getFileName().toString();
    }

    private String resolveContentType(String contentType, String fileName) {
        if (StringUtils.isNotBlank(contentType)) {
            return contentType;
        }
        String extension = FilenameUtils.getExtension(fileName);
        return ExtensionContentTypeEnum.getContentType(extension);
    }

    /**
     * 单文件上传
     *
     * @param romeFsConfig   罗马文件系统配置
     * @param accessModifier 访问权限
     * @param contentType    文件类型
     * @param fileName       文件名
     * @param localPath      本地文件路径
     * @param retries        最大重试次数
     * @return 上传后的文件路径
     * @throws Exception 上传异常
     */
    private String singleUpload(RomeFsConfig romeFsConfig, String accessModifier, String contentType,
                                String fileName, Path localPath, int retries) throws Exception {
        PutObjectService putObjectService = new PutObjectService(romeFsConfig);
        for (int retryCount = 0; retryCount <= retries; retryCount++) {
            try {
                String filePath = putObjectService.putObject(accessModifier, contentType, fileName, localPath.toFile());
                if (retryCount == 0) {
                    log.info("Single upload success, filePath={}", filePath);
                } else {
                    log.info("Single upload retry success, filePath={}, retryCount={}", filePath, retryCount);
                }
                return filePath;
            } catch (Exception e) {
                if (e instanceof RomeFsException && ((RomeFsException) e).getHttpStatus() == 500 && retryCount < retries) {
                    log.debug("Single upload has an exception", e);
                    log.info("Single upload has an exception and will retry, fileName={}, retryCount={}", fileName, retryCount);
                } else {
                    throw e;
                }
            }
        }
        // 超过重试次数
        throw new IllegalStateException(String.format("Single upload failed, fileName=%s, retries=%d", fileName, retries));
    }

    /**
     * 分片上传
     *
     * @param romeFsConfig   罗马文件系统配置
     * @param accessModifier 访问权限
     * @param contentType    文件类型
     * @param fileName       文件名
     * @param localPath      本地文件路径
     * @param retries        最大重试次数
     * @param partBytes      分片大小
     * @return 上传后的文件路径
     * @throws Exception 上传异常
     */
    private String multipartUpload(RomeFsConfig romeFsConfig, String accessModifier, String contentType,
                                   String fileName, Path localPath, int retries, int partBytes) throws Exception {
        CustomMultipartUploadService customMultipartUploadService = new CustomMultipartUploadService(romeFsConfig);
        CustomMultipartUploadService.MultipartUploadId multipartUploadId = customMultipartUploadService
                .createMultipartUpload(accessModifier, contentType, fileName);
        TreeMap<Integer, String> eTagMap = new TreeMap<>();
        try (FileChannel fileChannel = FileChannel.open(localPath, StandardOpenOption.READ)) {
            ByteBuffer buffer = ByteBuffer.allocate(partBytes);
            int partNumber = 0;
            byte[] fullPartBuffer = new byte[partBytes];
            while (true) {
                buffer.clear();
                partNumber++;
                int read = fileChannel.read(buffer);
                if (read == -1) {
                    break;
                }
                buffer.flip();
                byte[] data = buffer.remaining() == partBytes ? fullPartBuffer : new byte[buffer.remaining()];
                buffer.get(data);
                String eTag = partUpload(customMultipartUploadService, multipartUploadId, fileName, partNumber, data, retries);
                eTagMap.put(partNumber, eTag);
            }
            log.info("Multipart upload eTagMap={}", eTagMap);
            String filePath = customMultipartUploadService.complete(multipartUploadId, eTagMap);
            log.info("Multipart upload success, filePath={}", filePath);
            return filePath;
        } catch (Exception e) {
            try {
                customMultipartUploadService.abort(multipartUploadId);
            } catch (RuntimeException ex) {
                log.info("Abort multipart upload has an exception", ex);
            }
            throw e;
        }
    }

    private String partUpload(CustomMultipartUploadService customMultipartUploadService,
                              CustomMultipartUploadService.MultipartUploadId multipartUploadId,
                              String fileName, int partNumber, byte[] data, int retries) throws Exception {
        String uploadId = multipartUploadId.getUploadId();
        for (int retryCount = 0; retryCount <= retries; retryCount++) {
            try {
                String eTag = customMultipartUploadService.uploadPart(multipartUploadId, partNumber, data);
                if (retryCount == 0) {
                    log.info("Part upload success, fileName={}, partNumber={}, uploadId={}, eTag={}",
                            fileName, partNumber, uploadId, eTag);
                } else {
                    log.info("Part upload retry success, fileName={}, partNumber={}, uploadId={}, eTag={}, retryCount={}",
                            fileName, partNumber, uploadId, eTag, retryCount);
                }
                return eTag;
            } catch (Exception e) {
                if (e instanceof RomeFsException && ((RomeFsException) e).getHttpStatus() == 500 && retryCount < retries) {
                    log.debug("Part upload has an exception", e);
                    log.info("Part upload has an exception, fileName={}, partNumber={}, uploadId={}, retryCount={}",
                            fileName, partNumber, uploadId, retryCount);
                } else {
                    throw e;
                }
            }
        }
        // 超过重试次数
        throw new IllegalStateException(String.format(
                "Part upload failed, fileName=%s, partNumber=%d, uploadId=%s, retries=%d",
                fileName, partNumber, uploadId, retries));
    }

    /**
     * 文件扩展名对应的内容类型枚举
     */
    private enum ExtensionContentTypeEnum {

        DEFAULT("", ""),
        GIF("gif", "image/gif"),
        JPEG("jpeg", "image/jpeg"),
        JPG("jpg", "image/jpeg"),
        PNG("png", "image/png"),
        WEBP("webp", "image/webp"),
        ;

        private final String extension;
        private final String contentType;

        ExtensionContentTypeEnum(String extension, String contentType) {
            this.extension = extension;
            this.contentType = contentType;
        }

        public static String getContentType(String extension) {
            for (ExtensionContentTypeEnum value : values()) {
                if (value.extension.equalsIgnoreCase(extension)) {
                    return value.contentType;
                }
            }
            return DEFAULT.contentType;
        }
    }
}