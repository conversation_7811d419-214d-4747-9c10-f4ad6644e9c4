package fm.lizhi.ocean.wave.management.model.result.family.offlinezone;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerUserInfoVO;
import fm.lizhi.ocean.wave.management.model.vo.family.FamilyVO;
import fm.lizhi.ocean.wave.management.model.vo.user.UserVO;
import lombok.Data;

/**
 * 查询线下主播数据列表结果
 * <AUTHOR>
 */
@Data
public class ListPlayerDataResult {

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;


    /**
     * 实名证件号码
     */
    private String idCardNumber;

    /**
     * 实名姓名
     */
    private String idName;


    /**
     * 公会信息
     */
    private FamilyVO familyInfo;

    /**
     * 厅主信息
     */
    private UserVO njInfo;

    /**
     * 主播信息
     */
    private UserVO playerInfo;

    /**
     * 签约时间
     */
    private Long beginSignTime;

    /**
     * 主播分类：0 线下，1 线上
     */
    private Integer category;

    // 保护协议相关字段
    /**
     * 保护协议ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long protectionId;

    /**
     * 协议状态
     * @see fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.ProtectionStatusEnums
     */
    private Integer protectionStatus;

    /**
     * 是否受保护：true受保护，false未受保护
     */
    private Boolean protection;

    /**
     * 协议开始时间
     */
    private Long agreementStartTime;

    /**
     * 协议结束时间
     */
    private Long agreementEndTime;

}