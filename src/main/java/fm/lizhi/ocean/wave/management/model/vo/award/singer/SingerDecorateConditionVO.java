package fm.lizhi.ocean.wave.management.model.vo.award.singer;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.List;

@Data
public class SingerDecorateConditionVO {
    /**
     * 条件id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 装扮规则ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long decorateRuleId;

    private Integer appId;

    /**
     * 1:曲风，2：原创
     */
    private Integer conditionType;

    /**
     * 1:固定曲风，2：任一曲风，3：全能曲风
     */
    private Integer songStyleType;

    /**
     * 是否原创歌手
     */
    private Boolean originalSinger;

    /**
     * 曲风
     */
    private List<String> songStyleList;
}
