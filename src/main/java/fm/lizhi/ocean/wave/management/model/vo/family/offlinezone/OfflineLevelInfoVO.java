package fm.lizhi.ocean.wave.management.model.vo.family.offlinezone;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OfflineLevelInfoVO {

    /**
     * 等级ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 等级名称
     */
    private String levelName;
}
