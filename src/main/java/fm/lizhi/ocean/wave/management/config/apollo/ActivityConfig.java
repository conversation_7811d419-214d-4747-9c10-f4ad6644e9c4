package fm.lizhi.ocean.wave.management.config.apollo;

import fm.lizhi.ocean.godzilla.api.constant.ActivityPlanImageSceneEnum;
import fm.lizhi.ocean.lamp.common.config.annotation.JsonStringProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * 活动配置
 */
@ConfigurationProperties(prefix = "activity")
@Data
public class ActivityConfig {

    /**
     * AI相关配置
     */
    private Ai ai = new Ai();

    /**
     * AI相关配置
     */
    @Data
    public static class Ai {

        /**
         * AI图片场景列表, 配置参考{@link ActivityPlanImageSceneEnum}枚举, 例如
         * <p>
         *     {@code [{"value":0,"name":"动漫"},{"value":1,"name":"港风动漫"},{"value":2,"name":"国风"},{"value":3,"name":"赛博朋克"}]}
         * </p>
         * 注意修改配置之前请先与人工智能团队沟通确认是否支持相关参数
         */
        @JsonStringProperty
        private List<ImageStyle> imageStyles = new ArrayList<>();

        /**
         * AI图片场景列表, 配置参考{@link ActivityPlanImageSceneEnum}枚举, 例如
         * <p>
         *     {@code [{"value":100,"name":"自定义","width":0,"height":0},{"value":0,"name":"官频封面","width":1200,"height":1200},{"value":1,"name":"节目单封面","width":1200,"height":1200},{"value":2,"name":"房间背景","width":750,"height":1624},{"value":3,"name":"首页横幅","width":1620,"height":426},{"value":4,"name":"模板封面","width":1992,"height":768},{"value":5,"name":"蒙面头像框","width":1440,"height":1440}]}
         * </p>
         * 注意修改配置之前请先与人工智能团队沟通确认是否支持相关参数
         */
        @JsonStringProperty
        private List<ImageScene> imageScenes = new ArrayList<>();

        /**
         * AI图片风格
         */
        @Data
        public static class ImageStyle {

            /**
             * 风格值
             */
            private Integer value;

            /**
             * 风格名称
             */
            private String name;
        }

        /**
         * AI图片场景
         */
        @Data
        public static class ImageScene {

            /**
             * 场景值
             */
            private Integer value;

            /**
             * 场景名称
             */
            private String name;

            /**
             * 图片宽度
             */
            private Integer width;

            /**
             * 图片高度
             */
            private Integer height;
        }
    }
}
