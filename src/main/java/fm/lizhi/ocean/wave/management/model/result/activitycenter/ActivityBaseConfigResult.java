package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import fm.lizhi.ocean.wave.management.model.vo.activitycenter.ActivityResourceEnumVO;
import fm.lizhi.ocean.wave.management.model.vo.activitycenter.RoomCategoryVO;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityBaseConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityBigClassTypeConfigBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ActivityBaseConfigResult {

    /**
     * 应用信息列表
     */
    private List<ActivityBaseConfig> appInfoList;

    /**
     * 自动配置资源列表
     */
    private List<ActivityAutoConfigResourceResult> autoConfigResourceList;

    /**
     * 提报限制列表
     */
    private List<ActivityBaseConfig> applyRuleList;

    /**
     * 玩法工具列表
     */
    private List<ActivityToolsConfig> activityToolList;

    /**
     * 素材分类
     */
    private List<ActivityBaseConfig> fodderClassificationList;

    /**
     * 装扮类型
     */
    private List<ActivityBaseConfig> decorateTypeList;


    /**
     * 亮点标签
     */
    private List<ActivityTemplateHighlightConfigResult> heightLightList;

    /**
     * 大分类类型列表
     */
    private List<ActivityBigClassTypeConfig> bigClassTypeList;

    /**
     * 活动目标
     */
    private List<ActivityBaseConfig> activityGoalList;

    /**
     * 房间品类
     */
    private List<RoomCategoryVO> roomCategoryList;

    /**
     * 奖励标签列表
     */
    private List<ActivityResourceEnumVO> rewardTagList;

}
