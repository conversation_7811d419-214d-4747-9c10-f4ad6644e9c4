package fm.lizhi.ocean.wave.management.model.vo.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/14 17:01
 */
@Data
@Accessors(chain = true)
public class FamilyLevelConfigVO {

    /**
     * Id 仅编辑的时候需要
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 最小流水
     */
    private Integer minIncome;

    /**
     * 角标
     */
    private String levelIcon;

    /**
     * 勋章
     */
    private String levelMedal;

    /**
     * 奖励图片
     */
    private List<String> awardImgs;

    /**
     * 最近修改人
     */
    private String modifyUser;

    /**
     * 主题色
     */
    private String themColor;

    /**
     * 背景颜色
     */
    private String backgroundColor;

}
