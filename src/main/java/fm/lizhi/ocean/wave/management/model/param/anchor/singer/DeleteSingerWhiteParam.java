package fm.lizhi.ocean.wave.management.model.param.anchor.singer;

import fm.lizhi.ocean.wave.management.common.validation.EnumValue;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class DeleteSingerWhiteParam {

    @NotNull(message = "歌手ID不能为空")
    @Min(value = 1, message = "歌手ID不能小于1")
    private Long singerId;

    @NotNull(message = "歌手类型不能为空")
    @EnumValue(value = SingerTypeEnum.class, fieldName = EnumValue.FIELD_NAME_TYPE, message = "白名单歌手类型错误")
    private Integer singerType;
}
