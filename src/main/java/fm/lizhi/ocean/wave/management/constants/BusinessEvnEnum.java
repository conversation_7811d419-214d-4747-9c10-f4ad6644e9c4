package fm.lizhi.ocean.wave.management.constants;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum BusinessEvnEnum {
    PP("ppyw", 10919088, "cn", "pplive", 1, "5022031727227306035", "PP"),
    HEI_YE("heiye", 57333013, "cn", "heiye", 1, "", "小陪伴"),
    XIMI("ximi", 9637128, "cn", "ximi", 1, "", "小西米"),
    ;

    private final String name;
    private final int appId;
    private final String region;
    private final String businessEnv;
    private final int online;
    private final String payAppIdStr;
    private final String cnName;

    private static final Map<Integer, BusinessEvnEnum> APP_ID_MAP = new HashMap<>(8);

    static {
        for (BusinessEvnEnum item : BusinessEvnEnum.values()) {
            APP_ID_MAP.put(item.getAppId(), item);
        }
    }

    BusinessEvnEnum(String name, int appId, String region, String businessEnv, int online, String payAppIdStr, String cnName) {
        this.name = name;
        this.appId = appId;
        this.region = region;
        this.businessEnv = businessEnv;
        this.online = online;
        this.payAppIdStr = payAppIdStr;
        this.cnName = cnName;
    }

    public String getName() {
        return name;
    }

    public Integer appId() {
        return appId;
    }

    public int getAppId() {
        return appId;
    }

    public String getRegion() {
        return region;
    }

    public String getBusinessEnv() {
        return businessEnv;
    }

    public String getPayAppIdStr() {
        return payAppIdStr;
    }

    public String getCnName() {
        return cnName;
    }

    public static BusinessEvnEnum from(Integer appId) {
        return APP_ID_MAP.get(appId);
    }

}