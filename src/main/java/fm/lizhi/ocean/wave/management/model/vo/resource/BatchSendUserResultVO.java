package fm.lizhi.ocean.wave.management.model.vo.resource;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/3/22 16:27
 */
@Data
@Accessors(chain = true)
public class BatchSendUserResultVO {

    /**
     * 用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 异常原因
     */
    private String msg;

}
