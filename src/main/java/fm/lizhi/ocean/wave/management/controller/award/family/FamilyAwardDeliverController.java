package fm.lizhi.ocean.wave.management.controller.award.family;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.manager.file.FileExportManager;
import fm.lizhi.ocean.wave.management.model.converter.award.family.FamilyAwardDeliverConvert;
import fm.lizhi.ocean.wave.management.model.param.award.family.GetAwardDeliverRecordParam;
import fm.lizhi.ocean.wave.management.model.param.award.family.ListDeliverItemParam;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.award.family.*;
import fm.lizhi.ocean.wave.management.model.vo.file.FileExportVO;
import fm.lizhi.ocean.wave.management.remote.service.award.family.FamilyAwardDeliverServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.AwardDeliverRecordV1Bean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.AwardDeliverRecordV2Bean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.FamilyAwardDeliverItemBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestAwardDeliverListRecordV1;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestAwardDeliverListRecordV2;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestGetListDeliverItem;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.List;

/**
 * 公会奖励发放记录
 * <AUTHOR>
 * @date 2025/3/26 10:54
 */
@RestController
@RequestMapping("/award/family/awardDeliver")
@Slf4j
public class FamilyAwardDeliverController {

    @Autowired
    private FamilyAwardDeliverServiceRemote familyAwardDeliverServiceRemote;
    @Autowired
    private FileExportManager fileExportManager;

    /**
     * 奖励发放记录-列出发放详情
     * @return
     */
    @GetMapping("/listDeliverItem")
    public ResultVO<List<FamilyAwardDeliverItemVO>> listDeliverItem(@Validated ListDeliverItemParam param){
        RequestGetListDeliverItem request = new RequestGetListDeliverItem();
        request.setAppId(SessionExtUtils.getAppId());
        request.setRecordId(param.getRecordId());
        Result<List<FamilyAwardDeliverItemBean>> result = familyAwardDeliverServiceRemote.listDeliverItem(request);
        if (ResultUtils.isFailure(result)) {
            log.warn("listDeliverItem failed. request:{}, rCode:{}", request, result.rCode());
            return ResultVO.failure("系统异常");
        }
        return ResultVO.success(FamilyAwardDeliverConvert.I.deliverItemBeans2VOs(result.target()));
    }

    /**
     * 奖励发放记录-列出记录(陪伴/西米)
     * @param param
     * @return
     */
    @GetMapping("listRecordV1")
    public ResultVO<PageVO<AwardDeliverRecordV1VO>> listRecordV1(GetAwardDeliverRecordParam param){
        RequestAwardDeliverListRecordV1 request = new RequestAwardDeliverListRecordV1();
        request.setPageNumber(param.getPageNumber());
        request.setPageSize(param.getPageSize());
        request.setFamilyUserId(param.getFamilyUserId());
        request.setFamilyId(param.getFamilyId());
        request.setFamilyName(param.getFamilyName());
        request.setMinDeliverTime(param.getMinDeliverTime());
        request.setMaxDeliverTime(param.getMaxDeliverTime());
        request.setAppId(SessionExtUtils.getAppId());

        Result<PageBean<AwardDeliverRecordV1Bean>> result = familyAwardDeliverServiceRemote.listRecordV1(request);
        if (ResultUtils.isFailure(result)) {
            log.warn("listRecordV1 failed. request:{}, rCode:{}", request, result.rCode());
            return ResultVO.failure("系统异常");
        }

        List<AwardDeliverRecordV1VO> voList = FamilyAwardDeliverConvert.I.awardDeliverRecordV1Beans2VOs(result.target().getList());
        return ResultVO.success(PageVO.of(result.target().getTotal(), voList));
    }

    /**
     * 奖励发放记录-导出记录(陪伴/西米)
     * @param param
     * @param response
     */
    @GetMapping("exportDeliverV1")
    public void exportDeliverV1(GetAwardDeliverRecordParam param, HttpServletResponse response){
        String fileName = "奖励发放记录";
        try {
            FileExportVO<AwardDeliverRecordV1ExcelVO> fileExportVO = new FileExportVO<>();
            fileExportVO.setHead(AwardDeliverRecordV1ExcelVO.class);
            fileExportVO.setFileName(fileName);
            fileExportVO.setQueryAll(true);

            fileExportManager.exportToHttpResponse(fileExportVO, response, (pageNo, pageSize)-> {

                RequestAwardDeliverListRecordV1 request = new RequestAwardDeliverListRecordV1();
                request.setPageNumber(pageNo);
                request.setPageSize(pageSize);
                request.setFamilyUserId(param.getFamilyUserId());
                request.setFamilyId(param.getFamilyId());
                request.setFamilyName(param.getFamilyName());
                request.setMinDeliverTime(param.getMinDeliverTime());
                request.setMaxDeliverTime(param.getMaxDeliverTime());
                request.setAppId(SessionExtUtils.getAppId());

                Result<PageBean<AwardDeliverRecordV1Bean>> result = familyAwardDeliverServiceRemote.listRecordV1(request);
                if (ResultUtils.isFailure(result)) {
                    log.warn("exportDeliverV1 failed. request:{}, rCode:{}", request, result.rCode());
                    return PageVO.of(0, Collections.emptyList());
                }

                List<AwardDeliverRecordV1ExcelVO> voList = FamilyAwardDeliverConvert.I.awardDeliverRecordV1Beans2ExcelVOs(result.target().getList());
                return PageVO.of(result.target().getTotal(), voList);
            });
        } catch (IOException e) {
            log.error("exportDeliverV1 param={},error:", param, e);
        }
    }

    /**
     * 奖励发放记录-列出记录(PP)
     * @return
     */
    @GetMapping("listRecordV2")
    public ResultVO<PageVO<AwardDeliverRecordV2VO>> listRecordV2(GetAwardDeliverRecordParam param){

        RequestAwardDeliverListRecordV2 request = new RequestAwardDeliverListRecordV2();
        request.setPageNumber(param.getPageNumber());
        request.setPageSize(param.getPageSize());
        request.setFamilyUserId(param.getFamilyUserId());
        request.setFamilyId(param.getFamilyId());
        request.setFamilyName(param.getFamilyName());
        request.setMinDeliverTime(param.getMinDeliverTime());
        request.setMaxDeliverTime(param.getMaxDeliverTime());
        request.setAppId(SessionExtUtils.getAppId());

        Result<PageBean<AwardDeliverRecordV2Bean>> result = familyAwardDeliverServiceRemote.listRecordV2(request);
        if (ResultUtils.isFailure(result)) {
            log.warn("listRecordV2 failed. request:{}, rCode:{}", request, result.rCode());
            return ResultVO.failure("系统异常");
        }

        List<AwardDeliverRecordV2VO> voList = FamilyAwardDeliverConvert.I.awardDeliverRecordV2Beans2VOs(result.target().getList());
        return ResultVO.success(PageVO.of(result.target().getTotal(), voList));
    }

    /**
     * 奖励发放记录-导出记录(PP)
     * @param param
     * @param response
     */
    @GetMapping("exportDeliverV2")
    public void exportDeliverV2(GetAwardDeliverRecordParam param, HttpServletResponse response){
        String fileName = "奖励发放记录";
        try {

            FileExportVO<AwardDeliverRecordV2ExcelVO> fileExportVO = new FileExportVO<>();
            fileExportVO.setHead(AwardDeliverRecordV2ExcelVO.class);
            fileExportVO.setFileName(fileName);
            fileExportVO.setQueryAll(true);

            fileExportManager.exportToHttpResponse(fileExportVO, response, (pageNo, pageSize)-> {

                RequestAwardDeliverListRecordV2 request = new RequestAwardDeliverListRecordV2();
                request.setPageNumber(pageNo);
                request.setPageSize(pageSize);
                request.setFamilyUserId(param.getFamilyUserId());
                request.setFamilyId(param.getFamilyId());
                request.setFamilyName(param.getFamilyName());
                request.setMinDeliverTime(param.getMinDeliverTime());
                request.setMaxDeliverTime(param.getMaxDeliverTime());
                request.setAppId(SessionExtUtils.getAppId());

                Result<PageBean<AwardDeliverRecordV2Bean>> result = familyAwardDeliverServiceRemote.listRecordV2(request);
                if (ResultUtils.isFailure(result)) {
                    log.warn("exportDeliverV2 failed. request:{}, rCode:{}", request, result.rCode());
                    return PageVO.of(0, Collections.emptyList());
                }

                List<AwardDeliverRecordV2ExcelVO> voList = FamilyAwardDeliverConvert.I.awardDeliverRecordV2Beans2ExcelVOs(result.target().getList());
                return PageVO.of(result.target().getTotal(), voList);
            });
        } catch (IOException e) {
            log.error("exportDeliverV2 param={},error:", param, e);
        }
    }

}
