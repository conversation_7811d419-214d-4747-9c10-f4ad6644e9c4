package fm.lizhi.ocean.wave.management.model.result.common;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import lombok.Data;

import java.beans.Transient;

/**
 * 飞书卡片消息请求结果
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeiShuCardMessageResult {

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 消息
     */
    private String msg;

    /**
     * 构造失败的结果
     *
     * @param msg 消息
     * @return 失败的结果
     */
    public static FeiShuCardMessageResult failure(String msg) {
        FeiShuCardMessageResult result = new FeiShuCardMessageResult();
        result.setCode((int) GeneralRCode.GENERAL_RCODE_UNKNOWN_ERROR);
        result.setMsg(msg);
        return result;
    }

    /**
     * 是否成功
     */
    @Transient
    public boolean isSuccess() {
        return code != null && code.equals(0);
    }

    /**
     * 是否失败
     */
    @Transient
    public boolean isFailure() {
        return !isSuccess();
    }
}
