package fm.lizhi.ocean.wave.management.model.param.anchor.singer;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 歌手库-厅详情查询参数
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SingerHallInfoParam {

    /**
     * 厅ID
     */
    private Long njId;

    /**
     * 厅主波段号
     */
    private String njBand;

    /**
     * 排序指标
     */
    private String orderMetrics;

    /**
     * 排序类型
     */
    private String orderType;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNo = 1;

    /**
     * 每页数量
     */
    @NotNull(message = "每页数量不能为空")
    @Min(value = 1, message = "每页数量不能小于1")
    private Integer pageSize = 10;
}
