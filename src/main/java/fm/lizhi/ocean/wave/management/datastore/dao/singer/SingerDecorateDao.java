package fm.lizhi.ocean.wave.management.datastore.dao.singer;

import com.google.common.collect.Lists;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerDecorateConditionDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerDecorateFlowPageParamDTO;
import fm.lizhi.ocean.wave.management.model.param.award.singer.SaveSingerDecorateRuleParam;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperatorEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateOperateStatusEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestUpdateSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateCondition;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateConditionExample;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.mapper.SingerDecorateConditionMapper;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateFlowExample;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateRule;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateRuleExample;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.mapper.SingerDecorateFlowMapper;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.mapper.SingerDecorateRuleMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 装扮数据访问层
 */
@Component
@Slf4j
public class SingerDecorateDao {
    /**
     * 全部的常量定义
     */
    private final String ALL_TYPE = "ALL";

    @Resource
    private SingerDecorateFlowMapper singerDecorateFlowMapper;
    @Resource
    private SingerDecorateRuleMapper singerDecorateRuleMapper;
    @Resource
    private SingerDecorateConditionMapper singerDecorateConditionMapper;

    /**
     * 批量插入歌手装扮流水
     *
     * @param decorateFlowList 歌手装扮流水列表
     * @return 是否插入成功
     */
    public boolean batchInsert(List<SingerDecorateFlow> decorateFlowList) {
        if (CollectionUtils.isEmpty(decorateFlowList)) {
            return true;
        }
        return singerDecorateFlowMapper.batchInsert(decorateFlowList) == decorateFlowList.size();
    }



    /**
     * 分页查询歌手装扮规则
     */
    public PageList<SingerDecorateRule> pageSingerDecorateRule(int appId, int pageNo, int pageSize) {

        SingerDecorateRuleExample example = new SingerDecorateRuleExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeletedEqualTo(Boolean.FALSE)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
        ;

        example.setOrderByClause("modify_time desc");

        return singerDecorateRuleMapper.pageByExample(example, pageNo, pageSize);
    }

    /**
     * 分页查询歌手装扮流水
     */
    public PageList<SingerDecorateFlow> pageSingerDecorateFlow(SingerDecorateFlowPageParamDTO param, int pageNo, int pageSize) {
        SingerDecorateFlowExample example = new SingerDecorateFlowExample();
        SingerDecorateFlowExample.Criteria criteria = example.createCriteria()
                .andAppIdEqualTo(param.getAppId())
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                ;

        if (param.getUserId() != null){
            criteria.andUserIdEqualTo(param.getUserId());
        }

        if (StringUtils.isNotBlank(param.getOperator())){
            if (param.getOperator().equals(SingerDecorateFlowOperatorEnum.SYSTEM.getOperator())){
                criteria.andOperatorEqualTo(param.getOperator());
            }else {
                criteria.andOperatorNotEqualTo(SingerDecorateFlowOperatorEnum.SYSTEM.getOperator());
            }
        }

        if (param.getDecorateType() != null){
            criteria.andDecorateTypeEqualTo(param.getDecorateType());
        }

        if (param.getOperateType() != null){
            criteria.andOperateTypeEqualTo(param.getOperateType());
        }

        if (param.getStartOperateTime() != null){
            criteria.andOperateTimeGreaterThanOrEqualTo(new Date(param.getStartOperateTime()));
        }
        if (param.getEndOperateTime() != null){
            criteria.andOperateTimeLessThanOrEqualTo(new Date(param.getEndOperateTime()));
        }

        example.setOrderByClause("create_time desc");

        return singerDecorateFlowMapper.pageByExample(example, pageNo, pageSize);
    }

    /**
     * 获取歌手装扮规则
     */
    public List<SingerDecorateRule> getSingerDecorateRule(int appId, SingerTypeEnum singerType, String songStyle) {

        SingerDecorateRuleExample example = new SingerDecorateRuleExample();
        SingerDecorateRuleExample.Criteria criteria = example.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeletedEqualTo(Boolean.FALSE)
                .andEnabledEqualTo(Boolean.TRUE)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andSingerTypeEqualTo(singerType.getType())
                ;

        ArrayList<String> songStyleList = Lists.newArrayList(ALL_TYPE);
        Optional.ofNullable(songStyle).ifPresent(songStyleList::add);
        criteria.andSongStyleIn(songStyleList);

        return singerDecorateRuleMapper.selectByExample(example);
    }

    /**
     * 获取歌手装扮流水
     */
    public List<SingerDecorateFlow> getDecorateFlowByUserIdAndSingerType(Long userId, SingerTypeEnum singerType, SingerDecorateFlowOperateEnum singerDecorateFlowOperateEnum) {
        SingerDecorateFlowExample example = new SingerDecorateFlowExample();
        SingerDecorateFlowExample.Criteria criteria = example.createCriteria()
                .andUserIdEqualTo(userId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andSingerTypeEqualTo(singerType.getType())
                ;

        if (singerDecorateFlowOperateEnum != null){
            criteria.andOperateTypeEqualTo(singerDecorateFlowOperateEnum.getCode());
        }

        return singerDecorateFlowMapper.selectByExample(example);
    }

    /**
     * 获取歌手装扮流水
     */
    public List<SingerDecorateFlow> getDecorateFlowByTransactionIdAndLteRetryCount(Long transactionId, List<SingerDecorateOperateStatusEnum> statusList) {
        SingerDecorateFlowExample example = new SingerDecorateFlowExample();
        example.createCriteria()
                .andTransactionIdEqualTo(transactionId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andStatusIn(statusList.stream().map(SingerDecorateOperateStatusEnum::getStatus).collect(Collectors.toList()))
                .andRetryCountLessThanOrEqualTo(3)
        ;

        return singerDecorateFlowMapper.selectByExample(example);
    }

    /**
     * 获取可回收的歌手装扮流水
     */
    public List<SingerDecorateFlow> getCanRecoverDecorateFlowByUserIdAndSingerType(Long userId, Integer singerType) {
        SingerDecorateFlowExample example = new SingerDecorateFlowExample();
        example.createCriteria()
                .andUserIdEqualTo(userId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andRecycledEqualTo(false)
                .andSingerTypeEqualTo(singerType)
                .andOperateTypeEqualTo(SingerDecorateFlowOperateEnum.GRANT.getCode())
        ;

        return singerDecorateFlowMapper.selectByExample(example);

    }


    /**
     * 更新歌手装扮流水回收状态
     */
    public void updateSingerDecorateFlowRecycled(Long userId, Integer singerType, Long decorateId, Integer decorateType, Long ruleId, boolean recycled) {
        SingerDecorateFlowExample example = new SingerDecorateFlowExample();
        example.createCriteria()
                .andUserIdEqualTo(userId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andRecycledEqualTo(false)
                .andDecorateTypeEqualTo(decorateType)
                .andSingerTypeEqualTo(singerType)
                .andDecorateIdEqualTo(decorateId)
                .andRuleIdEqualTo(ruleId)
        ;

        SingerDecorateFlow singerDecorateFlow = new SingerDecorateFlow();
        singerDecorateFlow.setModifyTime(new Date());
        singerDecorateFlow.setRecycled(recycled);
        singerDecorateFlowMapper.updateByExample(singerDecorateFlow, example);
    }

    /**
     * 根据状态获取歌手装扮流水
     */
    public PageList<SingerDecorateFlow> getDecorateFlowByStatus(List<SingerDecorateOperateStatusEnum> statusList, int pageSize, int pageNo) {
        SingerDecorateFlowExample example = new SingerDecorateFlowExample();
        example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andStatusIn(statusList.stream().map(SingerDecorateOperateStatusEnum::getStatus).collect(Collectors.toList()));

        return singerDecorateFlowMapper.pageByExample(example, pageNo, pageSize);

    }

    /**
     * 根据用户ID和规则ID列表查询歌手装扮流水
     */
    public List<SingerDecorateFlow> getDecorateFlowByUserIdAndRuleIds(Long userId, int appId, List<Long> ruleIds, SingerDecorateFlowOperateEnum operate, boolean recycled) {
        SingerDecorateFlowExample example = new SingerDecorateFlowExample();
        example.createCriteria()
                .andUserIdEqualTo(userId)
                .andAppIdEqualTo(appId)
                .andRuleIdIn(ruleIds)
                .andRecycledEqualTo(recycled)
                .andOperateTypeEqualTo(operate.getCode())
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        return singerDecorateFlowMapper.selectByExample(example);
    }

    public List<SingerDecorateCondition> getSingerDecorateCondition(Long ruleId) {
        SingerDecorateConditionExample example = new SingerDecorateConditionExample();
        example.createCriteria()
                .andDecorateRuleIdEqualTo(ruleId);
        return singerDecorateConditionMapper.selectByExample(example);
    }

    @Transactional(rollbackFor = Exception.class)
    @DataStore(namespace = "mysql_ocean_wavecenter")
    public void insertSingerDecorateRule(SingerDecorateRule decorateRule, List<SingerDecorateCondition> conditions) {

        int insert = singerDecorateRuleMapper.insert(decorateRule);
        if(insert != 1) {
            throw new RuntimeException("插入歌手装饰规则失败");
        }
        for (SingerDecorateCondition condition : conditions) {
            condition.setDecorateRuleId(decorateRule.getId());
            int inserted = singerDecorateConditionMapper.insert(condition);
            if(inserted != 1) {
                throw new RuntimeException("插入歌手装饰条件失败");
            }
        }
    }

    /**
     * 更新歌手装饰规则
     * @param decorateRule
     * @param conditions
     */
    @Transactional(rollbackFor = Exception.class)
    @DataStore(namespace = "mysql_ocean_wavecenter")
    public void updateSingerDecorateRule(SingerDecorateRule decorateRule, List<SingerDecorateCondition> conditions) {
        SingerDecorateRule ruleExist = singerDecorateRuleMapper.selectByPrimaryKey(SingerDecorateRule.builder().id(decorateRule.getId()).build());
        if (ruleExist == null) {
            log.warn("updateSingerDecorateRule fail;rule not found, id:{}", decorateRule.getId());
            return;
        }
        if(singerDecorateRuleMapper.updateByPrimaryKey(decorateRule) != 1) {
            throw new RuntimeException("更新歌手装饰规则失败");
        }
        for (SingerDecorateCondition condition : conditions) {
            SingerDecorateCondition conditionExist = singerDecorateConditionMapper.selectByPrimaryKey(SingerDecorateCondition.builder().id(condition.getId()).build());
            if (conditionExist == null) {
                log.warn("updateSingerDecorateRule fail;condition not found, id:{}", condition.getId());
                throw new RuntimeException("插入歌手装饰条件失败");
            }
            int inserted = singerDecorateConditionMapper.updateByPrimaryKey(condition);
            if(inserted != 1) {
                throw new RuntimeException("插入歌手装饰条件失败");
            }
        }
    }


    public boolean updateEnable(Long id, Boolean enabled, String operator) {
        SingerDecorateRule select = SingerDecorateRule.builder().id(id).enabled(enabled).build();
        SingerDecorateRule rule = singerDecorateRuleMapper.selectOne(select);
        if(rule != null) {
            return true;
        }
        SingerDecorateRule singerDecorateRule = SingerDecorateRule.builder()
                .id(id).enabled(enabled).build();
        singerDecorateRule.setModifyTime(new Date());
        singerDecorateRule.setOperator(operator);
        return singerDecorateRuleMapper.updateByPrimaryKey(singerDecorateRule) == 1;
    }

    public boolean updateDeleteStatus(Long id, String operator) {
        SingerDecorateRule select = SingerDecorateRule.builder().id(id).deleted(true).build();
        SingerDecorateRule rule = singerDecorateRuleMapper.selectOne(select);
        if(rule != null) {
            return true;
        }
        SingerDecorateRule singerDecorateRule = SingerDecorateRule.builder()
                .id(id).deleted(true).build();
        singerDecorateRule.setModifyTime(new Date());
        singerDecorateRule.setOperator(operator);
        return singerDecorateRuleMapper.updateByPrimaryKey(singerDecorateRule) == 1;
    }

    public List<SingerDecorateRule> findByDecorateId(Long decorateId, Integer singerType, Integer appId) {
        SingerDecorateRuleExample example = new SingerDecorateRuleExample();
        example.createCriteria().andDecorateIdEqualTo(decorateId).andAppIdEqualTo(appId).andSingerTypeEqualTo(singerType);
        return singerDecorateRuleMapper.selectByExample(example);
    }
}
