package fm.lizhi.ocean.wave.management.controller;

import com.ctrip.framework.apollo.core.enums.Env;
import fm.lizhi.ocean.wave.management.common.condition.ConditionalOnDeployEnvNot;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 健康检查控制器, 因为发布平台的配置只能是固定路径, 因此不指定路径前缀.
 */
@ConditionalOnDeployEnvNot({Env.TEST})
@RestController
public class HealthCheckController {

    /**
     * 健康检查接口, 路径固定为"/status", 返回值无实际意义, 只要返回状态码200即可.
     *
     * @return 状态码
     */
    @GetMapping("/status")
    public String status() {
        return HttpStatus.OK.name();
    }
}
