package fm.lizhi.ocean.wave.management.model.param.resource;

import lombok.Data;

import javax.validation.constraints.Max;

/**
 * <AUTHOR>
 * @date 2025/3/17 20:02
 */
@Data
public class RecycleRecommendCardParam {

    /**
     * 发放记录ID
     */
    private Long recordId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 回收数量
     * 有 recordId 根据 recordId 回收
     */
    @Max(value = 999, message = "发放数量不能超过999")
    private Integer num;

    /**
     * 原因
     * 黑叶需要
     */
    private String reason;

}
