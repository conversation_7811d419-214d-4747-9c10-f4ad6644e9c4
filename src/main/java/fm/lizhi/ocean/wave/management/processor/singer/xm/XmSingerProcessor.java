package fm.lizhi.ocean.wave.management.processor.singer.xm;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerInfoDao;
import fm.lizhi.ocean.wave.management.processor.singer.ISingerProcessor;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmSingerProcessor implements ISingerProcessor {

    @Resource
    private SingerInfoDao singerInfoDao;


    @Override
    public List<Long> getRelatedEliminateSingerRecordIds(List<Long> userIds, int appId, int singerType) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }

        List<Integer> singerStatusList = Lists.newArrayList(SingerStatusEnum.EFFECTIVE.getStatus(), SingerStatusEnum.AUTHENTICATING.getStatus());
        //判断当前要操作的歌手列表类型
        if (singerType == SingerTypeEnum.NEW.getType()) {
            //如果是淘汰认证歌手，xm需要关联淘汰高级歌手(不在白名单)
            //高级歌手类型列表
            List<Integer> seniorSingerTypeList = Lists.newArrayList(SingerTypeEnum.QUALITY.getType(), SingerTypeEnum.STAR.getType());
            List<SingerInfo> seniorSingerList = singerInfoDao.getSingerListNotInWhiteList(appId, userIds, seniorSingerTypeList, singerStatusList);
            List<Long> relatedEliminateIds = seniorSingerList.stream().map(SingerInfo::getId).collect(Collectors.toList());
            log.info("xm.new.getRelatedEliminateSingerIds: userIds={}, 关联淘汰歌手数量={}", userIds, relatedEliminateIds.size());
            return relatedEliminateIds;
        } else {
            //如果是高级歌手，就关联淘汰认证歌手了
            List<SingerInfo> newSingerList = singerInfoDao.getSingerListNotInWhiteList(appId, userIds, Lists.newArrayList(SingerTypeEnum.NEW.getType()), singerStatusList);
            List<Long> relatedEliminateIds = newSingerList.stream().map(SingerInfo::getId).collect(Collectors.toList());
            log.info("xm.senior.getRelatedEliminateSingerIds: userIds={}, 关联淘汰歌手数量={}", userIds, relatedEliminateIds.size());
            return relatedEliminateIds;
        }
    }

    @Override
    public void reissueDecorateAfterEliminate(int appId, List<Long> singerIds, int eliminateSingerType, String operator) {
        //TODO 可能存在多等级装扮，显示会有问题
    }

    @Override
    public boolean isNeedRemoveSingerAfterRemoveWhiteList(int appId, Long njId) {
        return false;
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }
}
