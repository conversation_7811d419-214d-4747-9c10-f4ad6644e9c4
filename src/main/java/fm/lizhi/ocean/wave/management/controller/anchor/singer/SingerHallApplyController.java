package fm.lizhi.ocean.wave.management.controller.anchor.singer;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.manager.file.FileExportManager;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerHallApplyConvert;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.ImportHallApplyParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.OperateHallApplyParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.PageHallApplyExcelParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.PageHallApplyParam;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.PageHallApplyResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerSingHallApplyRecordSummaryExcelVO;
import fm.lizhi.ocean.wave.management.model.vo.file.FileExportVO;
import fm.lizhi.ocean.wave.management.remote.service.anchor.singer.SingerHallApplyServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplySourceEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponsePageHallApply;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.GetMapping;


/**
 * 点唱厅管理
 * <AUTHOR>
 */
@RestController
@RequestMapping("/singer/hallApply")
@Slf4j
public class SingerHallApplyController {

    @Autowired
    private SingerHallApplyServiceRemote singerHallApplyServiceRemote;

    @Autowired
    private FileExportManager fileExportManager;

    /**
     * 点唱厅管理-分页查询
     */
    @GetMapping("/list")
    public ResultVO<PageHallApplyResult> pageHallApply(PageHallApplyParam param) {
        Integer appId = SessionExtUtils.getAppId();
        Result<ResponsePageHallApply> result = singerHallApplyServiceRemote.pageHallApplyList(
            SingerHallApplyConvert.I.buildRequestPageHallApply(param, appId));
        if (ResultUtils.isFailure(result)) {
            log.warn("page hall apply fail, rCode:{}", result.rCode());
            return ResultVO.failure(result.getMessage());
        }
        return ResultVO.success(SingerHallApplyConvert.I.convertPageHallApplyResult(result.target()));
    }

    @GetMapping("/list/export")
    public void exportHallApply(PageHallApplyExcelParam param, HttpServletResponse response) {
        FileExportVO<SingerSingHallApplyRecordSummaryExcelVO> fileExportVO = new FileExportVO<>();
        fileExportVO.setHead(SingerSingHallApplyRecordSummaryExcelVO.class);
        fileExportVO.setFileName("点唱厅管理-分页查询");
        fileExportVO.setQueryAll(param.isQueryAll());
        fileExportVO.setPageNo(param.isQueryAll() ? 1 : param.getPageNo());
        fileExportVO.setPageSize(param.isQueryAll() ? 500 : param.getPageSize());
        
        Integer appId = SessionExtUtils.getAppId();
        try {
            fileExportManager.exportToHttpResponse(fileExportVO, response, (pageNo, pageSize) -> {
                
                Result<ResponsePageHallApply> result = singerHallApplyServiceRemote.pageHallApplyList(
                    SingerHallApplyConvert.I.buildRequestPageHallApply(param, appId));
                if (ResultUtils.isFailure(result)) {
                    log.warn("page hall apply fail, rCode:{}", result.rCode());
                    return PageVO.of(0, new ArrayList<>());
                }
                List<SingerSingHallApplyRecordSummaryExcelVO> voList = SingerHallApplyConvert.I.convertSingerSingHallApplyRecordSummaryExcelVOs(result.target().getList());
                return PageVO.of(result.target().getTotal(), voList);
            });
        } catch (IOException e) {
            log.error("export hall apply fail, error:{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }
    

    /**
     * 点唱厅操作
     */
    @PostMapping("/operate")
    public ResultVO<Void> operateHallApply(@RequestBody OperateHallApplyParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        Result<Void> result = singerHallApplyServiceRemote.operateHallApply(
            SingerHallApplyConvert.I.buildRequestOperateHallApply(param, appId, operator));
        if (ResultUtils.isFailure(result)) {
            log.warn("operate hall apply fail, rCode:{}", result.rCode());
            return ResultVO.failure(result.getMessage());
        }
        return ResultVO.success();
    }

    /**
     * 点唱厅导入
     */
    @PostMapping("/import")
    public ResultVO<List<String>> importHallApply(@RequestBody ImportHallApplyParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        Result<List<String>> result = singerHallApplyServiceRemote.importHallApply(
            SingerHallApplyConvert.I.buildRequestImportHallApply(param, appId, operator, SingerHallApplySourceEnum.WAVE_CENTER));
        if (ResultUtils.isFailure(result)) {
            log.warn("import hall apply fail, rCode:{}", result.rCode());
            return ResultVO.failure(result.getMessage());
        }
        return ResultVO.success(result.target());
    }
}
