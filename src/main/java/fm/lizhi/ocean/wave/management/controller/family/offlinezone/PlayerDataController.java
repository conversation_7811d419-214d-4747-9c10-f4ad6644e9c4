package fm.lizhi.ocean.wave.management.controller.family.offlinezone;

import fm.lizhi.ocean.wave.management.manager.family.offlinezone.PlayerDataManager;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.BatchResetPlayerDataParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.ListPlayerDataParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListPlayerDataResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 线下主播数据控制器
 */
@RestController
@RequestMapping("/offline/player")
@Slf4j
public class PlayerDataController {

    @Autowired
    private PlayerDataManager playerDataManager;

    /**
     * 分页查询线下主播数据（带保护状态）
     *
     * @param param 查询参数
     * @return 查询结果的VO
     */
    @GetMapping("/list")
    public ResultVO<PageVO<ListPlayerDataResult>> listPlayerDataWithProtection(@Validated ListPlayerDataParam param) {
        log.info("查询线下主播数据，参数: {}", param);

        Integer appId = SessionExtUtils.getAppId();
        param.setAppId(appId);

        return playerDataManager.listPlayerDataWithProtection(param);
    }


    /**
     * 批量重置线下主播数据
     *
     * @param param 重置参数
     * @return 重置结果的VO
     */
    @PostMapping("/batchReset")
    public ResultVO<Void> batchResetPlayerData(@RequestBody @Validated BatchResetPlayerDataParam param) {
        log.info("重置线下主播数据，参数: {}", param);
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        return playerDataManager.batchResetPlayerData(param, operator, appId);
    }


}