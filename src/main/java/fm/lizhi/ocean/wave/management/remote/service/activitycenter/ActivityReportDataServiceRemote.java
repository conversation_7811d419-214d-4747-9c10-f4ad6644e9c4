package fm.lizhi.ocean.wave.management.remote.service.activitycenter;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataDetailBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataGiftBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataPlayerBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataSummaryBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityReportDataService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 活动数据
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityReportDataServiceRemote {

    @Autowired
    private ActivityReportDataService activityReportDataService;


    /**
     * 活动数据汇总
     */
    public Result<ActivityReportDataSummaryBean> getReportSummary(Long activityId, int appId){
        Result<ActivityReportDataSummaryBean> result = activityReportDataService.getReportSummary(activityId, appId);
        if (ResultUtils.isFailure(result)){
            log.warn("getReportSummary fail, activityId={}, appId={}", activityId, appId);
        }
        return result;
    }


    /**
     * 活动数据趋势图
     */
    public Result<List<ActivityReportDataDetailBean>> getReportDetail(Long activityId, int appId){
        Result<List<ActivityReportDataDetailBean>> result = activityReportDataService.getReportDetail(activityId, appId);
        if (ResultUtils.isFailure(result)){
            log.warn("getReportDetail fail, activityId={}, appId={}", activityId, appId);
        }
        return result;
    }


    /**
     * 活动数据主播表现
     */
    public Result<PageBean<ActivityReportDataPlayerBean>> pageReportPlayer(Long activityId, int appId, int pageNo, int pageSize){
        Result<PageBean<ActivityReportDataPlayerBean>> result = activityReportDataService.pageReportPlayer(activityId, appId, pageNo, pageSize);
        if (ResultUtils.isFailure(result)){
            log.warn("pageReportPlayer fail, activityId={}, appId={}", activityId, appId);
        }
        return result;
    }


    /**
     * 活动数据送礼明细
     */
    public Result<PageBean<ActivityReportDataGiftBean>> pageReportGift(Long activityId, int appId, int pageNo, int pageSize){
        Result<PageBean<ActivityReportDataGiftBean>> result = activityReportDataService.pageReportGift(activityId, appId, pageNo, pageSize);
        if (ResultUtils.isFailure(result)){
            log.warn("pageReportGift fail, activityId={}, appId={}", activityId, appId);
        }

        return result;
    }


}
