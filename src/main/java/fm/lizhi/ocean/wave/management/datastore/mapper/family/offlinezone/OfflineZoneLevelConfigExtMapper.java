package fm.lizhi.ocean.wave.management.datastore.mapper.family.offlinezone;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelConfig;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface OfflineZoneLevelConfigExtMapper {

    @Select("SELECT * FROM `offline_zone_level_config`\n" +
            "WHERE `app_id` = #{appId} AND `deploy_env` = #{deployEnv} AND `deleted` = 0\n" +
            "ORDER BY `level_order` ASC")
    List<OfflineZoneLevelConfig> listLevelConfig(@Param("appId") Integer appId, @Param("deployEnv") String deployEnv);
}
