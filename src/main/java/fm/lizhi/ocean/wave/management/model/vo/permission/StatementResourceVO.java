package fm.lizhi.ocean.wave.management.model.vo.permission;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/20 16:33
 */
@Data
@Accessors(chain = true)
public class StatementResourceVO {

    /**
     * 资源类型
     * role=平台角色， permission=权限
     */
    private String resourceType;

    /**
     * 资源列表
     */
    private List<StatementResourceItemVO> resources;

}
