package fm.lizhi.ocean.wave.management.model.converter.activitycenter;

import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityNoticeResult;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityNoticeConfig;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityNoticeConfigConvert {

    ActivityNoticeConfigConvert I = Mappers.getMapper(ActivityNoticeConfigConvert.class);

    List<ActivityNoticeResult> convertActivityNoticeResultList(List<ResponseGetActivityNoticeConfig> target);

}
