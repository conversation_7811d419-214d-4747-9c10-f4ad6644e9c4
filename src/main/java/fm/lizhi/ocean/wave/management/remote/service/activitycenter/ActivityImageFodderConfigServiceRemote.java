package fm.lizhi.ocean.wave.management.remote.service.activitycenter;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityImageFodderBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestPageActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseSaveActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityImageFodderConfigService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ActivityImageFodderConfigServiceRemote {


    @Autowired
    private ActivityImageFodderConfigService activityImageFodderConfigService;


    public Result<ResponseSaveActivityImageFodder> save(RequestSaveActivityImageFodder request){
        Result<ResponseSaveActivityImageFodder> result = activityImageFodderConfigService.saveImageFodder(request);
        if (ResultUtils.isFailure(result)){
            log.warn("save activity image fodder config failed. rCode:{}", result.rCode());
        }

        return result;
    }


    public Result<Void> update(RequestUpdateActivityImageFodder request){
        Result<Void> result = activityImageFodderConfigService.updateImageFodder(request);
        if (ResultUtils.isFailure(result)){
            log.warn("update activity image fodder config failed. id:{}, rCode:{}", request.getId(), result.rCode());
        }

        return result;
    }

    public Result<Void> delete(Long id, Integer appId, String operator){
        Result<Void> result = activityImageFodderConfigService.deleteImageFodder(id, operator);
        if (ResultUtils.isFailure(result)){
            log.warn("delete activity image fodder config failed. id:{}, appId:{}, operator:{}, rCode:{}",
                    id, appId, operator, result.rCode());
        }
        return result;
    }

    public Result<PageBean<ActivityImageFodderBean>> list(RequestPageActivityImageFodder request){
        Result<PageBean<ActivityImageFodderBean>> result = activityImageFodderConfigService.pageImageFodder(request);
        if (ResultUtils.isFailure(result)){
            log.warn("list activity image fodder config failed. rCode:{}", result.rCode());
        }

        return result;
    }
}
