package fm.lizhi.ocean.wave.management.model.converter.resource;

import fm.lizhi.ocean.wave.management.model.vo.resource.ListShortNumberVO;
import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.response.ResponseListShortNumber;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ShortNumberConvert {

    ShortNumberConvert I = Mappers.getMapper(ShortNumberConvert.class);

    List<ListShortNumberVO> toListShortNumberVOS(List<ResponseListShortNumber> beans);
}
