package fm.lizhi.ocean.wave.management.controller;

import fm.lizhi.ocean.wave.management.manager.ActivityReportDataManager;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityReportDataDetailResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityReportDataGiftResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityReportDataPlayerResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityReportDataSummaryResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/activity/report")
@Slf4j
public class ActivityReportDataController {

    @Autowired
    private ActivityReportDataManager activityReportDataManager;

    /**
     * 活动数据-数据汇总
     */
    @GetMapping("/summary")
    public ResultVO<ActivityReportDataSummaryResult> getReportSummary(@RequestParam("activityId") Long activityId) {
        Integer appId = SessionExtUtils.getAppId();
        return activityReportDataManager.getReportSummary(activityId, appId);
    }

    /**
     * 活动数据-数据明细（趋势图）
     */
    @GetMapping("/detail")
    public ResultVO<List<ActivityReportDataDetailResult>> getReportDetail(@RequestParam("activityId") Long activityId) {
        Integer appId = SessionExtUtils.getAppId();
        return activityReportDataManager.getReportDetail(activityId, appId);
    }



    /**
     * 活动数据-主播表现
     */
    @GetMapping("/player")
    public ResultVO<PageVO<ActivityReportDataPlayerResult>> pageReportPlayer(@RequestParam("activityId") Long activityId,
                                                                             @RequestParam(required = false, name = "pageNo", defaultValue = "1") int pageNo,
                                                                             @RequestParam(required = false, name = "pageSize", defaultValue = "20")  int pageSize) {
        Integer appId = SessionExtUtils.getAppId();
        return activityReportDataManager.pageReportPlayer(activityId, appId, pageNo, pageSize);
    }



    /**
     * 活动数据-用户送礼明细
     */
    @GetMapping("/giftDetail")
    public ResultVO<PageVO<ActivityReportDataGiftResult>> pageReportGift(@RequestParam("activityId") Long activityId,
                                                                         @RequestParam(required = false, name = "pageNo", defaultValue = "1") int pageNo,
                                                                         @RequestParam(required = false, name = "pageSize", defaultValue = "20")  int pageSize) {
        Integer appId = SessionExtUtils.getAppId();
        return activityReportDataManager.pageReportGift(activityId, appId, pageNo, pageSize);
    }



}
