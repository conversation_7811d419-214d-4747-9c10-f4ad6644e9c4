package fm.lizhi.ocean.wave.management.config;

import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.godzilla.api.service.AiActivityPlanImageService;
import fm.lizhi.ocean.wave.management.config.apollo.DubboClientConfig;
import fm.lizhi.ocean.wave.management.config.apollo.DubboClientProperties;
import fm.lizhi.ocean.wave.platform.api.permission.service.PermissionService;
import fm.lizhi.ocean.wave.platform.api.permission.service.PlatformRoleService;
import fm.lizhi.ocean.wave.platform.api.permission.service.StatementService;
import fm.lizhi.ocean.wave.platform.api.platform.service.WaveAnnouncementManagementService;
import fm.lizhi.ocean.wave.platform.api.platform.version.service.WaveVersionManagementService;
import fm.lizhi.ocean.wave.platform.api.platform.webp.service.WebpConvertService;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.*;
import fm.lizhi.ocean.wavecenter.api.award.singer.serivce.SingerDecorateOperateService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityAiResultRateService;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityApplyService;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityCommonService;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityOfficialSeatTimeService;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityReportDataService;
import fm.lizhi.ocean.wavecenter.api.award.family.service.FamilyAwardDeliverService;
import fm.lizhi.ocean.wavecenter.api.award.family.service.FamilyLevelAwardRuleService;
import fm.lizhi.ocean.wavecenter.api.award.family.service.FamilyOtherAwardRuleService;
import fm.lizhi.ocean.wavecenter.api.award.singer.serivce.SingerDecorateFlowService;
import fm.lizhi.ocean.wavecenter.api.award.singer.serivce.SingerDecorateRuleService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.*;
import fm.lizhi.ocean.wavecenter.api.gift.service.GiftService;
import fm.lizhi.ocean.wavecenter.api.grow.capability.service.CapabilityService;
import fm.lizhi.ocean.wavecenter.api.grow.level.service.FamilyLevelConfigService;
import fm.lizhi.ocean.wavecenter.api.message.service.WcNoticeConfigService;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.service.TaskTemplateService;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.service.DecorateManagementService;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.service.DecorateService;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.service.RecommendCardService;
import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.service.ShortNumberService;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.api.user.service.UserFamilyService;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.service.BatchImportSingerService;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneProtectionService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 服务客户端配置
 */
@Configuration
public class ServiceClientConfiguration {

    private final DubboClientConfig dubboClientConfig;

    public ServiceClientConfiguration(DubboClientConfig dubboClientConfig) {
        this.dubboClientConfig = dubboClientConfig;
    }

    private <T> T buildDubboClient(Class<T> type) {
        DubboClientProperties commonDubboClientProperties = dubboClientConfig.getCommon();
        return buildDubboClient(type, commonDubboClientProperties);
    }

    private <T> T buildDubboClient(Class<T> type, DubboClientProperties dubboClientProperties) {
        DubboClientBuilder<T> dubboClientBuilder = new DubboClientBuilder<>(type);
        dubboClientBuilder.connections(dubboClientProperties.getConnections());
        dubboClientBuilder.timeoutInMillis((int) dubboClientProperties.getTimeout().toMillis());
        dubboClientBuilder.retries(dubboClientProperties.getRetries());
        if (!dubboClientProperties.isAsync()) {
            dubboClientBuilder.sync();
        }
        return dubboClientBuilder.build();
    }

    /**
     * 活动申请服务
     *
     * @return 活动申请服务
     */
    @Bean
    public ActivityApplyService activityApplyService() {
        return buildDubboClient(ActivityApplyService.class);
    }

    /**
     * 活动分类配置服务
     *
     * @return 活动分类配置服务
     */
    @Bean
    public ActivityClassificationConfigService activityClassificationConfigService() {
        return buildDubboClient(ActivityClassificationConfigService.class);
    }


    /**
     * 图片素材服务
     * @return 图片素材服务
     */
    @Bean
    public ActivityImageFodderConfigService activityImageFodderConfigService() {
        return buildDubboClient(ActivityImageFodderConfigService.class);
    }

    /**
     * 活动等级服务
     * @return 活动等级服务
     */
    @Bean
    public ActivityLevelConfigService activityLevelConfigService() {
        return buildDubboClient(ActivityLevelConfigService.class);
    }

    /**
     * 推荐卡服务
     */
    @Bean
    public ActivityRecommendCardConfigService activityRecommendCardConfigService() {
        return buildDubboClient(ActivityRecommendCardConfigService.class);
    }

    /**
     * 资源配置服务
     */
    @Bean
    public ActivityResourceConfigService activityResourceConfigService() {
        return buildDubboClient(ActivityResourceConfigService.class);
    }

    /**
     * 提报规则服务
     */
    @Bean
    public ActivityRuleConfigService activityRuleConfigService() {
        return buildDubboClient(ActivityRuleConfigService.class);
    }

    /**
     * 装扮服务
     * @return
     */
    @Bean
    public ActivityDecorateService activityDecorateService(){
        return buildDubboClient(ActivityDecorateService.class);
    }

    /**
     * 数据报表服务
     */
    @Bean
    public ActivityReportDataService activityReportDataService(){
        return buildDubboClient(ActivityReportDataService.class);
    }

    /**
     * 基础枚举配置服务
     */
    @Bean
    public ActivityCommonService activityCommonService(){
        return buildDubboClient(ActivityCommonService.class);
    }

    /**
     * 活动模板配置服务
     *
     * @return 活动模板配置服务
     */
    @Bean
    public ActivityTemplateConfigService activityTemplateService() {
        return buildDubboClient(ActivityTemplateConfigService.class);
    }

    /**
     * 活动官频位时间服务
     *
     * @return 活动官频位时间服务
     */
    @Bean
    public ActivityOfficialSeatTimeService activityOfficialSeatTimeService() {
        return buildDubboClient(ActivityOfficialSeatTimeService.class);
    }

    /**
     * 用户通用服务
     *
     * @return 用户通用服务
     */
    @Bean
    public UserCommonService userCommonService() {
        return buildDubboClient(UserCommonService.class);
    }

    /**
     * 用户家族服务
     *
     * @return 用户家族服务
     */
    @Bean
    public UserFamilyService userFamilyService() {
        return buildDubboClient(UserFamilyService.class);
    }

    /**
     * 活动后台运营操作服务
     *
     * @return 活动后台运营操作服务
     */
    @Bean
    public ActivityAdminOperateService activityAdminOperateService() {
        return buildDubboClient(ActivityAdminOperateService.class);
    }

    @Bean
    public ActivityNoticeConfigService activityNoticeConfigService() {
        return buildDubboClient(ActivityNoticeConfigService.class);
    }

    @Bean
    public ActivityInfoQueryService activityInfoQueryService() {
        return buildDubboClient(ActivityInfoQueryService.class);
    }

    @Bean
    public PlatformRoleService platformRoleService(){
        return buildDubboClient(PlatformRoleService.class);
    }

    @Bean
    public PermissionService permissionService(){
        return buildDubboClient(PermissionService.class);
    }

    @Bean
    public StatementService statementService(){
        return buildDubboClient(StatementService.class);
    }

    /**
     * 创作者版本管理服务
     *
     * @return 创作者版本管理服务
     */
    @Bean
    public WaveVersionManagementService waveVersionManagementService() {
        return buildDubboClient(WaveVersionManagementService.class);
    }

    /**
     * 活动工具配置服务
     *
     * @return 活动工具配置服务
     */
    @Bean
    public ActivityToolsConfigService activityToolsConfigService() {
        return buildDubboClient(ActivityToolsConfigService.class);
    }


    @Bean
    public SingerVerifyAdminService singerVerifyAdminService(){
        return buildDubboClient(SingerVerifyAdminService.class);
    }

    @Bean
    public SingerInfoAdminService singerInfoAdminService(){
        return buildDubboClient(SingerInfoAdminService.class);
    }

    @Bean
    public FamilyLevelConfigService familyLevelConfigService(){
        return buildDubboClient(FamilyLevelConfigService.class);
    }

    @Bean
    public FamilyLevelAwardRuleService familyLevelAwardRuleService() {
        return buildDubboClient(FamilyLevelAwardRuleService.class);
    }

    @Bean
    public FamilyOtherAwardRuleService familyOtherAwardRuleService() {
        return buildDubboClient(FamilyOtherAwardRuleService.class);
    }

    @Bean
    public RecommendCardService recommendCardService(){
        return buildDubboClient(RecommendCardService.class);
    }

    @Bean
    public DecorateService decorateService(){
        return buildDubboClient(DecorateService.class);
    }

    @Bean
    public FamilyAwardDeliverService familyAwardDeliverService(){
        return buildDubboClient(FamilyAwardDeliverService.class);
    }

    @Bean
    public ShortNumberService shortNumberService(){
        return buildDubboClient(ShortNumberService.class);
    }

    @Bean
    public SingerHallApplyService singerHallApplyService(){
        return buildDubboClient(SingerHallApplyService.class);
    }

    @Bean
    public SingerDecorateRuleService singerDecorateRuleService() {
        return buildDubboClient(SingerDecorateRuleService.class);
    }

    @Bean
    public SingerDecorateFlowService singerDecorateFlowService() {
        return buildDubboClient(SingerDecorateFlowService.class);
    }

    @Bean
    public SingerConfigService singerConfigService(){
        return buildDubboClient(SingerConfigService.class);
    }

    @Bean
    public WebpConvertService webpConvertService() {
        return buildDubboClient(WebpConvertService.class);
    }

    @Bean
    public AiActivityPlanImageService aiActivityPlanImageService() {
        DubboClientProperties aiImageDubboClientProperties = dubboClientConfig.getAiImage();
        return buildDubboClient(AiActivityPlanImageService.class, aiImageDubboClientProperties);
    }

    @Bean
    public ActivityAiResultRateService activityAiResultRateService() {
        return buildDubboClient(ActivityAiResultRateService.class);
    }

    @Bean
    public DecorateManagementService decorateManagementService() {
        return buildDubboClient(DecorateManagementService.class);
    }

    @Bean
    public WcNoticeConfigService wcNoticeConfigService() {
        return buildDubboClient(WcNoticeConfigService.class);
    }

    @Bean
    public WaveAnnouncementManagementService waveAnnouncementManagementService() {
        return buildDubboClient(WaveAnnouncementManagementService.class);
    }

    @Bean
    public TaskTemplateService taskTemplateService() {
        return buildDubboClient(TaskTemplateService.class);
    }

    @Bean
    public CapabilityService capabilityService() {
        return buildDubboClient(CapabilityService.class);
    }

    @Bean
    public SingerDecorateOperateService singerDecorateOperateService() {
        return buildDubboClient(SingerDecorateOperateService.class);
    }

    @Bean
    public SingerChatService singerChatService() {
        return buildDubboClient(SingerChatService.class);
    }


    @Bean
    public BatchImportSingerService batchImportSingerService(){
        return buildDubboClient(BatchImportSingerService.class);
    }

    @Bean
    public GuidGenerator guidGenerator() {
        return new GuidGenerator();
    }

    @Bean
    public OfflineZoneProtectionService offlineZoneProtectionService() {
        return buildDubboClient(OfflineZoneProtectionService.class);
    }

    @Bean
    public GiftService giftService() {
        return buildDubboClient(GiftService.class);
    }
}
