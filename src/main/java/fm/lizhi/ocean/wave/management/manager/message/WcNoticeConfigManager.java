package fm.lizhi.ocean.wave.management.manager.message;

import fm.lizhi.ocean.wave.management.model.param.message.WcNoticeUpdateStatusParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.message.WcNoticeConfigConvert;
import fm.lizhi.ocean.wave.management.model.param.message.WcNoticeConfigParam;
import fm.lizhi.ocean.wave.management.model.param.message.WcNoticeConfigQueryParam;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.message.WcNoticeConfigResVO;
import fm.lizhi.ocean.wave.management.remote.service.message.WcNoticeConfigRemote;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseWcNoticeConfigPage;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WcNoticeConfigManager {

    @Autowired
    private WcNoticeConfigRemote wcNoticeConfigRemote;

    /**
     * 删除公告配置
     * @param id 公告配置ID
     * @return 删除结果，ResultVO<Void>
     */
    public ResultVO<Void> deleteWaveCenterNotice(Long id) {
        Result<Void> result = wcNoticeConfigRemote.deleteWcNoticeConfigById(id);
        if (result.rCode() != 0) {
            log.warn("删除公告配置失败，id: {}, rCode: {}", id, result.rCode());
            return ResultVO.failure("删除失败");
        }
        return ResultVO.success();
    }

    /**
     * 保存公告配置
     * @param param 公告配置参数
     * @param operator 操作人
     * @param appId 应用ID
     * @return 保存结果
     */
    public ResultVO<Void> saveWcNoticeConfig(WcNoticeConfigParam param, String operator, Integer appId) {
        Result<Void> result = wcNoticeConfigRemote.saveWcNoticeConfig(param, operator, appId);
        if (result.rCode() != 0) {
            log.warn("保存公告配置失败，param: {}, operator: {}, appId: {}", param, operator, appId);
            return ResultVO.failure("保存失败");
        }
        return ResultVO.success();
    }

    /**
     * 分页查询公告配置
     * @param param 查询参数
     * @param appId 应用ID
     * @return 分页结果
     */
    public ResultVO<WcNoticeConfigResVO> getWaveCenterNoticeList(WcNoticeConfigQueryParam param, Integer appId) {
        Result<ResponseWcNoticeConfigPage> result = wcNoticeConfigRemote.queryWcNoticeConfigPage(param, appId);
        if (result == null || result.rCode() != 0) {
            log.warn("查询公告配置列表失败，param: {}, appId: {}, rCode: {}, msg: {}", param, appId, result != null ? result.rCode() : null, result != null ? result.getMessage() : null);
            return ResultVO.failure(result != null ? result.rCode() : -1, result != null ? result.getMessage() : "查询失败");
        }
        return ResultVO.success(WcNoticeConfigConvert.I.respToConfigVo(result.target()));
    }

    /**
     * 上下架
     * @param param 公告配置参数
     * @param operator 操作人
     * @param appId 应用ID
     * @return 结果
     */
    public ResultVO<Void> updateNoticeStatus(WcNoticeUpdateStatusParam param, String operator, Integer appId) {
        Result<Void> result = wcNoticeConfigRemote.updateNoticeStatus(param, operator, appId);
        if (result.rCode() != 0) {
            log.warn("修改上下架失败，param: {}, operator: {}, appId: {}", param, operator, appId);
            return ResultVO.failure("修改失败");
        }
        return ResultVO.success();
    }

}
