package fm.lizhi.ocean.wave.management.config.apollo;

import lombok.Data;

/**
 * 业务配置项
 */
@Data
public class BusinessProperties {

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 罗马上传地址, 例如{@code https://ximi-upload.qyinliao.com}, 实际上只有前端使用, 这里只是为了方便比对
     */
    private String romeAddress;

    /**
     * 罗马上传内网地址, 例如{@code http://ximi-upload.ximi-internal.com}
     */
    private String romeInnerAddress;

    /**
     * CDN地址, 例如{@code https://cdn.qyinliao.com}
     */
    private String cdnHost;

    /**
     * CDN内网地址, 例如{@code http://cdn.ximi-internal.com}
     */
    private String cdnInnerHost;

    /**
     * CDN外网地址, 给第三方访问, 例如{@code http://cdnofficeout.lz225.com}. 如果为空则取{@link #cdnHost}相同配置.
     */
    private String cdnOuterHost;
}
