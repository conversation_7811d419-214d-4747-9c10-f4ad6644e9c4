package fm.lizhi.ocean.wave.management.model.converter.activitycenter;

import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityBaseConfigResult;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityConfigBean;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityBaseEnumConfigConvert {

    ActivityBaseEnumConfigConvert I = Mappers.getMapper(ActivityBaseEnumConfigConvert.class);


    ActivityBaseConfigResult toActivityBaseConfigResult(ResponseActivityConfigBean target);
}
