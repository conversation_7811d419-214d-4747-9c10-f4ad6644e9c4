package fm.lizhi.ocean.wave.management.model.converter.activitycenter;

import fm.lizhi.ocean.wave.management.model.param.activitycenter.*;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.CreateActivityTemplateResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.GetActivityTemplateResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.GetActivityTemplateShelfStatusResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.PageActivityTemplateResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityProcessBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceExtraBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplatePageBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestCreateActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestPageActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityTemplateShelfStatus;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseCreateActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplateShelfStatus;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 活动模板转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityTemplateConverter {

    /**
     * 转换为创建活动模板RPC请求
     *
     * @param param    创建活动模板参数
     * @param appId    应用ID
     * @param operator 操作人
     * @return 创建活动模板RPC请求
     */
    @Mapping(target = "whiteNjUserGroupId", ignore = true)
    RequestCreateActivityTemplate toRequestCreateActivityTemplate(CreateActivityTemplateParam param, Integer appId, String operator);

    @Mapping(target = "resourceCode", ignore = true)
    @Mapping(target = "name", ignore = true)
    ActivityTemplateFlowResourceBean toActivityTemplateFlowResourceBean(SaveActivityTemplateParam.FlowResource flowResource);

    @Mapping(target = "durationLimit", ignore = true)
    ActivityTemplateFlowResourceExtraBean toActivityTemplateFlowResourceExtraBean(SaveActivityTemplateParam.FlowResourceExtra extra);

    /**
     * 转换为创建活动模板WEB结果
     *
     * @param resp 创建活动模板响应
     * @return 创建活动模板WEB结果
     */
    CreateActivityTemplateResult toCreateActivityTemplateResult(ResponseCreateActivityTemplate resp);

    /**
     * 转换为更新活动模板RPC请求
     *
     * @param param    更新活动模板参数
     * @param appId    应用ID
     * @param operator 操作人
     * @return 更新活动模板RPC请求
     */
    @Mapping(target = "whiteNjUserGroupId", ignore = true)
    RequestUpdateActivityTemplate toRequestUpdateActivityTemplate(UpdateActivityTemplateParam param, Integer appId, String operator);

    /**
     * 转换为更新活动模板上下架状态RPC请求
     *
     * @param param    更新活动模板上下架状态参数
     * @param operator 操作人
     * @return 更新活动模板上下架状态RPC请求
     */
    RequestUpdateActivityTemplateShelfStatus toRequestUpdateActivityTemplateShelfStatus(UpdateActivityTemplateShelfStatusParam param, String operator, Integer appId);

    /**
     * 转换为获取活动模板上下架状态WEB结果
     *
     * @param resp 获取活动模板上下架状态响应
     * @return 获取活动模板上下架状态WEB结果
     */
    GetActivityTemplateShelfStatusResult toGetActivityTemplateShelfStatusResult(ResponseGetActivityTemplateShelfStatus resp);

    /**
     * 转换为分页查询活动模板RPC请求
     *
     * @param param 分页查询活动模板参数
     * @return 分页查询活动模板RPC请求
     */
    @Mapping(target = "classIds", source = "classId")
    RequestPageActivityTemplate toRequestPageActivityTemplate(PageActivityTemplateParam param);

    /**
     * 转换为分页查询活动模板WEB结果
     *
     * @param pageBean 分页查询活动模板响应
     * @return 分页查询活动模板WEB结果
     */
    PageVO<PageActivityTemplateResult> toPageActivityTemplateResultPageVO(PageBean<ActivityTemplatePageBean> pageBean);

    /**
     * 转换为获取活动模板WEB结果
     *
     * @param resp 获取活动模板响应
     * @return 获取活动模板WEB结果
     */
    GetActivityTemplateResult toGetActivityTemplateResult(ResponseGetActivityTemplate resp);
}
