package fm.lizhi.ocean.wave.management.model.dto.singer;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SingerAuditParamDTO {

    /**
     * 操作人
     */
    private String operator;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 点唱厅状态
     * 通过操作才需要传
     */
    private SingerHallApplyStatusEnum singerHallStatus;

    /**
     * 目标审核状态
     */
    private Integer targetAuditStatus;

    /**
     * 歌手类型
     */
    private Integer singerType;

    /**
     * 是否需要校验点唱厅状态
     */
    private boolean needCheckSingerHallStatus;

    /**
     * 通过的曲风
     */
    private String passSongStyle;

    /**
     * 是否是原创歌手
     */
    private boolean originalSinger;

}
