
package fm.lizhi.ocean.wave.management.model.converter.activitycenter;

import fm.lizhi.ocean.wave.management.model.param.activitycenter.PageActivityImageFodderParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityImageFodderParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityImageFodderParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityImageFodderResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.SaveActivityImageFodderResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityImageFodderBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestPageActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseSaveActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR, unmappedSourcePolicy = ReportingPolicy.ERROR)
public interface ActivityImageFodderConfigConvert {

    ActivityImageFodderConfigConvert I = Mappers.getMapper(ActivityImageFodderConfigConvert.class);

    @Mappings({
            @Mapping(source = "appId", target = "appId"),
            @Mapping(source = "operator", target = "operator"),

    })
    RequestSaveActivityImageFodder toRequestSaveActivityImageFodder(SaveActivityImageFodderParam param, Integer appId, String operator);

    @Mappings({
            @Mapping(source = "appId", target = "appId"),
            @Mapping(source = "operator", target = "operator"),

    })
    RequestUpdateActivityImageFodder toRequestUpdateActivityImageFodder(UpdateActivityImageFodderParam param, Integer appId, String operator);

    RequestPageActivityImageFodder toRequestPageActivityImageFodder(PageActivityImageFodderParam param, Integer appId);

    @BeanMapping(unmappedTargetPolicy = ReportingPolicy.ERROR, unmappedSourcePolicy = ReportingPolicy.IGNORE)
    PageVO<ActivityImageFodderResult> toActivityImageFodderResultPageVO(PageBean<ActivityImageFodderBean> target);

    SaveActivityImageFodderResult toSaveActivityImageFodderResult(ResponseSaveActivityImageFodder target);
}
