package fm.lizhi.ocean.wave.management.config.apollo;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;

/**
 * dubbo客户端配置, 在application namespace中配置
 */
@ConfigurationProperties(prefix = "dubbo.client")
@Data
public class DubboClientConfig {

    /**
     * 通用配置
     */
    private DubboClientProperties common = buildCommonProperties();

    /**
     * AI图片配置
     */
    private DubboClientProperties aiImage = buildAiImageProperties();

    private static DubboClientProperties buildCommonProperties() {
        return new DubboClientProperties();
    }

    private static DubboClientProperties buildAiImageProperties() {
        DubboClientProperties properties = new DubboClientProperties();
        properties.setTimeout(Duration.ofMinutes(1));
        return properties;
    }
}
