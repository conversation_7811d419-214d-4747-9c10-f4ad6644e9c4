package fm.lizhi.ocean.wave.management.model.vo.message;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class WcNoticeConfigItemVO {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    private String title;
    private String content;
    private Integer type;
    private Integer status;
    private Long effectTime;
    private String operator;
    private Long modifyTime;
} 