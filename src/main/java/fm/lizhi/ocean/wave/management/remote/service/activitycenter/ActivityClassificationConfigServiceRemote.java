package fm.lizhi.ocean.wave.management.remote.service.activitycenter;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityBigClassBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityClassConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityClassificationConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityClassificationConfigServiceRemote {


    @Autowired
    private ActivityClassificationConfigService activityClassificationConfigService;

    /**
     * 保存大类
     */
    public Result<Void> saveBigClass(RequestSaveActivityBigClass request) {
        Result<Void> result = activityClassificationConfigService.saveBigClassification(request);
        if (ResultUtils.isFailure(result)){
            log.warn("save activity big classification failed. request:{}, rCode:{}", request, result.rCode());
        }
        return result;
    }

    /**
     * 更新大类
     */
    public Result<Void> updateBigClass(RequestUpdateActivityBigClass request) {
        Result<Void> result = activityClassificationConfigService.updateBigClassification(request);
        if (ResultUtils.isFailure(result)){
            log.warn("update activity big classification failed. request:{}, rCode:{}", request, result.rCode());
        }
        return result;
    }

    /**
     * 删除大类
     */
    public Result<Void> deleteBigClass(Long id, Integer appId, String operator) {
        RequestDeleteActivityBigClass req = new RequestDeleteActivityBigClass();
        req.setId(id);
        req.setAppId(appId);
        req.setOperator(operator);
        Result<Void> result = activityClassificationConfigService.deleteBigClassification(req);
        if (ResultUtils.isFailure(result)){
            log.warn("delete activity big classification failed. id:{}, appId:{}, operator:{}, rCode:{}", id, appId, operator, result.rCode());
        }
        return result;
    }

    /**
     * 大类列表
     */
    public Result<List<ActivityBigClassBean>> getBigClassList(Integer appId) {
        Result<List<ActivityBigClassBean>> result = activityClassificationConfigService.listBigClassByAppId(appId);
        if (ResultUtils.isFailure(result)){
            log.warn("list activity big classification failed. appId:{}", appId);
        }
        return result;
    }

    /**
     * 保存分类
     */
    public Result<Void> saveClassification(RequestSaveActivityClassification request) {
        Result<Void> result = activityClassificationConfigService.saveClassification(request);
        if (ResultUtils.isFailure(result)){
            log.warn("save activity classification failed. request:{}, rCode:{}", request, result.rCode());
        }
        return result;
    }

    /**
     * 更新分类
     */
    public Result<Void> updateClassification(RequestUpdateActivityClassification request) {
        Result<Void> result = activityClassificationConfigService.updateClassification(request);
        if (ResultUtils.isFailure(result)){
            log.warn("update activity classification failed. request:{}, rCode:{}", request, result.rCode());
        }
        return result;
    }

    /**
     * 删除分类
     */
    public Result<Void> deleteClassification(Long id, Integer appId, String operator) {
        RequestDeleteActivityClassification req = new RequestDeleteActivityClassification();
        req.setId(id);
        req.setAppId(appId);
        req.setOperator(operator);
        Result<Void> result = activityClassificationConfigService.deleteClassification(req);
        if (ResultUtils.isFailure(result)){
            log.warn("delete activity classification failed. id:{}, appId:{}, operator:{}", id, appId, operator);
        }
        return result;
    }


    /**
     * 获取分类列表
     */
    public Result<List<ActivityClassConfigBean>> getClassificationList(Integer appId, Long bigClassId) {
        Result<List<ActivityClassConfigBean>> result = activityClassificationConfigService.listClassificationByBigClassId(bigClassId);
        if (ResultUtils.isFailure(result)){
            log.warn("get class list failed. appId:{}, bigClassId:{}", appId, bigClassId);
        }
        return result;
    }
}
