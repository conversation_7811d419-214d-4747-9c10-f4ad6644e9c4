package fm.lizhi.ocean.wave.management.model.param.award.singer;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 分页获取歌手装扮规则配置列表
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PageSingerDecorateRuleParam {

    @NotNull(message = "分页大小不能为空")
    @Min(value = 1, message = "分页大小不能小于1")
    private int pageSize;

    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于1")
    private int pageNo;
}
