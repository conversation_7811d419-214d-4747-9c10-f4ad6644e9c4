package fm.lizhi.ocean.wave.management.datastore.dao.family.offlinezone;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.management.datastore.mapper.family.offlinezone.OfflineZoneLevelRightExtMapper;
import fm.lizhi.ocean.wave.management.datastore.mapper.family.offlinezone.OfflineZoneLevelRightIntroductionExtMapper;
import fm.lizhi.ocean.wave.management.datastore.mapper.family.offlinezone.OfflineZoneLevelRightRelationExtMapper;
import fm.lizhi.ocean.wave.management.model.converter.family.offlinezone.LevelRightConvert;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.AddLevelRightParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.DeleteLevelRightParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.UpdateLevelRightParam;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRight;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRightIntroduction;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneLevelRightIntroductionMapper;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneLevelRightMapper;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 线下专区等级权益 Dao
 */
@Repository
@Slf4j
public class LevelRightDao {

    @Autowired
    private LevelRightConvert levelRightConvert;

    @Autowired
    private OfflineZoneLevelRightMapper offlineZoneLevelRightMapper;
    @Autowired
    private OfflineZoneLevelRightExtMapper offlineZoneLevelRightExtMapper;

    @Autowired
    private OfflineZoneLevelRightIntroductionMapper offlineZoneLevelRightIntroductionMapper;
    @Autowired
    private OfflineZoneLevelRightIntroductionExtMapper offlineZoneLevelRightIntroductionExtMapper;

    @Autowired
    private OfflineZoneLevelRightRelationExtMapper offlineZoneLevelRightRelationExtMapper;

    /**
     * 新增等级权益
     *
     * @param param 新增参数
     * @return 新增的等级权益ID
     */
    @Transactional
    public Long addLevelRight(AddLevelRightParam param) {
        // 插入等级权益
        OfflineZoneLevelRight createRightEntity = levelRightConvert.toCreateRightEntity(param);
        offlineZoneLevelRightMapper.insert(createRightEntity);
        log.info("insert levelRight, entity={}", createRightEntity);
        // 插入等级权益介绍列表
        Long rightId = createRightEntity.getId();
        List<OfflineZoneLevelRightIntroduction> createIntroductionEntities = levelRightConvert.toCreateIntroductionEntities(param, rightId);
        if (CollectionUtils.isNotEmpty(createIntroductionEntities)) {
            int insertIntroductionRows = offlineZoneLevelRightIntroductionMapper.batchInsert(createIntroductionEntities);
            log.info("insert levelRightIntroduction, rows={}, entities={}", insertIntroductionRows, createIntroductionEntities);
        } else {
            log.info("no need to insert levelRightIntroduction");
        }
        return rightId;
    }

    /**
     * 根据ID获取等级权益
     *
     * @param id 等级权益ID
     * @return 等级权益实体
     */
    public OfflineZoneLevelRight getLevelRight(long id) {
        OfflineZoneLevelRight getById = new OfflineZoneLevelRight();
        getById.setId(id);
        return offlineZoneLevelRightMapper.selectByPrimaryKey(getById);
    }

    /**
     * 根据ID列表获取等级权益列表
     *
     * @param ids 等级权益ID列表
     * @return 等级权益实体列表
     */
    public List<OfflineZoneLevelRight> getLevelRights(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return offlineZoneLevelRightExtMapper.selectByIds(ids);
    }

    /**
     * 根据ID列表获取等级权益映射, key为等级权益ID, value为等级权益实体
     *
     * @param ids 等级权益ID列表
     * @return ID到等级权益实体的映射
     */
    public Map<Long, OfflineZoneLevelRight> getLevelRightsMap(List<Long> ids) {
        List<OfflineZoneLevelRight> levelRights = getLevelRights(ids);
        Map<Long, OfflineZoneLevelRight> levelRightMap = new HashMap<>(levelRights.size());
        for (OfflineZoneLevelRight levelRight : levelRights) {
            levelRightMap.put(levelRight.getId(), levelRight);
        }
        log.debug("get levelRightsMap, ids={}, levelRightsMap={}", ids, levelRightMap);
        return levelRightMap;
    }

    /**
     * 根据ID获取等级权益介绍列表, 按index升序
     *
     * @param rightId 等级权益ID
     * @return 等级权益介绍列表
     */
    public List<OfflineZoneLevelRightIntroduction> getIntroductionsByRightId(long rightId) {
        OfflineZoneLevelRightIntroduction selectMany = new OfflineZoneLevelRightIntroduction();
        selectMany.setRightId(rightId);
        List<OfflineZoneLevelRightIntroduction> list = offlineZoneLevelRightIntroductionMapper.selectMany(selectMany);
        List<OfflineZoneLevelRightIntroduction> sortedList = new ArrayList<>(list);
        sortedList.sort(Comparator.comparing(OfflineZoneLevelRightIntroduction::getIndex));
        return sortedList;
    }

    private List<OfflineZoneLevelRightIntroduction> getIntroductionsByRightIds(List<Long> rightIds) {
        if (CollectionUtils.isEmpty(rightIds)) {
            return Collections.emptyList();
        }
        return offlineZoneLevelRightIntroductionExtMapper.selectByRightIds(rightIds);
    }

    /**
     * 更新等级权益
     *
     * @param param                     更新参数
     * @param shouldUpdateIntroductions 是否需要更新介绍列表, 在外层提前判断好传入
     */
    @Transactional
    public void updateLevelRight(UpdateLevelRightParam param, boolean shouldUpdateIntroductions) {
        // 更新等级权益
        OfflineZoneLevelRight updateRightEntity = levelRightConvert.toUpdateRightEntity(param);
        int updateRightRows = offlineZoneLevelRightMapper.updateByPrimaryKey(updateRightEntity);
        log.info("update levelRight, rows={}, entity={}", updateRightRows, updateRightEntity);
        // 更新关联的介绍列表
        if (shouldUpdateIntroductions) {
            // 更新介绍列表是低频操作, 直接先删除再批量插入
            Long rightId = param.getId();
            int deleteIntroductionRows = offlineZoneLevelRightIntroductionExtMapper.deleteByRightId(rightId);
            List<OfflineZoneLevelRightIntroduction> insertIntroductionEntities = levelRightConvert.toCreateIntroductionEntities(param, rightId);
            int insertIntroductionRows = offlineZoneLevelRightIntroductionMapper.batchInsert(insertIntroductionEntities);
            log.info("update levelRightIntroduction from rightId={}, deleteRows={}, insertRows={}, entities={}",
                    rightId, deleteIntroductionRows, insertIntroductionRows, insertIntroductionEntities);
        } else {
            log.info("no need to update levelRightIntroduction for rightId={}", param.getId());
        }
    }

    /**
     * 删除等级权益
     *
     * @param param 删除参数
     */
    @Transactional
    public void deleteLevelRight(DeleteLevelRightParam param) {
        Long id = param.getId();
        String operator = SessionUtils.getAccount();
        int deleteRightRows = offlineZoneLevelRightExtMapper.deleteById(id, operator);
        log.info("delete levelRight, rows={}, id={}, operator={}", deleteRightRows, id, operator);
        int deleteLevelRightRelationRows = offlineZoneLevelRightRelationExtMapper.deleteByRightId(id);
        log.info("delete levelRightRelation by rightId, rows={}, rightId={}", deleteLevelRightRelationRows, id);
    }

    /**
     * 列出所有等级权益, 不包含已删除的
     *
     * @return 等级权益列表
     */
    public List<OfflineZoneLevelRight> listLevelRights() {
        Integer appId = SessionExtUtils.getAppId();
        String deployEnv = ConfigUtils.getEnvRequired().name();
        List<OfflineZoneLevelRight> levelRights = offlineZoneLevelRightExtMapper.listLevelRights(appId, deployEnv);
        log.debug("list levelRights, appId={}, deployEnv={}, levelRights={}", appId, deployEnv, levelRights);
        return levelRights;
    }

    /**
     * 根据权益ID列表获取权益ID到权益介绍列表的映射, 其中权益介绍列表已经按index升序排列
     *
     * @param rightIds 权益ID列表
     * @return 权益ID到权益介绍列表的映射
     */
    public ListValuedMap<Long, OfflineZoneLevelRightIntroduction> getLevelRightIntroductionsMap(List<Long> rightIds) {
        ArrayListValuedHashMap<Long, OfflineZoneLevelRightIntroduction> levelRightIntroductionsMap = new ArrayListValuedHashMap<>();
        List<OfflineZoneLevelRightIntroduction> introductions = getIntroductionsByRightIds(rightIds);
        for (OfflineZoneLevelRightIntroduction introduction : introductions) {
            levelRightIntroductionsMap.put(introduction.getRightId(), introduction);
        }
        log.debug("get levelRightIntroductionsMap, rightIds={}, levelRightIntroductionsMap={}", rightIds, levelRightIntroductionsMap);
        return levelRightIntroductionsMap;
    }
}
