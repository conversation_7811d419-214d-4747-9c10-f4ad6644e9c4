package fm.lizhi.ocean.wave.management.model.converter.activitycenter.ai;

import fm.lizhi.ocean.godzilla.api.constant.ActivityPlanImageApiVersionEnum;
import fm.lizhi.ocean.godzilla.api.constant.ActivityPlanImageSceneEnum;
import fm.lizhi.ocean.godzilla.api.constant.ActivityPlanImageStyleEnum;
import fm.lizhi.ocean.godzilla.api.constant.AiUserTypeEnum;
import fm.lizhi.ocean.godzilla.api.model.common.AiActivityPlanImageParam;
import fm.lizhi.ocean.godzilla.api.model.common.AiOperator;
import fm.lizhi.ocean.godzilla.api.model.request.activityplan.*;
import fm.lizhi.ocean.godzilla.api.model.response.activityplan.*;
import fm.lizhi.ocean.wave.management.config.apollo.ActivityConfig;
import fm.lizhi.ocean.wave.management.config.apollo.CommonConfig;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.ai.*;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ai.*;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wave.management.util.UrlUtils;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateRoomBackground;
import fm.lizhi.sso.client.SessionUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
                ActivityPlanImageApiVersionEnum.class,
                ResultVO.class,
        }
)
public abstract class ActivityAiConvert {

    @Autowired
    protected CommonConfig commonConfig;

    public abstract List<GetAiImageStylesResult.ImageStyle> toAiImageStyles(List<ActivityConfig.Ai.ImageStyle> imageStyleConfigs);

    @Mapping(target = "imageStyles", source = "imageStyles")
    public abstract GetAiImageStylesResult toGetAiImageStylesResult(ActivityPlanImageStyleEnum[] imageStyles);

    @Mapping(target = "name", source = "msg")
    protected abstract GetAiImageStylesResult.ImageStyle toAiImageStyle(ActivityPlanImageStyleEnum imageStyleEnum);

    public abstract List<GetAiImageScenesResult.ImageScene> toAiImageScenes(List<ActivityConfig.Ai.ImageScene> imageSceneConfigs);

    @Mapping(target = "imageScenes", source = "imageScenes")
    public abstract GetAiImageScenesResult toGetAiImageScenesResult(ActivityPlanImageSceneEnum[] imageScenes);

    @Mapping(target = "name", source = "msg")
    protected abstract GetAiImageScenesResult.ImageScene toAiImageScene(ActivityPlanImageSceneEnum imageSceneEnum);

    @Named("getAppIdAsLong")
    protected Long getAppIdAsLong(Object ignore) {
        return SessionExtUtils.getAppId() != null ? SessionExtUtils.getAppId().longValue() : null;
    }

    @Named("getSsoAiOperator")
    protected AiOperator getSsoAiOperator(Object ignore) {
        AiOperator aiOperator = new AiOperator();
        aiOperator.setUserType(AiUserTypeEnum.SSO_USER.getValue());
        aiOperator.setUserName(SessionUtils.getAccount());
        return aiOperator;
    }

    @Named("toImageParams")
    protected List<AiActivityPlanImageParam> toImageParams(List<String> imageUrls) {
        if (CollectionUtils.isEmpty(imageUrls)) {
            return Collections.emptyList();
        }
        ArrayList<AiActivityPlanImageParam> imageParams = new ArrayList<>(imageUrls.size());
        for (String imageUrl : imageUrls) {
            imageParams.add(toImageParam(imageUrl));
        }
        return imageParams;
    }

    @Mapping(target = "imageUrl", source = "imageUrl", qualifiedByName = "replaceWithWaveCdnOuterHost")
    protected abstract AiActivityPlanImageParam toImageParam(String imageUrl);

    @Named("replaceWithWaveCdnOuterHost")
    protected String replaceWithWaveCdnOuterHost(String url) {
        // AI生图相关接口提交的图片URL都是默认为创作者平台CDN, 因此替换为外网CDN也是按创作者平台的CDN来替换
        String cdnOuterHost = commonConfig.getWaveCdn().getCdnOuterHost();
        return UrlUtils.replaceHostOrEmpty(url, cdnOuterHost);
    }

    @Mapping(target = "appId", source = "param", qualifiedByName = "getAppIdAsLong")
    @Mapping(target = "apiVersion", expression = "java(ActivityPlanImageApiVersionEnum.POLISH_PROMPT_V1.getValue())")
    @Mapping(target = "operator", source = "param", qualifiedByName = "getSsoAiOperator")
    public abstract RequestActivityPlanPolishTextToImagePrompt toRequestActivityPlanPolishTextToImagePrompt(PolishTextToImagePromptParam param);

    public abstract PolishTextToImagePromptResult toPolishTextToImagePromptResult(ResponseActivityPlanPolishTextToImagePrompt response);

    @Mapping(target = "appId", source = "param", qualifiedByName = "getAppIdAsLong")
    @Mapping(target = "apiVersion", expression = "java(ActivityPlanImageApiVersionEnum.TEXT_TO_IMAGE_V1.getValue())")
    @Mapping(target = "operator", source = "param", qualifiedByName = "getSsoAiOperator")
    @Mapping(target = "syncRequest", constant = "true")
    public abstract RequestActivityPlanSubmitTextToImageTask toRequestActivityPlanSubmitTextToImageTask(TextToImageParam param);

    public abstract TextToImageResult toTextToImageResult(ResponseActivityPlanSubmitTextToImageTask response);

    @Mapping(target = "appId", source = "param", qualifiedByName = "getAppIdAsLong")
    @Mapping(target = "apiVersion", expression = "java(ActivityPlanImageApiVersionEnum.ERASURE_IMAGE_V1.getValue())")
    @Mapping(target = "operator", source = "param", qualifiedByName = "getSsoAiOperator")
    @Mapping(target = "syncRequest", constant = "true")
    @Mapping(target = "originalImageUrl", source = "param.originalImageUrl", qualifiedByName = "replaceWithWaveCdnOuterHost")
    @Mapping(target = "maskImageUrl", source = "param.maskImageUrl", qualifiedByName = "replaceWithWaveCdnOuterHost")
    public abstract RequestActivityPlanSubmitErasureImageTask toRequestActivityPlanSubmitErasureImageTask(ErasureImageParam param);

    @Mapping(target = "RCode", expression = "java(ResultVO.FAILURE)")
    @Mapping(target = "msg", source = "response.resultImage.errorMsg")
    @Mapping(target = "data", ignore = true)
    public abstract ResultVO<ErasureImageResult> toFailedErasureImageResultVO(ResponseActivityPlanSubmitErasureImageTask response);

    @Mapping(target = "imageId", source = "response.resultImage.imageId")
    @Mapping(target = "imageUrl", source = "response.resultImage.imageUrl")
    @Mapping(target = "imagePath", source = "response.resultImage.imagePath")
    @Mapping(target = "success", source = "response.resultImage.success")
    @Mapping(target = "errorMsg", source = "response.resultImage.errorMsg")
    public abstract ErasureImageResult toSuccessErasureImageResult(ResponseActivityPlanSubmitErasureImageTask response);

    @Mapping(target = "appId", source = "param", qualifiedByName = "getAppIdAsLong")
    @Mapping(target = "apiVersion", expression = "java(ActivityPlanImageApiVersionEnum.EXPAND_IMAGE_V1.getValue())")
    @Mapping(target = "operator", source = "param", qualifiedByName = "getSsoAiOperator")
    @Mapping(target = "syncRequest", constant = "true")
    @Mapping(target = "originalImageUrl", source = "param.originalImageUrl", qualifiedByName = "replaceWithWaveCdnOuterHost")
    @Mapping(target = "maskImageUrl", source = "param.maskImageUrl", qualifiedByName = "replaceWithWaveCdnOuterHost")
    public abstract RequestActivityPlanSubmitExpandImageTask toRequestActivityPlanSubmitExpandImageTask(ExpandImageParam param);

    @Mapping(target = "RCode", expression = "java(ResultVO.FAILURE)")
    @Mapping(target = "msg", source = "response.resultImage.errorMsg")
    @Mapping(target = "data", ignore = true)
    public abstract ResultVO<ExpandImageResult> toFailedExpandImageResultVO(ResponseActivityPlanSubmitExpandImageTask response);

    @Mapping(target = "imageId", source = "response.resultImage.imageId")
    @Mapping(target = "imageUrl", source = "response.resultImage.imageUrl")
    @Mapping(target = "imagePath", source = "response.resultImage.imagePath")
    @Mapping(target = "success", source = "response.resultImage.success")
    @Mapping(target = "errorMsg", source = "response.resultImage.errorMsg")
    public abstract ExpandImageResult toSuccessExpandImageResult(ResponseActivityPlanSubmitExpandImageTask response);

    @Mapping(target = "appId", source = "param", qualifiedByName = "getAppIdAsLong")
    @Mapping(target = "apiVersion", expression = "java(ActivityPlanImageApiVersionEnum.REMOVE_IMAGE_BG_V1.getValue())")
    @Mapping(target = "operator", source = "param", qualifiedByName = "getSsoAiOperator")
    @Mapping(target = "syncRequest", constant = "true")
    @Mapping(target = "imageList", source = "param.imageUrls", qualifiedByName = "toImageParams")
    public abstract RequestActivityPlanSubmitRemoveImageBackgroundTask toRequestActivityPlanSubmitRemoveImageBackgroundTask(RemoveImageBackgroundParam param);

    public abstract RemoveImageBackgroundResult toRemoveImageBackgroundResult(ResponseActivityPlanSubmitRemoveImageBackgroundTask response);

    public abstract RequestCreateRoomBackground toRequestCreateRoomBackground(Integer appId, SaveBusinessImageParam.RoomBackground roomBackground);

    public abstract SaveBusinessImageResult toSaveBusinessImageResult(ResponseCreateRoomBackground response);

    public abstract RequestCreateAvatarWidget toRequestCreateAvatarWidget(Integer appId, SaveBusinessImageParam.AvatarWidget avatarWidget);

    public abstract SaveBusinessImageResult toSaveBusinessImageResult(ResponseCreateAvatarWidget response);
}
