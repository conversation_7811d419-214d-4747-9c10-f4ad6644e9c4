package fm.lizhi.ocean.wave.management.manager.common;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.romefs.javasdk.config.RomeFsConfig;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.common.annotation.TransferWaveCdnToBusinessCdn;
import fm.lizhi.ocean.wave.management.config.apollo.BusinessConfig;
import fm.lizhi.ocean.wave.management.config.apollo.BusinessProperties;
import fm.lizhi.ocean.wave.management.config.apollo.CommonConfig;
import fm.lizhi.ocean.wave.management.model.param.common.RomeFsPutParam;
import fm.lizhi.ocean.wave.management.model.result.common.RomeFsPutResult;
import fm.lizhi.ocean.wave.management.model.result.common.TransferCdnResult;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.UrlUtils;
import fm.lizhi.ocean.wave.platform.api.common.service.CommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequest;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 公共cdn管理器
 */
@Slf4j
@Component
public class CommonCdnManager {

    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");

    @Autowired
    private BusinessConfig businessConfig;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private RomeFsManager romeFsManager;

    @Autowired
    @Qualifier("restTemplate")
    private RestTemplate restTemplate;

    /**
     * 根据应用id添加cdn域名, 如果原path为空则返回空
     *
     * @param path  路径
     * @param appId 应用id
     * @return 添加cdn域名后的路径
     */
    public String addHostOrEmpty(String path, int appId) {
        BusinessProperties businessProperties = businessConfig.getPropertiesByAppId(appId);
        return UrlUtils.addHostOrEmpty(path, businessProperties.getCdnHost());
    }

    /**
     * 根据应用id添加cdn内部域名, 如果原path为空则返回空
     *
     * @param path  路径
     * @param appId 应用id
     * @return 添加cdn内部域名后的路径
     */
    public String addInnerHostOrEmpty(String path, int appId) {
        BusinessProperties businessProperties = businessConfig.getPropertiesByAppId(appId);
        return UrlUtils.addHostOrEmpty(path, businessProperties.getCdnInnerHost());
    }

    /**
     * 将创作者CDN的文件转存到到业务CDN, 包含{@link TransferWaveCdnToBusinessCdn}注解的bean字段将被转存.
     * 该方法会返回一个克隆的bean, 并且将转存后的url替换到bean中. 只要有一个字段转存失败, 就会返回失败.
     *
     * @param bean  对象, 必须是可克隆的对象
     * @param appId 应用id
     * @return 包含转存后url的克隆的bean
     */
    public <T> Result<T> transferWaveToBusinessByBean(T bean, int appId) {
        T clonedBean;
        try {
            @SuppressWarnings("unchecked")
            T newInstance = (T) BeanUtils.instantiateClass(bean.getClass());
            BeanUtils.copyProperties(bean, newInstance);
            clonedBean = newInstance;
        } catch (RuntimeException e) {
            log.error("transferWaveToBusinessByBean clone bean error, bean={}, appId={}", bean, appId, e);
            return ResultUtils.failure(CommonService.PARAM_ERROR, "克隆bean失败");
        }
        HashSet<String> originalUrls = new HashSet<>();
        // 通过Introspector获取需要转存的字段值
        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(bean.getClass());
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
                Method readMethod = propertyDescriptor.getReadMethod();
                Method writeMethod = propertyDescriptor.getWriteMethod();
                if (readMethod != null && Objects.equals(readMethod.getReturnType(), String.class) && writeMethod != null) {
                    Field field = readMethod.getDeclaringClass().getDeclaredField(propertyDescriptor.getName());
                    if (field.isAnnotationPresent(TransferWaveCdnToBusinessCdn.class)) {
                        String value = (String) readMethod.invoke(bean);
                        if (StringUtils.isNotBlank(value)) {
                            originalUrls.add(value);
                        }
                    }
                }
            }
        } catch (IntrospectionException | NoSuchFieldException | IllegalAccessException | InvocationTargetException |
                 RuntimeException e) {
            log.error("transferWaveToBusinessByBean get originalUrls error, bean={}, appId={}", bean, appId, e);
            return ResultUtils.failure(CommonService.INTERNAL_ERROR, "访问待转存CDN的字段失败");
        }
        log.info("transferWaveToBusinessByBean originalUrls={}, appId={}", originalUrls, appId);
        if (originalUrls.isEmpty()) {
            return ResultUtils.success(clonedBean);
        }
        // key: 转存前的url, value: 转存后的url
        HashMap<String, String> transferMap = new HashMap<>();
        for (String originalUrl : originalUrls) {
            Result<TransferCdnResult> transferResult = transferWaveToBusinessByUrl(originalUrl, appId);
            if (ResultUtils.isFailure(transferResult)) {
                return ResultUtils.failure(transferResult);
            }
            TransferCdnResult transferCdnResult = transferResult.target();
            String transferredUrl = transferCdnResult.getUrl();
            transferMap.put(originalUrl, transferredUrl);
        }
        log.info("transferWaveToBusinessByBean transferMap={}, appId={}", transferMap, appId);
        // 通过Introspector将转存后的url设到克隆的bean中
        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(bean.getClass());
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
                Method readMethod = propertyDescriptor.getReadMethod();
                Method writeMethod = propertyDescriptor.getWriteMethod();
                if (readMethod != null && Objects.equals(readMethod.getReturnType(), String.class) && writeMethod != null) {
                    Field field = readMethod.getDeclaringClass().getDeclaredField(propertyDescriptor.getName());
                    if (field.isAnnotationPresent(TransferWaveCdnToBusinessCdn.class)) {
                        String originalValue = (String) readMethod.invoke(bean);
                        String transferredValue = transferMap.get(originalValue);
                        if (StringUtils.isNotBlank(transferredValue)) {
                            writeMethod.invoke(clonedBean, transferredValue);
                        }
                    }
                }
            }
            log.info("transferWaveToBusinessByBean set transferred url success, bean={}, appId={}, clonedBean={}", bean, appId, clonedBean);
            return ResultUtils.success(clonedBean);
        } catch (IntrospectionException | NoSuchFieldException | InvocationTargetException | IllegalAccessException |
                 RuntimeException e) {
            log.error("transferWaveToBusinessByBean error, clonedBean={}, appId={}, transferMap={}", clonedBean, appId, transferMap, e);
            return ResultUtils.failure(CommonService.INTERNAL_ERROR, "设置转存CDN的字段失败");
        }
    }

    /**
     * 将创作者CDN的文件转存到到业务CDN
     *
     * @param url   原始文件地址
     * @param appId 应用id
     * @return 转存结果
     */
    public Result<TransferCdnResult> transferWaveToBusinessByUrl(String url, int appId) {
        if (StringUtils.isBlank(url)) {
            return ResultUtils.failure(CommonService.PARAM_ERROR, "文件地址不能为空");
        }
        Path localPath;
        try {
            String cdnInnerHost = commonConfig.getWaveCdn().getCdnInnerHost();
            String innerUrl = UrlUtils.replaceHostOrEmpty(url, cdnInnerHost);
            Result<Path> downloadResult = restTemplate.execute(innerUrl, HttpMethod.GET, new SetAcceptRequestCallback(), new DownloadResponseExtractor());
            if (downloadResult == null) {
                return ResultUtils.failure(CommonService.INTERNAL_ERROR, "下载结果为空, url=" + url);
            }
            if (ResultUtils.isFailure(downloadResult)) {
                return ResultUtils.failure(downloadResult);
            }
            localPath = downloadResult.target();
        } catch (RuntimeException e) {
            log.error("Download from wave inner CDN error, url={}", url, e);
            return ResultUtils.failure(CommonService.PARAM_ERROR, "下载文件失败, url=" + url);
        }
        try {
            String dateTime = LocalDateTime.now().format(dateTimeFormatter);
            String uuid = UUID.randomUUID().toString().replace("-", "");
            String extension = FilenameUtils.getExtension(URI.create(url).getPath()).toLowerCase();
            String suffix = StringUtils.isNotBlank(extension) ? "." + extension : StringUtils.EMPTY;
            String fileName = String.format("/manager/%s/%s%s", dateTime, uuid, suffix);
            BusinessProperties businessProperties = businessConfig.getPropertiesByAppId(appId);
            RomeFsConfig romeFsConfig = RomeFsConfig.builder()
                    .appId(appId)
                    .hostApp(StringUtils.EMPTY)
                    .deviceId(ConfigUtils.getServiceName())
                    .address(businessProperties.getRomeInnerAddress())
                    .customFileName(true)
                    .build();
            RomeFsPutParam romeFsPutParam = new RomeFsPutParam();
            romeFsPutParam.setRomeFsConfig(romeFsConfig);
            romeFsPutParam.setAccessModifier(RomeFsPutParam.ACCESS_MODIFIER_PUBLIC);
            romeFsPutParam.setLocalPath(localPath);
            romeFsPutParam.setFileName(fileName);
            Result<RomeFsPutResult> putObjectResult = romeFsManager.putObject(romeFsPutParam);
            if (ResultUtils.isFailure(putObjectResult)) {
                return ResultUtils.failure(CommonService.INTERNAL_ERROR, "转存文件失败, url=" + url);
            }
            String filePath = putObjectResult.target().getFilePath();
            TransferCdnResult transferCdnResult = new TransferCdnResult();
            transferCdnResult.setPath(filePath);
            transferCdnResult.setUrl(UrlUtils.addHostOrEmpty(filePath, businessProperties.getCdnHost()));
            transferCdnResult.setInnerUrl(UrlUtils.addHostOrEmpty(filePath, businessProperties.getCdnInnerHost()));
            transferCdnResult.setOuterUrl(UrlUtils.addHostOrEmpty(filePath, businessProperties.getCdnOuterHost()));
            return ResultUtils.success(transferCdnResult);
        } catch (RestClientResponseException e) {
            int rawStatusCode = e.getRawStatusCode();
            if (rawStatusCode == HttpStatus.NOT_FOUND.value()) {
                log.info("Transfer wave CDN file not found, url={}, appId={}", url, appId);
                log.trace("Transfer wave CDN file not found, url={}, appId={}", url, appId, e);
                return ResultUtils.failure(CommonService.PARAM_ERROR, "文件不存在, url=" + url);
            } else {
                log.error("Transfer wave CDN file to business CDN error, url={}, appId={}, statusCode={}", url, appId, rawStatusCode);
                return ResultUtils.failure(CommonService.INTERNAL_ERROR, "转存文件失败, url=" + url);
            }
        } catch (RuntimeException e) {
            log.error("Transfer wave CDN file to business CDN error, url={}, appId={}", url, appId, e);
            return ResultUtils.failure(CommonService.INTERNAL_ERROR, "转存文件失败, url=" + url);
        }
    }

    private static class SetAcceptRequestCallback implements RequestCallback {

        @Override
        public void doWithRequest(ClientHttpRequest request) throws IOException {
            request.getHeaders().setAccept(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM, MediaType.ALL));
        }
    }

    private static class DownloadResponseExtractor implements ResponseExtractor<Result<Path>> {

        @Override
        public Result<Path> extractData(@NotNull ClientHttpResponse response) throws IOException {
            Path localPath;
            try {
                localPath = Files.createTempFile(StringUtils.EMPTY, StringUtils.EMPTY);
            } catch (IOException | RuntimeException e) {
                log.error("Failed to create temp file", e);
                return ResultUtils.failure(CommonService.INTERNAL_ERROR, "生成临时文件失败");
            }
            try {
                Files.copy(response.getBody(), localPath, StandardCopyOption.REPLACE_EXISTING);
            } catch (IOException | RuntimeException e) {
                log.error("Failed to copy file", e);
                return ResultUtils.failure(CommonService.INTERNAL_ERROR, "文件下载失败");
            }
            return ResultUtils.success(localPath);
        }
    }
}
