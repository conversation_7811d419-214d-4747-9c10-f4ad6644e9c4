package fm.lizhi.ocean.wave.management.model.converter.award.family;

import fm.lizhi.ocean.wave.management.model.param.award.family.ListFamilySpecialRecommendCardParam;
import fm.lizhi.ocean.wave.management.model.param.award.family.UploadFamilySpecialRecommendCardParam;
import fm.lizhi.ocean.wave.management.model.vo.award.family.ListFamilySpecialRecommendCardNameVO;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.ListFamilySpecialRecommendCardNameBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestClearFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestListFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestUploadFamilySpecialRecommendCardName;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface FamilyOtherAwardRuleConvert {

    FamilyOtherAwardRuleConvert I = Mappers.getMapper(FamilyOtherAwardRuleConvert.class);

    RequestUploadFamilySpecialRecommendCardName toRequestUploadFamilySpecialRecommendCardName(
            UploadFamilySpecialRecommendCardParam param, Integer appId, String operator);

    RequestClearFamilySpecialRecommendCardName toRequestClearFamilySpecialRecommendCardName(Integer appId, String operator);

    RequestListFamilySpecialRecommendCardName toRequestListFamilySpecialRecommendCardName(
            ListFamilySpecialRecommendCardParam param, Integer appId);

    List<ListFamilySpecialRecommendCardNameVO> toListFamilySpecialRecommendCardNameVOS(List<ListFamilySpecialRecommendCardNameBean> beans);
}
