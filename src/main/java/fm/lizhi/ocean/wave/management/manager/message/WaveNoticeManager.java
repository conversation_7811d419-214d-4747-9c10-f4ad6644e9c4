package fm.lizhi.ocean.wave.management.manager.message;

import fm.lizhi.ocean.wave.management.model.param.wave.notice.SaveWaveNoticeParam;
import fm.lizhi.ocean.wave.management.model.param.wave.notice.EnableOrDisableNoticeParam;
import fm.lizhi.ocean.wave.platform.api.platform.bean.WaveAnnouncementBean;
import fm.lizhi.ocean.wave.platform.api.platform.request.RequestSaveOrUpdateAnnouncement;
import fm.lizhi.ocean.wave.platform.api.platform.request.RequestEnableAnnouncement;
import fm.lizhi.commons.service.client.pojo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.remote.service.message.WaveNoticeRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.model.result.wave.notice.WaveNoticeResult;
import fm.lizhi.ocean.wave.platform.api.platform.response.ResponseAnnouncementList;
import fm.lizhi.ocean.wave.management.model.converter.wave.notice.WaveNoticeResultMapper;

import java.util.Collections;
import java.util.List;

@Slf4j
@Component
public class WaveNoticeManager {

    @Autowired
    private WaveNoticeRemote waveNoticeRemote;

    /**
     * 保存或更新PC公告
     */
    public ResultVO<Void> saveOrUpdateWaveNotice(SaveWaveNoticeParam param, Integer appId) {
        RequestSaveOrUpdateAnnouncement req = RequestSaveOrUpdateAnnouncement.builder()
                .title(param.getTitle())
                .content(param.getContent())
                .id(param.getId())
                .appIds(Collections.singletonList(appId))
                .build();
        Result<WaveAnnouncementBean> result = waveNoticeRemote.saveOrUpdateWaveAnnouncement(req);
        if (ResultUtils.isFailure(result)) {
            log.warn("saveOrUpdateWaveNotice failed, param={}, appId={}, rCode={}, msg={}", param, appId, result.rCode(), result.getMessage());
            return ResultVO.failure(result.rCode(), result.getMessage());
        }
        log.info("saveOrUpdateWaveNotice success, param={}, appId={}", param, appId);
        return ResultVO.success();
    }

    /**
     * 启用或禁用PC公告
     */
    public ResultVO<Void> enableOrDisableNotice(EnableOrDisableNoticeParam param, Integer appId) {
        RequestEnableAnnouncement req = RequestEnableAnnouncement.builder()
                .id(param.getId())
                .appIds(Collections.singletonList(appId))
                .enable(param.getEnable())
                .build();
        Result<Boolean> result;
        if (Boolean.TRUE.equals(param.getEnable())) {
            result = waveNoticeRemote.enableAnnouncement(req);
        } else {
            result = waveNoticeRemote.disableAnnouncement(req);
        }
        if (ResultUtils.isFailure(result) || !Boolean.TRUE.equals(result.target())) {
            log.warn("enableOrDisableNotice failed, param={}, appId={}, rCode={}, msg={}", param, appId, result.rCode(), result.getMessage());
            return ResultVO.failure(result.rCode(), result.getMessage());
        }
        log.info("enableOrDisableNotice success, param={}, appId={}", param, appId);
        return ResultVO.success();
    }

    public ResultVO<PageVO<WaveNoticeResult>> getWaveNoticeList(Integer pageSize, Integer pageNo, Boolean enable, Integer appId) {
        Result<ResponseAnnouncementList> result = waveNoticeRemote.getWaveAnnouncementRelation(enable, pageSize, pageNo);
        if (ResultUtils.isFailure(result)) {
            log.warn("getWaveNoticeList failed, pageSize={}, pageNum={}, enable={}, appId={}, rCode={}, msg={}", pageSize, pageNo, enable, appId, result.rCode(), result.getMessage());
            return ResultVO.failure(result.rCode(), result.getMessage());
        }
        ResponseAnnouncementList data = result.target();
        List<WaveNoticeResult> voList = WaveNoticeResultMapper.INSTANCE.toResultList(data.getList());
        return ResultVO.success(PageVO.of(data.getTotal(), voList));
    }

    /**
     * 获取最新的PC公告
     *
     * @param appId appId
     * @return 结果
     */
    public ResultVO<WaveNoticeResult> getLatestWaveNotice(Integer appId) {
        Result<WaveAnnouncementBean> result = waveNoticeRemote.getLatestWaveNotice(appId);
        if (ResultUtils.isFailure(result)) {
            log.warn("getLatestWaveNotice failed, appId={}, rCode={}, msg={}", appId, result.rCode(), result.getMessage());
            return ResultVO.failure(result.rCode(), result.getMessage());
        }

        WaveNoticeResult waveNoticeResult = WaveNoticeResultMapper.INSTANCE.beanToResult(result.target());
        return ResultVO.success(waveNoticeResult);
    }
} 