package fm.lizhi.ocean.wave.management.controller.grow.capability;

import fm.lizhi.ocean.wave.management.model.param.grow.capability.SaveCapabilityParam;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.grow.capability.CapabilityManageVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import fm.lizhi.ocean.wave.management.manager.grow.capability.CapabilityManager;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/30 17:37
 */
@RestController
@RequestMapping("/grow/capability")
public class GrowCapabilityController {

    @Autowired
    private CapabilityManager capabilityManager;

    /**
     * 保存能力项
     *
     * @return 结果
     */
    @PostMapping("saveCapability")
    public ResultVO<Void> saveCapability(@Validated @RequestBody SaveCapabilityParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        return capabilityManager.saveCapability(param, appId, operator);
    }

    /**
     * 列表
     *
     * @return 结果
     */
    @GetMapping("manageList")
    public ResultVO<List<CapabilityManageVO>> manageList() {
        Integer appId = SessionExtUtils.getAppId();
        return capabilityManager.manageList(appId);
    }


}
