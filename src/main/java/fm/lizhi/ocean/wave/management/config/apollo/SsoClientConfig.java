package fm.lizhi.ocean.wave.management.config.apollo;

import com.lizhi.commons.config.core.util.ConfigUtils;
import lombok.Data;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * sso客户端配置, 在application namespace中配置
 */
@ConfigurationProperties(prefix = "sso.client")
@Data
public class SsoClientConfig {

    /**
     * SSO服务名, 默认为当前服务名
     */
    private String serviceName = ConfigUtils.getServiceName();

    /**
     * 不需要登录的URL, Ant Pattern风格. 如果不需要登录, 则也不会进行权限检查
     */
    private String[] excludeLoginUrlPattern = ArrayUtils.EMPTY_STRING_ARRAY;

    /**
     * 不需要权限的URL, Ant Pattern风格. 先进行登录检查, 再进行权限检查
     */
    private String[] excludeUrlPattern = ArrayUtils.EMPTY_STRING_ARRAY;

    /**
     * 退出登录URL
     */
    private String logoutPath = "/system/logout";

    /**
     * 退出登录后跳转的URL
     */
    private String logoutBackPath = "/system/logoutBack";

    /**
     * 是否允许操作审计
     */
    private boolean enableOpAudit = true;

    /**
     * 是否所有环境都进行检查权限
     */
    private boolean alwaysCheckPermission = true;

    /**
     * Cookie的同源策略
     */
    private String sameSite = "None";

    /**
     * 登录回跳URL的scheme
     */
    private String backUrlScheme;

    /**
     * 调用SSO RPC的key
     */
    private String rpcKey = "90dc0e10a425c5f06a7e606b74d0b5bd";

    /**
     * 调用SSO RPC的类型
     */
    private String rpcType = "web_common_permissions";
}
