package fm.lizhi.ocean.wave.management.controller;

import fm.lizhi.ocean.wave.management.manager.ActivityDecorateManager;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.BatchGetDecorateParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.PageDecorateParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.DressUpResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 装扮
 * <AUTHOR>
 */
@RestController
@RequestMapping("/activity/dressUp")
@Slf4j
public class ActivityDecorateController {

    @Autowired
    private ActivityDecorateManager activityDecorateManager;


    /**
     * 获取装扮列表
     */
    @GetMapping("/list")
    public ResultVO<PageVO<DressUpResult>> list(PageDecorateParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("get activity dress up list,param={}, appId={}, operator={}", param, appId, operator);

        return activityDecorateManager.list(param, appId);
    }

    /**
     * 批量获取装扮
     */
    @GetMapping("/batchGetDressUp")
    public ResultVO<List<DressUpResult>> batchGetDressUp(BatchGetDecorateParam param){
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("batch get activity dress up list,param={}, appId={}, operator={}", param, appId, operator);
        return activityDecorateManager.batchGetDressUp(param, appId);
    }

}
