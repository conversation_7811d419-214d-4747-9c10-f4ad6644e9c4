package fm.lizhi.ocean.wave.management.controller.activitycenter.gift;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.gift.ActivityGiftConvert;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.gift.GetGiftsParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.gift.ListGiftParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.gift.GetGiftsResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.gift.ListGiftResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.JsonUtils;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.gift.bean.GetGiftsBean;
import fm.lizhi.ocean.wavecenter.api.gift.bean.ListGiftBean;
import fm.lizhi.ocean.wavecenter.api.gift.request.RequestGetGifts;
import fm.lizhi.ocean.wavecenter.api.gift.request.RequestListGift;
import fm.lizhi.ocean.wavecenter.api.gift.service.GiftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/activity/gift")
@Slf4j
public class ActivityGiftController {

    @Autowired
    private ActivityGiftConvert activityGiftConvert;

    @Autowired
    private GiftService giftService;

    /**
     * 分页查询礼物
     *
     * @param param 请求参数
     * @return 礼物列表
     */
    @GetMapping("/list")
    public ResultVO<PageVO<ListGiftResult>> listGift(@Validated ListGiftParam param) {
        Integer appId = SessionExtUtils.getAppId();
        RequestListGift request = activityGiftConvert.toRequestListGift(param, appId);
        Result<PageBean<ListGiftBean>> result = giftService.listGift(request);
        if (ResultUtils.isFailure(result)) {
            int rCode = result.rCode();
            String message = StringUtils.defaultIfBlank(result.getMessage(), "查询礼物失败");
            log.info("listGift fail, param: {}, result: {}", param, result);
            return ResultVO.failure(rCode, message);
        }
        PageBean<ListGiftBean> pageBean = result.target();
        if (log.isDebugEnabled()) {
            log.debug("listGift success, param: {}, pageBean: {}", param, JsonUtils.toJsonString(pageBean));
        }
        PageVO<ListGiftResult> pageVO = activityGiftConvert.toListGiftResults(pageBean);
        return ResultVO.success(pageVO);
    }

    /**
     * 批量查询礼物
     *
     * @param param 请求参数
     * @return 礼物列表
     */
    @GetMapping("/getGifts")
    public ResultVO<List<GetGiftsResult>> getGifts(@Validated GetGiftsParam param) {
        Integer appId = SessionExtUtils.getAppId();
        RequestGetGifts request = activityGiftConvert.toRequestGetGifts(param, appId);
        Result<List<GetGiftsBean>> result = giftService.getGifts(request);
        if (ResultUtils.isFailure(result)) {
            int rCode = result.rCode();
            String message = StringUtils.defaultIfBlank(result.getMessage(), "查询礼物失败");
            log.info("getGifts fail, param: {}, result: {}", param, result);
            return ResultVO.failure(rCode, message);
        }
        List<GetGiftsBean> beans = result.target();
        if (log.isDebugEnabled()) {
            log.debug("getGifts success, param: {}, beans: {}", param, JsonUtils.toJsonString(beans));
        }
        List<GetGiftsResult> results = activityGiftConvert.toGetGiftsResults(beans);
        return ResultVO.success(results);
    }
}
