package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * @description: 用户提报活动简单信息
 * @author: guoyibin
 * @create: 2024/10/23 17:24
 */
@Data
public class UserActivitySimpleInfoResult {

    /**
     * 活动ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动报名类型
     */
    private Integer applyType;

    /**
     * 活动审核状态
     */
    private Integer auditStatus;

    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;


    /**
     * 活动分类名称
     */
    private String className;

    /**
     * 大分类名称
     */
    private String bigClassName;

    /**
     * 分类ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    /**
     * 活动等级
     */
    private String levelName;

    /**
     * 提报人ID
     */
    private String applyUserId;

    /**
     * 提报人波段号
     */
    private String applyUserBand;

    /**
     * 提报人名称
     */
    private String applyUserName;

    /**
     * 提报家族ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long applyFamilyId;

    /**
     * 提报家族名称
     */
    private String applyFamilyName;

    /**
     * 提报厅厅主波段号
     */
    private String applyHallHostBand;

    /**
     * 提报厅厅主ID
     */
    private String applyHallHostId;

    /**
     * 提报厅厅主名称
     */
    private String applyHallHost;

    /**
     * 提报时间
     */
    private Long createTime;

    /**
     * 审核操作人
     */
    private String auditOperator;

    /**
     * 版本号
     */
    private Integer version;
    /**
     * 活动状态
     *
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityStatusEnum
     */
    private Integer activityStatus;
}
