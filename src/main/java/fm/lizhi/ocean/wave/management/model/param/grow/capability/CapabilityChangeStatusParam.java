package fm.lizhi.ocean.wave.management.model.param.grow.capability;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/5/30 17:58
 */
@Data
public class CapabilityChangeStatusParam {

    @NotNull(message = "id不可为空")
    private Long id;

    /**
     * 状态 0=停用  1=启用 2=下周
     */
    @NotBlank(message = "状态不可为空")
    private Integer status;

}
