package fm.lizhi.ocean.wave.management.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 分页结果
 *
 * @param <T> 数据类型
 */
@Data
public class PageVO<T> {

    /**
     * 结果总数
     */
    private int total;

    /**
     * 数据列表
     */
    private List<T> list;

    /**
     * 创建一个分页结果
     *
     * @param total 结果总数
     * @param list  数据列表
     * @param <T>   数据类型
     * @return 分页结果
     */
    public static <T> PageVO<T> of(int total, List<T> list) {
        PageVO<T> pageVO = new PageVO<>();
        pageVO.setTotal(total);
        pageVO.setList(list);
        return pageVO;
    }
}
