package fm.lizhi.ocean.wave.management.model.converter.permission;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.management.model.result.permission.PermissionListResult;
import fm.lizhi.ocean.wave.management.model.result.permission.PlatformRoleListResult;
import fm.lizhi.ocean.wave.management.model.vo.permission.*;
import fm.lizhi.ocean.wave.platform.api.permission.bean.*;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/24 15:40
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PermissionConvert {

    PermissionConvert I = Mappers.getMapper(PermissionConvert.class);

    PlatformRoleListResult platformRoleListBean2VO(PlatformRoleListBean bean);

    List<PlatformRoleListResult> platformRoleListBeans2VOs(List<PlatformRoleListBean> beans);

    PermissionVO permissionBean2VO(PermissionBean bean);

    List<PermissionVO> permissionBeans2VOs(List<PermissionBean> beans);

    PermissionListResult permissionListBean2PermissionListResult(PermissionListBean bean);

    List<PermissionListResult> permissionListBeans2PermissionListResults(List<PermissionListBean> beans);

    PlatformRoleVO platformRoleBean2VO(PlatformRoleBean bean);

    List<PlatformRoleVO> platformRoleBeans2VOs(List<PlatformRoleBean> beans);

    StatementVO statementBean2VO(StatementBean bean);

    List<StatementVO> statementBeans2VOs(List<StatementBean> beans);

    StatementResourceVO statementResourceBean2VO(StatementResourceBean bean);

    List<StatementResourceVO> statementResourceBeans2VOs(List<StatementResourceBean> beans);

    StatementResourceItemVO statementResourceItemBean2VO(StatementResourceItemBean bean);

    List<StatementResourceItemVO> statementResourceItemBeans2VOs(List<StatementResourceItemBean> beans);

}
