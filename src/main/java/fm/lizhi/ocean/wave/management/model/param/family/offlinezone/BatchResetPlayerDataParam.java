package fm.lizhi.ocean.wave.management.model.param.family.offlinezone;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BatchResetPlayerDataParam {

    private List<NeedResetPlayer> playerList;


    @Data
    @Accessors(chain = true)
    public static class NeedResetPlayer {

        /**
         * 家族 ID
         */
        private Long familyId;

        /**
         * 厅 ID
         */
        private Long njId;

        /**
         * 主播 ID
         */
        private Long playerId;

    }
}
