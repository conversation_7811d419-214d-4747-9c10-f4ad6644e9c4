package fm.lizhi.ocean.wave.management.model.converter.activitycenter;

import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityToolParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityToolParam;
import fm.lizhi.ocean.wave.management.model.vo.ActivityToolsInfoVo;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityToolsInfoBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityTools;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityTools;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityToolsConfigConvert {

    ActivityToolsConfigConvert I = Mappers.getMapper(ActivityToolsConfigConvert.class);


    RequestSaveActivityTools buildRequestSaveActivityTools(SaveActivityToolParam param, Integer appId, String operator);

    RequestUpdateActivityTools buildRequestUpdateActivityTools(UpdateActivityToolParam param, Integer appId, String operator);


    List<ActivityToolsInfoVo> convertActivityToolsInfoBeanList(List<ActivityToolsInfoBean> target);
}
