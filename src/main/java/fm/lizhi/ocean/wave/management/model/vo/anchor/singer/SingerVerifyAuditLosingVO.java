package fm.lizhi.ocean.wave.management.model.vo.anchor.singer;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SingerVerifyAuditLosingVO {

    /**
     * 失败的记录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 失败原因
     */
    private String reason;

    public static SingerVerifyAuditLosingVO of(Long id, String reason) {
        SingerVerifyAuditLosingVO losingVO = new SingerVerifyAuditLosingVO();
        losingVO.setId(id);
        losingVO.setReason(reason);
        return losingVO;
    }
}
