package fm.lizhi.ocean.wave.management.model.param.family.offlinezone;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 更新广告展位参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UpdateAdPositionParam extends SaveAdPositionParam {

    /**
     * 广告展位ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;
}
