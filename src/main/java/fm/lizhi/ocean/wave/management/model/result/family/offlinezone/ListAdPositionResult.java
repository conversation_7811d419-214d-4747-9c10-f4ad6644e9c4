package fm.lizhi.ocean.wave.management.model.result.family.offlinezone;

import com.ctrip.framework.apollo.core.enums.Env;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneAdPositionStatusEnum;
import lombok.Data;

/**
 * 列出广告展位结果
 */
@Data
public class ListAdPositionResult {

    /**
     * 广告展位ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 环境：TEST/PRE/PRO
     *
     * @see Env#name()
     */
    private String deployEnv;

    /**
     * 主题
     */
    private String theme;

    /**
     * 横幅图片URL
     */
    private String banner;

    /**
     * 权重，数字越大越靠前
     */
    private Integer weight;

    /**
     * 跳转链接
     */
    private String jumpLink;

    /**
     * 上架状态：0-下架，1-上架
     *
     * @see OfflineZoneAdPositionStatusEnum
     */
    private Integer status;

    /**
     * 是否删除：0-否，1-是
     */
    private Boolean deleted;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long modifyTime;
}
