package fm.lizhi.ocean.wave.management.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ActivityBaseEnumConfigConvert;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityBaseConfigResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.remote.service.activitycenter.ActivityBaseEnumConfigServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityConfigBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 基础业务配置
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityBaseEnumConfigManager {

    @Autowired
    private ActivityBaseEnumConfigServiceRemote activityBaseEnumConfigServiceRemote;


    /**
     * 获取一些基础的枚举配置
     */
    public ResultVO<ActivityBaseConfigResult> getBaseEnumConfig(Integer appId){
        Result<ResponseActivityConfigBean> result = activityBaseEnumConfigServiceRemote.getBaseEnumConfig(appId);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result);
        }

        ActivityBaseConfigResult activityBaseConfigResult = ActivityBaseEnumConfigConvert.I.toActivityBaseConfigResult(result.target());
        //西米特殊处理，将官频位的资源显示的名称进行转换（不使用mapstruct处理，防止逻辑无法清晰化）
        if (BusinessEvnEnum.XIMI.getAppId() == appId) {
            activityBaseConfigResult.getAutoConfigResourceList()
            .stream().filter(item -> Objects.equals(item.getResourceCode(), AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode()))
                .forEach(item -> {
                    item.setName("官频位（含节目单）");
                });
        }
        return ResultVO.success(activityBaseConfigResult);
    }


}
