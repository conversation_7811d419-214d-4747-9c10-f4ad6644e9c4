package fm.lizhi.ocean.wave.management.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;

import java.net.URISyntaxException;
import java.util.StringJoiner;

/**
 * URL工具类
 */
public class UrlUtils {

    /**
     * 添加CDN host.
     * <ul>
     *     <li>如果原path为空, 则返回空白字符串</li>
     *     <li>如果原path已经有host, 则不再添加</li>
     * </ul>
     *
     * @param path URL路径
     * @param host CDN host
     * @return 添加host后的URL
     */
    public static String addHostOrEmpty(String path, String host) {
        return addHostOrEmpty(path, host, StringUtils.EMPTY);
    }

    /**
     * 添加CDN host和prefix.
     * <ul>
     *     <li>如果原path为空, 则返回空白字符串</li>
     *     <li>如果原path已经有host, 则不再添加</li>
     * </ul>
     *
     * @param path   URL路径
     * @param host   CDN host
     * @param prefix 路径前缀
     * @return 添加host和prefix后的URL
     */
    public static String addHostOrEmpty(String path, String host, String prefix) {
        if (StringUtils.isBlank(path)) {
            return StringUtils.EMPTY;
        }
        URIBuilder pathUriBuilder;
        try {
            pathUriBuilder = new URIBuilder(path);
        } catch (URISyntaxException | RuntimeException e) {
            throw new IllegalArgumentException("输入的path不是合法的URI: " + path, e);
        }
        URIBuilder hostUriBuilder;
        try {
            hostUriBuilder = new URIBuilder(host);
        } catch (URISyntaxException | RuntimeException e) {
            throw new IllegalArgumentException("输入的host不是合法的URI: " + host, e);
        }
        String finalScheme = pathUriBuilder.getHost() != null ? pathUriBuilder.getScheme() : hostUriBuilder.getScheme();
        String finalHost = pathUriBuilder.getHost() != null ? pathUriBuilder.getHost() : hostUriBuilder.getHost();
        int finalPort = pathUriBuilder.getHost() != null ? pathUriBuilder.getPort() : hostUriBuilder.getPort();
        String finalPath = StringUtils.isBlank(prefix) ? pathUriBuilder.getPath() : addPrefixToPath(prefix, pathUriBuilder.getPath());
        try {
            pathUriBuilder.setScheme(finalScheme);
            pathUriBuilder.setHost(finalHost);
            pathUriBuilder.setPort(finalPort);
            pathUriBuilder.setPath(finalPath);
            return pathUriBuilder.build().toASCIIString();
        } catch (URISyntaxException | RuntimeException e) {
            throw new IllegalStateException(String.format("添加host失败, path=%s, host=%s, prefix=%s", path, host, prefix), e);
        }
    }

    private static String addPrefixToPath(String prefix, String path) {
        StringJoiner pathJoiner = new StringJoiner("/", "/", "");
        for (String s : prefix.split("/")) {
            if (StringUtils.isNotEmpty(s)) {
                pathJoiner.add(s);
            }
        }
        for (String s : path.split("/")) {
            if (StringUtils.isNotEmpty(s)) {
                pathJoiner.add(s);
            }
        }
        return pathJoiner.toString();
    }

    /**
     * 替换CDN host.
     * <ul>
     *     <li>如果原path为空, 则返回空白字符串</li>
     *     <li>如果原path没有host, 则添加host</li>
     *     <li>如果原path已经有host, 则替换host</li>
     * </ul>
     *
     * @param path URL路径
     * @param host CDN host
     * @return 替换host后的URL
     */
    public static String replaceHostOrEmpty(String path, String host) {
        if (StringUtils.isBlank(path)) {
            return StringUtils.EMPTY;
        }
        URIBuilder pathUriBuilder;
        try {
            pathUriBuilder = new URIBuilder(path);
        } catch (URISyntaxException | RuntimeException e) {
            throw new IllegalArgumentException("输入的path不是合法的URI: " + path, e);
        }
        URIBuilder hostUriBuilder;
        try {
            hostUriBuilder = new URIBuilder(host);
        } catch (URISyntaxException | RuntimeException e) {
            throw new IllegalArgumentException("输入的host不是合法的URI: " + host, e);
        }
        String finalScheme = hostUriBuilder.getScheme() != null ? hostUriBuilder.getScheme() : pathUriBuilder.getScheme();
        String finalHost = hostUriBuilder.getHost() != null ? hostUriBuilder.getHost() : pathUriBuilder.getHost();
        int finalPort = hostUriBuilder.getHost() != null ? hostUriBuilder.getPort() : pathUriBuilder.getPort();
        try {
            pathUriBuilder.setScheme(finalScheme);
            pathUriBuilder.setHost(finalHost);
            pathUriBuilder.setPort(finalPort);
            return pathUriBuilder.build().toASCIIString();
        } catch (URISyntaxException | RuntimeException e) {
            throw new IllegalStateException(String.format("替换host失败, path=%s, host=%s", path, host), e);
        }
    }

    /**
     * 移除CDN host, 如果url为空则返回空白字符串.
     *
     * @param url URL路径
     * @return 移除host后的URL
     */
    public static String removeHostOrEmpty(String url) {
        if (StringUtils.isBlank(url)) {
            return StringUtils.EMPTY;
        }
        try {
            boolean startsWithSlash = url.startsWith("/");
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.setScheme(null);
            uriBuilder.setHost(null);
            uriBuilder.setPort(-1);
            String relativePath = uriBuilder.build().toASCIIString();
            return startsWithSlash ? relativePath : relativePath.substring(1);
        } catch (URISyntaxException | RuntimeException e) {
            throw new IllegalArgumentException("输入的url不是合法的URI: " + url, e);
        }
    }
}
