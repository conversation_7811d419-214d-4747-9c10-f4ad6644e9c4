package fm.lizhi.ocean.wave.management;

import fm.lizhi.common.datastore.mysql.spring.boot.annotation.EnableDatastoreMysqlConfig;
import fm.lizhi.common.kafka.ioc.spring.EnableKafkaClients;
import fm.lizhi.ocean.wavecenter.datastore.anchor.AnchorPackagePath;
import fm.lizhi.ocean.wavecenter.datastore.award.AwardPackagePath;
import fm.lizhi.ocean.wavecenter.datastore.family.FamilyPackagePath;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 启动参数详见README.md
 */
@SpringBootApplication
@EnableKafkaClients(basePackages = {"fm.lizhi.ocean.wave.management.kafka.utility.webp.consumer"})
@EnableDatastoreMysqlConfig(basePackages = {
        "fm.lizhi.ocean.wave.management.datastore",
        AnchorPackagePath.DATASTORE
        , AwardPackagePath.DATASTORE
        , FamilyPackagePath.DATASTORE
}
)
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
