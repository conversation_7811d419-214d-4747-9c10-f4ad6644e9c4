package fm.lizhi.ocean.wave.management.remote.grow.capability;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.grow.CapabilityConvert;
import fm.lizhi.ocean.wave.management.model.param.grow.capability.SaveCapabilityParam;
import fm.lizhi.ocean.wavecenter.api.grow.capability.request.RequestSaveCapability;
import fm.lizhi.ocean.wavecenter.api.grow.capability.response.ResponseCapability;
import fm.lizhi.ocean.wavecenter.api.grow.capability.service.CapabilityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 能力项远程服务
 * 封装下游 CapabilityService.saveCapability 调用
 * <AUTHOR>
 */
@Component
public class CapabilityRemote {
    @Autowired
    private CapabilityService capabilityService;

    /**
     * 保存能力项
     * @param param 能力项参数
     * @return 结果，rCode=0 表示成功
     */
    public Result<Void> saveCapability(SaveCapabilityParam param) {
        RequestSaveCapability request = CapabilityConvert.INSTANCE.toRequestSaveCapability(param);
        return capabilityService.saveCapability(request);
    }

    /**
     * 查询能力项列表
     * @param appId 应用ID
     * @return 能力项列表原始结果，rCode=0 表示成功
     */
    public Result<List<ResponseCapability>> queryCapabilityList(Integer appId) {
        return capabilityService.queryCapabilityList(appId);
    }
} 