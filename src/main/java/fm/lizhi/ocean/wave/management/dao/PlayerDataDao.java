package fm.lizhi.ocean.wave.management.dao;

import fm.lizhi.ocean.wave.management.model.dto.family.offlinezone.ListPlayerDataDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;

import fm.lizhi.ocean.wave.management.datastore.mapper.family.offlinezone.PlayerDataExtMapper;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.ListPlayerDataParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListPlayerDataResult;

/**
 * 线下主播数据访问层
 * <AUTHOR>
 */
@Repository
public class PlayerDataDao {

    @Autowired
    private PlayerDataExtMapper playerDataExtMapper;

    /**
     * 分页查询线下主播数据列表（带保护状态）
     * @param param 查询参数
     * @return 分页结果
     */
    public PageList<ListPlayerDataDTO> listPlayerDataWithProtection(ListPlayerDataParam param) {
        return playerDataExtMapper.listPlayerDataWithProtection(param, param.getPageNo(), param.getPageSize());
    }

}