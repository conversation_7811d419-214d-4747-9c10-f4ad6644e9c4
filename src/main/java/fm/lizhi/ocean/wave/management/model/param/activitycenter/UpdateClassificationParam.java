package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class UpdateClassificationParam {

    private Long id;

    /**
     * 分类名称
     */
    private String className;

    /**
     * 等级 ID
     */
    private Long levelId;

    /**
     * 权重
     */
    private Integer weight;
}
