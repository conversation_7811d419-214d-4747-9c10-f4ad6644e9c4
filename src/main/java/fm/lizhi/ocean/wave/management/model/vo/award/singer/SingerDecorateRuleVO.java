package fm.lizhi.ocean.wave.management.model.vo.award.singer;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 *
 * 歌手装扮规则配置
 *
 * <AUTHOR>
 * @date 2025-03-27 03:27:23
 */
@Data
@Accessors(chain = true)
public class SingerDecorateRuleVO {
    /**
     * ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 歌手认证等级
     */
    private Integer singerType;

    /**
     * 曲风
     */
    private String songStyle;

    /**
     * 装扮类型
     * @see PlatformDecorateTypeEnum
     */
    private Integer decorateType;

    /**
     * 装扮ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long decorateId;


    /**
     * 是否启用 0:停用，1: 启用
     */
    private Boolean enabled;

    /**
     * 是否删除 0未删除 1已删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long modifyTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 条件
     */
    private List<SingerDecorateConditionVO> conditionList;

}