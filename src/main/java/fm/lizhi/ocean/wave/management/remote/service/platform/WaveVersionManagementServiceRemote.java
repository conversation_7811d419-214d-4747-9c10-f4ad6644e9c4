package fm.lizhi.ocean.wave.management.remote.service.platform;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.platform.api.platform.version.request.RequestReplaceGrayGroupUsers;
import fm.lizhi.ocean.wave.platform.api.platform.version.response.ResponseGetGrayGroup;
import fm.lizhi.ocean.wave.platform.api.platform.version.service.WaveVersionManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class WaveVersionManagementServiceRemote {

    @Autowired
    private WaveVersionManagementService waveVersionManagementService;

    /**
     * 获取灰度分组
     *
     * @param groupId 分组ID
     * @return 结果
     */
    public Result<ResponseGetGrayGroup> getGrayGroup(long groupId) {
        log.info("getGrayGroup groupId={}", groupId);
        Result<ResponseGetGrayGroup> result = waveVersionManagementService.getGrayGroup(groupId);
        if (ResultUtils.isSuccess(result)) {
            log.info("getGrayGroup success, target: {}", result.target());
        } else {
            log.info("getGrayGroup fail, rCode: {}, message: {}", result.rCode(), result.getMessage());
        }
        return result;
    }

    /**
     * 替换灰度分组用户
     *
     * @param groupId  分组ID
     * @param appId    应用ID
     * @param userIds  用户ID列表
     * @param operator 操作人
     * @return 结果
     */
    public Result<Void> replaceGrayGroupUsers(long groupId, int appId, List<Long> userIds, String operator) {
        RequestReplaceGrayGroupUsers req = new RequestReplaceGrayGroupUsers();
        req.setGroupId(groupId);
        req.setAppId(appId);
        req.setUserIds(userIds);
        req.setOperator(operator);
        log.info("replaceGrayGroupUsers req={}", req);
        Result<Void> result = waveVersionManagementService.replaceGrayGroupUsers(req);
        if (ResultUtils.isSuccess(result)) {
            log.info("replaceGrayGroupUsers success");
        } else {
            log.info("replaceGrayGroupUsers fail, rCode: {}, message: {}", result.rCode(), result.getMessage());
        }
        return result;
    }
}
