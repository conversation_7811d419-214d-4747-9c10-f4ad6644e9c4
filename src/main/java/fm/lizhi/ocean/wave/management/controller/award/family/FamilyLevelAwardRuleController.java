package fm.lizhi.ocean.wave.management.controller.award.family;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.award.family.FamilyLevelAwardRuleConvert;
import fm.lizhi.ocean.wave.management.model.param.award.family.CreateFamilyLevelAwardRuleParam;
import fm.lizhi.ocean.wave.management.model.param.award.family.DeleteFamilyLevelAwardRuleParam;
import fm.lizhi.ocean.wave.management.model.param.award.family.GetFamilyLevelAwardRuleParam;
import fm.lizhi.ocean.wave.management.model.param.award.family.UpdateFamilyLevelAwardRuleParam;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.award.family.GetFamilyLevelAwardRuleVO;
import fm.lizhi.ocean.wave.management.model.vo.award.family.ListFamilyLevelAwardRuleVO;
import fm.lizhi.ocean.wave.management.remote.service.award.family.FamilyLevelAwardRuleServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.ListFamilyLevelAwardRuleBean;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyLevelAwardRule;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公会等级奖励规则
 */
@RestController
@RequestMapping("/award/family/levelAwardRule")
@Slf4j
public class FamilyLevelAwardRuleController {

    @Autowired
    private FamilyLevelAwardRuleServiceRemote familyLevelAwardRuleServiceRemote;

    @PostMapping("/create")
    public ResultVO<Void> create(@RequestBody CreateFamilyLevelAwardRuleParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("Create family level award rule, param={}, appId={}, operator={}", param, appId, operator);
        Result<Void> result = familyLevelAwardRuleServiceRemote.createFamilyLevelAwardRule(param, appId, operator);
        if (ResultUtils.isFailure(result)) {
            log.info("Create family level award rule failed, param={}, rCode={}, msg={}", param, result.rCode(), result.getMessage());
            return ResultVO.failure(result);
        }
        log.info("Create family level award rule success");
        return ResultVO.success();
    }

    @PostMapping("/update")
    public ResultVO<Void> update(@RequestBody UpdateFamilyLevelAwardRuleParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("Update family level award rule, param={}, appId={}, operator={}", param, appId, operator);
        Result<Void> result = familyLevelAwardRuleServiceRemote.updateFamilyLevelAwardRule(param, appId, operator);
        if (ResultUtils.isFailure(result)) {
            log.info("Update family level award rule failed, param={}, rCode={}, msg={}", param, result.rCode(), result.getMessage());
            return ResultVO.failure(result);
        }
        log.info("Update family level award rule success");
        return ResultVO.success();
    }

    @PostMapping("/delete")
    public ResultVO<Void> delete(@RequestBody DeleteFamilyLevelAwardRuleParam param) {
        String operator = SessionUtils.getAccount();
        log.info("Delete family level award rule, param={}, operator={}", param, operator);
        Result<Void> result = familyLevelAwardRuleServiceRemote.deleteFamilyLevelAwardRule(param, operator);
        if (ResultUtils.isFailure(result)) {
            log.info("Delete family level award rule failed, param={}, rCode={}, msg={}", param, result.rCode(), result.getMessage());
            return ResultVO.failure(result);
        }
        log.info("Delete family level award rule success");
        return ResultVO.success();
    }

    @GetMapping("/list")
    public ResultVO<List<ListFamilyLevelAwardRuleVO>> list() {
        Integer appId = SessionExtUtils.getAppId();
        log.info("List family level award rule, appId={}", appId);
        Result<List<ListFamilyLevelAwardRuleBean>> result = familyLevelAwardRuleServiceRemote.listFamilyLevelAwardRule(appId);
        if (ResultUtils.isFailure(result)) {
            log.info("List family level award rule failed, appId={}, rCode={}, msg={}", appId, result.rCode(), result.getMessage());
            return ResultVO.failure(result);
        }
        log.info("List family level award rule success");
        List<ListFamilyLevelAwardRuleBean> beans = result.target();
        log.debug("List family level award rule, beans={}", beans);
        List<ListFamilyLevelAwardRuleVO> VOS = FamilyLevelAwardRuleConvert.I.toListFamilyLevelAwardRuleVOS(beans);
        return ResultVO.success(VOS);
    }

    @GetMapping("/get")
    public ResultVO<GetFamilyLevelAwardRuleVO> get(GetFamilyLevelAwardRuleParam param) {
        log.info("Get family level award rule, param={}", param);
        Result<ResponseGetFamilyLevelAwardRule> result = familyLevelAwardRuleServiceRemote.getFamilyLevelAwardRule(param);
        if (ResultUtils.isFailure(result)) {
            log.info("Get family level award rule failed, param={}, rCode={}, msg={}", param, result.rCode(), result.getMessage());
            return ResultVO.failure(result);
        }
        log.info("Get family level award rule success");
        ResponseGetFamilyLevelAwardRule response = result.target();
        log.debug("Get family level award rule, response={}", response);
        GetFamilyLevelAwardRuleVO VO = FamilyLevelAwardRuleConvert.I.toGetFamilyLevelAwardRuleVO(response);
        return ResultVO.success(VO);
    }
}
