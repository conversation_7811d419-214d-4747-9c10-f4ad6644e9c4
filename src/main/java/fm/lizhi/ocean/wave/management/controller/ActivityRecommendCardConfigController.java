package fm.lizhi.ocean.wave.management.controller;


import fm.lizhi.ocean.wave.management.manager.ActivityRecommendCardConfigManager;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.DeleteActivityRecommendCardParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityRecommendCardParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityRecommendCardParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityRecommendCardConfigResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/activity/recommendCard")
@Slf4j
public class ActivityRecommendCardConfigController {

    @Autowired
    private ActivityRecommendCardConfigManager activityRecommendCardConfigManager;

    /**
     * 保存推荐卡
     * @param param
     * @return
     */
    @PostMapping("/save")
    public ResultVO<Void> save(@RequestBody SaveActivityRecommendCardParam param){
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("save recommend card, param: {}, appId: {}, operator: {}", param, appId, operator);

        return activityRecommendCardConfigManager.save(param, appId, operator);
    }

    /**
     * 更新推荐卡
     * @param param
     * @return
     */
    @PostMapping("/update")
    public ResultVO<Void> update(@RequestBody UpdateActivityRecommendCardParam param){
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("update recommend card, param:{}, appId: {}, operator: {}",param, appId, operator);

        return activityRecommendCardConfigManager.update(param, appId, operator);
    }

    /**
     * 删除推荐卡
     * @param param
     * @return
     */
    @PostMapping("/delete")
    public ResultVO<Void> delete(@RequestBody DeleteActivityRecommendCardParam param){
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("delete recommend card, param:{}, appId: {}, operator: {}", param, appId, operator);

        return activityRecommendCardConfigManager.delete(param.getId(), appId, operator);
    }

    /**
     * 获取列表
     * @return
     */
    @GetMapping("/list")
    public ResultVO<List<ActivityRecommendCardConfigResult>> list(){
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("list recommend card, appId: {}, operator: {}", appId, operator);
        return activityRecommendCardConfigManager.list(appId);
    }


}
