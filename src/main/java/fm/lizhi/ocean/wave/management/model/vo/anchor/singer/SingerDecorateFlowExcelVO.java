package fm.lizhi.ocean.wave.management.model.vo.anchor.singer;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Data;

@Data
public class SingerDecorateFlowExcelVO {

    /**
     * 记录ID
     */
    @ExcelProperty(value = "记录ID")
    private String id;

    /**
     * 装扮类型
     */
    @ExcelProperty(value = "装扮类型")
    private String decorateType;

    /**
     * 装扮ID
     */
    @ExcelProperty(value = "装扮ID")
    private String decorateId;

    /**
     * 操作类型 1: 发放 2: 回收
     */
    @ExcelProperty(value = "操作类型")
    private String operateType;

    /**
     * 操作人
     */
    @ExcelProperty(value = "操作人")
    private String operator;

    /**
     * 操作时间
     */
    @ExcelProperty(value = "操作时间")
    private String operateTime;

    /**
     * 操作原因
     */
    @ExcelProperty(value = "操作原因")
    private String reason;

    /**
     * 装扮名称
     */
    @ExcelProperty(value = "装扮名称")
    private String decorateName;

    /**
     * 预览 URL
     */
    @ExcelProperty(value = "预览 URL")
    private String previewUrl;


    /**
     * 主播ID
     */
    @ExcelProperty(value = "主播ID")
    private String userId;

    /**
     * 昵称
     */
    @ExcelProperty(value = "昵称")
    private String name;
}
