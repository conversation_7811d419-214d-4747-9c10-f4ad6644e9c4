package fm.lizhi.ocean.wave.management.controller.resource;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.resource.ShortNumberConvert;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.resource.ListShortNumberVO;
import fm.lizhi.ocean.wave.management.remote.service.resource.ShortNumberServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.response.ResponseListShortNumber;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/resource/shortNumber")
public class ShortNumberController {

    @Autowired
    private ShortNumberServiceRemote shortNumberServiceRemote;

    @GetMapping("/list")
    public ResultVO<List<ListShortNumberVO>> list() {
        Integer appId = SessionExtUtils.getAppId();
        log.info("listShortNumber appId={}", appId);
        Result<List<ResponseListShortNumber>> result = shortNumberServiceRemote.listShortNumber(appId);
        if (ResultUtils.isFailure(result)) {
            log.info("listShortNumber failed, appId={}, rCode={}, message={}", appId, result.rCode(), result.getMessage());
            return ResultVO.failure(result);
        }
        List<ResponseListShortNumber> list = result.target();
        log.debug("listShortNumber list={}", list);
        List<ListShortNumberVO> VOS = ShortNumberConvert.I.toListShortNumberVOS(list);
        return ResultVO.success(VOS);
    }
}
