package fm.lizhi.ocean.wave.management.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ActivityNoticeConfigConvert;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.ActivityUpateNoticeParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.DeleteNoticeParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityNoticeResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestDeleteActivityNoticeConfig;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityNoticeConfig;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityNoticeConfig;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityNoticeConfigService;
import fm.lizhi.sso.client.SessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/activity")
public class ActivityNoticeConfigController {

    @Autowired
    private ActivityNoticeConfigService activityNoticeConfigService;

    /**
     * 获取活动公告配置
     *
     * @return 活动公告配置
     */
    @GetMapping("/getNoticeConfig")
    public ResultVO<List<ActivityNoticeResult>> getActivityNoticeConfig() {
        Integer appId = SessionExtUtils.getAppId();
        Result<List<ResponseGetActivityNoticeConfig>> result = activityNoticeConfigService.getNoticeConfig(appId);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure("查询活动公告失败");
        }

        List<ResponseGetActivityNoticeConfig> target = result.target();
        List<ActivityNoticeResult> list = ActivityNoticeConfigConvert.I.convertActivityNoticeResultList(target);
        return ResultVO.success(list);
    }

    /**
     * 更新活动公告配置
     *
     * @param param 活动公告配置
     * @return 更新结果
     */
    @PostMapping("/saveNotice")
    public ResultVO<Long> updateActivityNoticeConfig(@RequestBody ActivityUpateNoticeParam param) {
        Integer appId = SessionExtUtils.getAppId();
        RequestUpdateActivityNoticeConfig request = new RequestUpdateActivityNoticeConfig();
        request.setAppId(appId);
        request.setContent(param.getContent());
        request.setId(param.getId());
        request.setCategoryList(param.getCategoryList());
        request.setOperator(SessionUtils.getAccount());
        Result<Long> result = activityNoticeConfigService.updateNoticeConfig(request);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result.rCode(), result.getMessage());
        }
        return ResultVO.success(result.target());
    }


    /**
     * 删除活动公告
     *
     * @return 删除结果
     */
    @PostMapping("/deleteNotice")
    public ResultVO<Void> deleteActivityNotice(@RequestBody DeleteNoticeParam param) {
        Integer appId = SessionExtUtils.getAppId();
        RequestDeleteActivityNoticeConfig request = new RequestDeleteActivityNoticeConfig();
        request.setAppId(appId);
        request.setId(param.getId());
        request.setOperator(SessionUtils.getAccount());
        Result<Void> result = activityNoticeConfigService.deleteNoticeConfig(request);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result.rCode(), result.getMessage());
        }
        return ResultVO.success();
    }
}
