package fm.lizhi.ocean.wave.management.model.param.permission;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/20 15:18
 */
@Data
public class SavePermissionParam {

    /**
     * ID 编辑的时候必传
     */
    private Long id;

    @NotBlank(message = "名称为空")
    private String permissionName;

    @NotBlank(message = "权限编码为空")
    private String permissionCode;

    @NotNull(message = "权限类型为空")
    private Integer permissionType;

    /**
     * 关联角色的角色ID
     */
    private List<Integer> roles;

}
