package fm.lizhi.ocean.wave.management.remote.service.anchor.singer;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerInfoConvert;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.BatchImportSingerParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.EliminateSingerParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.PageSingerInfoParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.UpgradeSingerParam;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestEliminateSinger;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestPageSingerInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestUpgradeSinger;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponsePageSingerInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerInfoAdminService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.ImportSingerInfoBean;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.request.RequestBatchImportSinger;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.response.ResponseBatchImportSinger;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.service.BatchImportSingerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class SingerInfoRemote {

    @Autowired
    private SingerInfoAdminService singerInfoAdminService;
    @Autowired
    private BatchImportSingerService batchImportSingerService;

    /**
     * 分页查询歌手信息
     */
    public Result<PageBean<ResponsePageSingerInfo>> pageSingerInfo(PageSingerInfoParam param, Integer appId) {

        RequestPageSingerInfo request = SingerInfoConvert.INSTANCE.convertRequestPageSingerInfo(param, appId);
        Result<PageBean<ResponsePageSingerInfo>> result = singerInfoAdminService.pageSingerInfo(request);
        if (ResultUtils.isFailure(result)) {
            log.warn("pageSingerInfo is fail. appId:{}, param:{}", appId, param);
            return result;
        }
        return result;
    }

    /**
     * 淘汰歌手
     */
    public Result<Void> eliminateSinger(EliminateSingerParam param, Integer appId, String operator, boolean isManual) {
        RequestEliminateSinger request = SingerInfoConvert.INSTANCE.convertRequestEliminateSinger(param, appId, operator, isManual);
        Result<Void> result = singerInfoAdminService.eliminateSinger(request);
        if (ResultUtils.isFailure(result)) {
            log.warn(" eliminateSinger is fail. appId:{}, param:{}", appId, param);
            return result;
        }
        return result;
    }

    /**
     * 晋升歌手
     */
    public Result<Void> upgradeSinger(UpgradeSingerParam param, Integer appId, String operator) {
        RequestUpgradeSinger request = SingerInfoConvert.INSTANCE.convertRequestUpgradeSinger(param, appId, operator);
        Result<Void> result = singerInfoAdminService.upgradeSinger(request);
        if (ResultUtils.isFailure(result)) {
            log.warn(" upgradeSinger is fail. appId:{}, param:{}", appId, param);
            return result;
        }
        return result;
    }


    public Result<ResponseBatchImportSinger> importSinger(List<ImportSingerInfoBean> singerInfoList, Integer appId, String operator) {
        RequestBatchImportSinger req = SingerInfoConvert.INSTANCE.toRequestBatchImportSinger(singerInfoList, appId, operator);
        Result<ResponseBatchImportSinger> result = batchImportSingerService.batchImportSinger(req);
        if (ResultUtils.isFailure(result)) {
            log.warn("importSinger is fail;appId:{};operator={};singerInfoList:{}", appId, operator, singerInfoList);
            return result;
        }
        return result;
    }


}
