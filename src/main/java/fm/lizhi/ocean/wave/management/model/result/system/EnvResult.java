package fm.lizhi.ocean.wave.management.model.result.system;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 系统环境信息
 */
@Data
public class EnvResult {

    /**
     * 服务名
     */
    private String serviceName;

    /**
     * 区域
     */
    private String region;

    /**
     * 业务环境
     */
    private String businessEnv;

    /**
     * 部署环境
     */
    private String deployEnv;

    /**
     * 迭代标签
     */
    @JsonProperty("vTag")
    private String vTag;

    /**
     * 项目版本
     */
    private String projectVersion;

    /**
     * 单点登录服务器地址, 格式为 {@code "https://lzssooffice.lizhi.fm"}
     */
    private String ssoServerAddress;
}
