package fm.lizhi.ocean.wave.management.remote.service.activitycenter.ai;

import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.ActivityAiResultRateRequest;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityAiResultRateService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.ai.ActivityAiResultRateParam;

@Component
public class ActivityAiResultRateRemote {
    @Autowired
    private ActivityAiResultRateService activityAiResultRateService;

    /**
     * 调用下游服务保存AI结果评分
     */
    public Result<Void> saveActivityAiResultRate(ActivityAiResultRateParam param, Integer appId, String creator) {
        ActivityAiResultRateRequest request = new ActivityAiResultRateRequest();
        request.setType(param.getType());
        request.setSerialId(param.getSerialId());
        request.setScore(param.getScore());
        request.setAdvice(param.getAdvice());
        request.setAppId(appId);
        request.setCreator(creator);
        return activityAiResultRateService.saveActivityAiResultRate(request);
    }
} 