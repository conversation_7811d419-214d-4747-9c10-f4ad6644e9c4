package fm.lizhi.ocean.wave.management.manager.singer.handler;

import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerInfoDao;
import fm.lizhi.ocean.wave.management.manager.singer.SingerPushManager;
import fm.lizhi.ocean.wave.management.manager.singer.SingerVerifyApplyManager;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerVerifyConvert;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerAuditParamDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerAuditPassedDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerExecuteAuditDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.UpdateSingerVerifyStatusParamDTO;
import fm.lizhi.ocean.wave.management.processor.singer.ISingerVerifyProcessor;
import fm.lizhi.ocean.wave.management.remote.service.anchor.singer.SingerChatRemote;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.*;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateOperateReasonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingerAuditPassedHandler extends AbstractSingerAuditHandler {

    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;
    @Autowired
    private SingerChatRemote singerChatRemote;
    @Autowired
    private SingerInfoDao singerInfoDao;

    @Autowired
    private SingerPushManager singerPushManager;

    @Autowired
    private ProcessorFactory factory;

    @Override
    public SingerExecuteAuditDTO auditHandle(SingerAuditParamDTO param, SingerVerifyRecord verifyRecord) {
        //校验歌手状态 要拿准备通过的歌手等级 而不是认证里面的歌手等级
        SingerInfo singerInfo = singerInfoDao.getSingerInfo(verifyRecord.getAppId(), verifyRecord.getUserId(), param.getSingerType(), true);
        SingerExecuteAuditDTO singerExecuteAuditDTO = checkSingerStatus(singerInfo, verifyRecord);
        if (!singerExecuteAuditDTO.isSuccess()) {
            log.warn("checkSingerStatus fail, singerId:{}, singerType:{}, reason:{}",
                    verifyRecord.getUserId(), param.getSingerType(), singerExecuteAuditDTO.getReason());
            return singerExecuteAuditDTO;
        }

        boolean success;
        SingerChatSceneEnum chatSceneEnum;
        // 需要校验点唱厅状态，目前只有黑叶过渡阶段需要
        if (param.isNeedCheckSingerHallStatus() && param.getSingerHallStatus() != SingerHallApplyStatusEnum.APPLYED) {
            Pair<Boolean, SingerChatSceneEnum> handleRes = handleHallStatusNoPassVerify(param, verifyRecord);
            success = handleRes.getLeft();
            chatSceneEnum = handleRes.getRight();
        } else {
            //不需要校验点唱厅
            boolean passRes = singerVerifyApplyManager.approveSingerVerifyRecord(SingerAuditPassedDTO.builder(param, verifyRecord, singerInfo));
            chatSceneEnum = passRes ? SingerChatSceneEnum.SINGER_AUDIT_PASS : null;
            success = passRes;
        }
        //装扮操作
        decorateOperateAfterSuccess(success, param, verifyRecord);
        //发送私信
        singerChatRemote.sendSingerChat(verifyRecord.getAppId(), verifyRecord.getUserId(), verifyRecord.getSingerType(), param.getPassSongStyle(), chatSceneEnum);
        return success ? SingerExecuteAuditDTO.success() : SingerExecuteAuditDTO.failure("修改状态失败，请稍候重试!");
    }

    /**
     * 处理需要检验点唱厅状态的审核状态非通过逻辑
     * 目前只有HY历史场景在使用，后续可以干掉
     *
     * @param param        审核参数
     * @param verifyRecord 认证记录
     * @return left: 是否成功，right: 私信场景
     */
    private Pair<Boolean, SingerChatSceneEnum> handleHallStatusNoPassVerify(SingerAuditParamDTO param, SingerVerifyRecord verifyRecord) {
        boolean success = false;
        SingerChatSceneEnum chatSceneEnum = null;
        if (param.getSingerHallStatus() == null || param.getSingerHallStatus() == SingerHallApplyStatusEnum.REJECTED) {
            //拒绝或者是不存在申请，直接设置预审不通过
            UpdateSingerVerifyStatusParamDTO paramDTO = SingerVerifyConvert.INSTANCE.buildUpdateParam(verifyRecord, param, SingerAuditStatusEnum.REJECTED.getStatus(), SingerStatusEnum.ELIMINATED.getStatus(), true, param.getSingerType());
            paramDTO.setRejectReason("点唱厅审核不通过");
            boolean updateRes = singerVerifyApplyManager.updateSingerVerifyRecordStatus(paramDTO);
            log.info("handleHallStatusNoPassVerify no singer hall, paramDTO:{}, res:{}", JsonUtil.dumps(paramDTO), updateRes);
            chatSceneEnum = updateRes ? SingerChatSceneEnum.PRE_AUDIT_NOT_PASS_BY_SIGN : null;
            success = updateRes;
        } else if (param.getSingerHallStatus() == SingerHallApplyStatusEnum.APPLYING) {
            // 执行审核中的操作
            UpdateSingerVerifyStatusParamDTO paramDTO = SingerVerifyConvert.INSTANCE.buildUpdateParam(verifyRecord, param, SingerAuditStatusEnum.PASS.getStatus(), SingerStatusEnum.AUTHENTICATING.getStatus(), true, param.getSingerType());
            boolean updateRes = singerVerifyApplyManager.updateSingerVerifyRecordStatus(paramDTO);
            log.info("handleHallStatusNoPassVerify singer hall auditing, paramDTO:{}, res:{}", JsonUtil.dumps(paramDTO), updateRes);
            chatSceneEnum = updateRes ? SingerChatSceneEnum.HOST_PASS_HALL_AUDITING : null;
            success = updateRes;
        }
        return Pair.of(success, chatSceneEnum);
    }

    /**
     * 校验歌手状态
     *
     * @param singerInfo   歌手信息
     * @param verifyRecord 认证记录
     * @return 结果
     */
    private SingerExecuteAuditDTO checkSingerStatus(SingerInfo singerInfo, SingerVerifyRecord verifyRecord) {
        if (singerInfo != null && singerInfo.getSingerStatus() == SingerStatusEnum.EFFECTIVE.getStatus()) {
            //已经是歌手了，不能再提交申请
            return SingerExecuteAuditDTO.failure("用户已经是歌手了，不能审批通过");
        }
        if (singerInfo != null && singerInfo.getSingerStatus() == SingerStatusEnum.AUTHENTICATING.getStatus() && verifyRecord.getAuditStatus() != SingerAuditStatusEnum.SELECTED.getStatus()) {
            //如果歌手认证中，并且操作不是从：选中状态 -> 通过，则不能通过
            return SingerExecuteAuditDTO.failure("用户已经是在歌手认证中了，不能审批通过");
        }
        return SingerExecuteAuditDTO.success();
    }

    /**
     * 成功之后的操作
     *
     * @param success      是否成功
     * @param param        审核参数
     * @param verifyRecord 认证记录
     */
    private void decorateOperateAfterSuccess(boolean success, SingerAuditParamDTO param, SingerVerifyRecord verifyRecord) {
        try {
            if (!success) {
                return;
            }
            //成功的话，发放等级对应奖励和回收对应等级奖励
            SingerInfo singerInfo = singerInfoDao.getSingerInfo(verifyRecord.getAppId(), verifyRecord.getUserId(), param.getSingerType(), true);
            //发送发放奖励的kafka消息
            singerPushManager.sendSingerPassKafkaMessage(verifyRecord.getAppId(), Lists.newArrayList(singerInfo),
                    param.getOperator(), SingerDecorateOperateReasonConstant.AUTH_PASS, verifyRecord.getId());

            //如果通过的是高级歌手，部分业务需要回收认证歌手的奖励
            if (param.getSingerType().equals(SingerTypeEnum.NEW.getType())) {
                return;
            }
            ISingerVerifyProcessor processor = factory.getProcessor(ISingerVerifyProcessor.class);
            processor.recoverSingerAward(verifyRecord.getAppId(), verifyRecord.getUserId(), SingerTypeEnum.NEW.getType(), param.getOperator());
            log.info("recover singer award operate, singerId:{}, singerType:{}", verifyRecord.getUserId(), param.getSingerType());
        } catch (Exception e) {
            log.error("decorateOperateAfterSuccess happen error, singerId:{}, singerType:{}", verifyRecord.getUserId(), param.getSingerType(), e);
        }
    }

    @Override
    public SingerAuditStatusEnum getAuditStatusEnum() {
        return SingerAuditStatusEnum.PASS;
    }
}
