package fm.lizhi.ocean.wave.management.config.apollo;

import lombok.Data;

import java.time.Duration;

/**
 * dubbo客户端配置项
 */
@Data
public class DubboClientProperties {

    /**
     * 长连接最大连接数
     */
    private int connections = 200;

    /**
     * 请求响应超时时间
     */
    private Duration timeout = Duration.ofSeconds(5);

    /**
     * 重试次数
     */
    private int retries = 0;

    /**
     * 是否异步
     */
    private boolean async = true;
}
