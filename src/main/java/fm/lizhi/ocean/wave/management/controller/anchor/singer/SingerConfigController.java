package fm.lizhi.ocean.wave.management.controller.anchor.singer;

import fm.lizhi.ocean.wave.management.manager.singer.SingerConfigServiceManager;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.AddChatSenceConfigParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.SaveApplyMenuConfigParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.UpdateAuditConfigParam;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.*;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;



@RestController
@RequestMapping("/singer")
public class SingerConfigController {

    @Autowired
    private SingerConfigServiceManager singerConfigServiceManager;

    @GetMapping("/getEnumerateConfig")
    public ResultVO<SingerEnumerateConfigResult> getEnumerateConfig() {
        Integer appId = SessionExtUtils.getAppId();
        return singerConfigServiceManager.getEnumerateConfig(appId);
    }

    @PostMapping("/updateAuditConfig")
    public ResultVO<Void> updateAuditConfig(@RequestBody UpdateAuditConfigParam param) {
        String operator = SessionUtils.getAccount();
        return singerConfigServiceManager.updateAuditConfig(param, operator);
    }

    @GetMapping("/getAuditConfig")
    public ResultVO<List<SingerAuditConfigResult>> getSingerConfig() {
        Integer appId = SessionExtUtils.getAppId();
        return singerConfigServiceManager.getSingerAuditConfig(appId);
    }

    @PostMapping("/addChatSenceConfig")
    public ResultVO<Void> addChatSenceConfig(@RequestBody AddChatSenceConfigParam param) {
        Integer appId = SessionExtUtils.getAppId();
        return singerConfigServiceManager.addChatSenceConfig(param, appId);
    }

    @PostMapping("/deleteChatSenceConfig")
    public ResultVO<Void> deleteChatSenceConfig(@RequestParam String sceneCode) {
        Integer appId = SessionExtUtils.getAppId();
        return singerConfigServiceManager.deleteChatSenceConfig(sceneCode, appId);
    }

    @GetMapping("/getChatSenceConfigs")
    public ResultVO<List<SingerChatSenceConfigResult>> getChatSenceConfigs(@RequestParam(required = false) String sceneCode, @RequestParam(required = false) Integer singerType) {
        Integer appId = SessionExtUtils.getAppId();
        return singerConfigServiceManager.getChatSenceConfigs(appId, sceneCode, singerType);
    }

    /**
     * 获取歌手申请菜单配置
     * @param singerType 歌手类型
     * @return 菜单配置
     */
    @GetMapping("/apply/getMenuConfig")
    public ResultVO<ApplyMenuConfigResult> getApplyMenuConfig(@RequestParam Integer singerType) {
        Integer appId = SessionExtUtils.getAppId();
        return singerConfigServiceManager.getApplyMenuConfig(appId, singerType);
    }

    /**
     * 更新歌手申请菜单配置
     * @param param 菜单配置参数
     * @return 结果
     */
    @PostMapping("/apply/menuConfig")
    public ResultVO<Void> saveApplyMenuConfig(@RequestBody @Valid SaveApplyMenuConfigParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        return singerConfigServiceManager.saveApplyMenuConfig(param, appId, operator);
    }

    @GetMapping("/getSongStyleNumConfig")
    public ResultVO<SongStyleNumResult> getSongStyleNumConfig() {
        Integer appId = SessionExtUtils.getAppId();
        return singerConfigServiceManager.getSongStyleCountConfig(appId);
    }

}
