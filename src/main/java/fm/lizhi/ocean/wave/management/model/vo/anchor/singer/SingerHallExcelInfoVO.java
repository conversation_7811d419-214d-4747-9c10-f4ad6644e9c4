package fm.lizhi.ocean.wave.management.model.vo.anchor.singer;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Data;

@Data
public class SingerHallExcelInfoVO {

    /**
     * 家族名称
     */
    @ExcelProperty(value = "家族昵称")
    private String familyName;

    /**
     * 高级歌手数量
     */
    @ExcelProperty(value = "高级歌手数量")
    private int seniorSingerAuthCnt;

    /**
     * 认证歌手数量
     */
    @ExcelProperty(value = "认证歌手数量")
    private int singerAuthCnt;

    /**
     * 上一自然周营收(钻石)
     */
    @ExcelProperty(value = "上一自然周营收(钻石)")
    private String income;

    /**
     * 点唱厅ID
     */
    @ExcelProperty(value = "点唱厅ID")
    private String singerId;

    /**
     * 厅昵称
     */
    @ExcelProperty(value = "厅昵称")
    private String name;

    /**
     * 厅波段号
     */
    @ExcelProperty(value = "厅波段号")
    private String band;
}
