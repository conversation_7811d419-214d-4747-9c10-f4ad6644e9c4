package fm.lizhi.ocean.wave.management.model.converter.activitycenter;

import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityReportDataGiftResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityReportDataPlayerResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityReportDataDetailResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityReportDataSummaryResult;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataDetailBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataGiftBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataPlayerBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataSummaryBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityReportDataConvert {

    ActivityReportDataConvert I = Mappers.getMapper(ActivityReportDataConvert.class);

    ActivityReportDataSummaryResult toActivityReportDataSummaryResult(ActivityReportDataSummaryBean target);

    List<ActivityReportDataDetailResult> toActivityReportDataDetailResults(List<ActivityReportDataDetailBean> target);

    PageVO<ActivityReportDataPlayerResult> toActivityReportDataPlayerResultPageVO(PageBean<ActivityReportDataPlayerBean> target);

    PageVO<ActivityReportDataGiftResult> toActivityReportDataGiftResultPageVO(PageBean<ActivityReportDataGiftBean> target);

}
