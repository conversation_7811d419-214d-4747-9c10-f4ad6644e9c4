package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @description: 用户活动提报列表
 * @author: guoyibin
 * @create: 2024/10/23 17:55
 */
@Data
@Accessors(chain = true)
public class QueryUserActivitiesResult {

    /**
     * 总数量
     */
    private Integer total;

    /**
     * 活动信息列表
     */
    private List<UserActivitySimpleInfoResult> list;
}
