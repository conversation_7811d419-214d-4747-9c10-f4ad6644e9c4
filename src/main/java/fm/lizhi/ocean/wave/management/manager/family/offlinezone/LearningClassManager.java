package fm.lizhi.ocean.wave.management.manager.family.offlinezone;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wave.management.datastore.dao.family.offlinezone.LearningClassDao;
import fm.lizhi.ocean.wave.management.model.converter.family.offlinezone.LearningClassConvert;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.AddLearningClassParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.DeleteLearningClassParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.ListLearningClassParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.UpdateLearningClassParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.AddLearningClassResult;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListLearningClassResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLearningClass;
import org.apache.commons.collections4.ListValuedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 线下专区学习课堂管理器
 */
@Component
public class LearningClassManager {

    @Autowired
    private LearningClassConvert learningClassConvert;

    @Autowired
    private LearningClassDao learningClassDao;

    /**
     * 新增学习课堂资料
     *
     * @param param 新增参数
     * @return 新增结果的VO
     */
    public ResultVO<AddLearningClassResult> addLearningClass(AddLearningClassParam param) {
        Long learningId = learningClassDao.addLearningClass(param);
        AddLearningClassResult result = learningClassConvert.toAddLearningClassResult(learningId);
        return ResultVO.success(result);
    }

    /**
     * 更新学习课堂资料
     *
     * @param param 更新参数
     * @return 更新结果的VO
     */
    public ResultVO<Void> updateLearningClass(UpdateLearningClassParam param) {
        Long id = param.getId();
        OfflineZoneLearningClass oldClass = learningClassDao.getLearningClass(id);
        if (oldClass == null) {
            return ResultVO.failure("资料ID不存在: " + id);
        }
        if (!Objects.equals(oldClass.getDeployEnv(), ConfigUtils.getEnvRequired().name())) {
            return ResultVO.failure("资料ID不属于当前环境: " + id);
        }
        if (oldClass.getDeleted()) {
            return ResultVO.failure("资料ID已被删除: " + id);
        }
        boolean shouldUpdateWhiteList = shouldUpdateWhiteList(param);
        learningClassDao.updateLearningClass(param, shouldUpdateWhiteList);
        return ResultVO.success();
    }

    private boolean shouldUpdateWhiteList(UpdateLearningClassParam param) {
        Long learningId = param.getId();
        Set<Long> newWhiteIds = new HashSet<>(param.getWhiteIds());
        Set<Long> oldWhiteIds = new HashSet<>(learningClassDao.getWhiteIdsByLearningId(learningId));
        return !Objects.equals(newWhiteIds, oldWhiteIds);
    }

    /**
     * 删除学习课堂资料
     *
     * @param param 删除参数
     * @return 删除结果的VO
     */
    public ResultVO<Void> deleteLearningClass(DeleteLearningClassParam param) {
        Long id = param.getId();
        OfflineZoneLearningClass oldClass = learningClassDao.getLearningClass(id);
        if (oldClass == null) {
            return ResultVO.failure("资料ID不存在: " + id);
        }
        if (!Objects.equals(oldClass.getDeployEnv(), ConfigUtils.getEnvRequired().name())) {
            return ResultVO.failure("资料ID不属于当前环境: " + id);
        }
        if (oldClass.getDeleted()) {
            return ResultVO.failure("资料ID已被删除: " + id);
        }
        learningClassDao.deleteLearningClass(param);
        return ResultVO.success();
    }

    /**
     * 列出学习课堂资料
     *
     * @param param 列出参数
     * @return 分页结果的VO
     */
    public ResultVO<PageVO<ListLearningClassResult>> listLearningClass(ListLearningClassParam param) {
        PageList<OfflineZoneLearningClass> pageList = learningClassDao.pageListLearningClass(param);
        List<Long> learningIds = pageList.stream().map(OfflineZoneLearningClass::getId).collect(Collectors.toList());
        ListValuedMap<Long, Long> classWhiteListIdsMap = learningClassDao.getLearningClassWhiteListIdsMap(learningIds);
        List<ListLearningClassResult> resultList = learningClassConvert.toListLearningClassResults(pageList, classWhiteListIdsMap);
        return ResultVO.success(PageVO.of(pageList.getTotal(), resultList));
    }
}
