package fm.lizhi.ocean.wave.management.controller.resource;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.resource.RecommendCardConvert;
import fm.lizhi.ocean.wave.management.model.param.resource.*;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.resource.BatchSendUserResultVO;
import fm.lizhi.ocean.wave.management.model.vo.resource.RecommendCardSendRecordVO;
import fm.lizhi.ocean.wave.management.model.vo.resource.RecommendCardUseRecordVO;
import fm.lizhi.ocean.wave.management.model.vo.resource.RecommendCardUserStockVO;
import fm.lizhi.ocean.wave.management.remote.service.resource.RecommendCardServiceRemote;
import fm.lizhi.ocean.wave.management.remote.service.user.UserCommonServiceRemote;
import fm.lizhi.ocean.wave.management.remote.service.user.UserFamilyServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.common.bean.ContextRequest;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.BatchSendUserResultBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardSendRecordBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardUseRecordBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardUserStockBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.*;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.service.RecommendCardService;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyDetailBean;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/17 17:00
 */
@Slf4j
@RestController
@RequestMapping("resource/recommendCard")
public class RecommendCardController {

    @Autowired
    private RecommendCardServiceRemote recommendCardServiceRemote;
    @Autowired
    private UserFamilyServiceRemote userFamilyServiceRemote;
    @Autowired
    private UserCommonServiceRemote userCommonServiceRemote;

    /**
     * 回收推荐卡
     * @return
     */
    @PostMapping("recycle")
    public ResultVO<Void> recycle(@RequestBody @Validated RecycleRecommendCardParam param){
        RequestRecycle request = new RequestRecycle()
                .setAppId(SessionExtUtils.getAppId())
                .setOperator(SessionUtils.getAccount())
                .setSendRecordId(param.getRecordId())
                .setUserId(param.getUserId())
                .setNum(param.getNum())
                .setReason(param.getReason());
        Result<Void> result = recommendCardServiceRemote.recycle(request);
        if (ResultUtils.isFailure(result)) {
            log.warn("recycle failed. request:{}, rCode:{}", request, result.rCode());
            return ResultVO.failure("回收失败, 推荐卡可能已被分配");
        }
        return ResultVO.success();
    }

    /**
     * 批量发放推荐卡
     * @param param
     * @return
     */
    @PostMapping("batchSend")
    public ResultVO<List<BatchSendUserResultVO>> batchSend(@RequestBody BatchSendRecommendCardParam param){
        Result<List<BatchSendUserResultBean>> result = recommendCardServiceRemote.batchSend(new RequestBatchSend()
                .setAppId(SessionExtUtils.getAppId())
                .setOperator(SessionUtils.getAccount())
                .setSendRecommendCards(RecommendCardConvert.I.sendCmdVOs2Beans(param.getSendRecommendCards()))
        );
        if (ResultUtils.isFailure(result)) {
            log.warn("batchSend failed. request:{}, rCode:{}", param, result.rCode());
            return ResultVO.failure("系统异常");
        }
        List<BatchSendUserResultBean> target = result.target();
        if (CollectionUtils.isEmpty(target)) {
            return ResultVO.success(Collections.emptyList());
        }

        List<String> errorMsgList = new ArrayList<>();
        for (BatchSendUserResultBean resultBean : result.target()) {
            errorMsgList.add("["+resultBean.getUserId()+"]"+resultBean.getMsg());
        }

        ResultVO<List<BatchSendUserResultVO>> failure = ResultVO.failure(2, Joiner.on("; ").join(errorMsgList));
        failure.setData(RecommendCardConvert.I.batchSendUserResultBeans2VOs(target));
        return failure;
    }

    /**
     * 查询推荐卡发放记录
     * @param param
     * @return
     */
    @GetMapping("getSendRecord")
    public ResultVO<PageVO<RecommendCardSendRecordVO>> getSendRecord(@Validated GetRecommendCardSendRecordParam param) {
        RequestGetSendRecord request = new RequestGetSendRecord();
        request.setAppId(SessionExtUtils.getAppId());
        request.setStartTime(param.getStartTime());
        request.setEndTime(param.getEndTime());
        request.setPageNo(param.getPageNo());
        request.setPageSize(param.getPageSize());
        request.setUserId(param.getUserId());

        Result<PageBean<RecommendCardSendRecordBean>> result = recommendCardServiceRemote.getSendRecord(request);
        if (ResultUtils.isFailure(result)) {
            log.warn("getSendRecord failed. request:{}, rCode:{}", request, result.rCode());
            return ResultVO.failure("系统异常");
        }

        PageBean<RecommendCardSendRecordBean> beanList = result.target();
        List<RecommendCardSendRecordVO> voList = RecommendCardConvert.I.sendRecordBeans2VOs(beanList.getList());
        return ResultVO.success(PageVO.of(beanList.getTotal(), voList));
    }

    /**
     * 推荐卡-使用记录
     * @param param
     * @return
     */
    @GetMapping("getUseRecord")
    public ResultVO<PageVO<RecommendCardUseRecordVO>> getUseRecord(@Validated GetRecommendCardUseRecordParam param) {
        RequestGetUseRecord request = new RequestGetUseRecord();
        request.setAppId(SessionExtUtils.getAppId());
        request.setUserId(param.getUserId());
        request.setPageNo(param.getPageNo());
        request.setPageSize(param.getPageSize());
        Result<PageBean<RecommendCardUseRecordBean>> result = recommendCardServiceRemote.getUseRecordForManagement(request);
        if (ResultUtils.isFailure(result)) {
            log.warn("getUseRecordForManagement failed. request:{}, rCode:{}", request, result.rCode());
            return ResultVO.failure("系统异常");
        }
        List<RecommendCardUseRecordVO> voList = RecommendCardConvert.I.useRecordBeans2VOs(result.target().getList());
        return ResultVO.success(PageVO.of(result.target().getTotal(), voList));
    }

    /**
     * 推荐卡-查询库存
     * @param param
     * @return
     */
    @GetMapping("getUserStock")
    public ResultVO<List<RecommendCardUserStockVO>> getUserStock(@Validated GetRecommendCardUserStockParam param) {
        int appId = SessionExtUtils.getAppId();

        RecommendCardUserStockVO vo = new RecommendCardUserStockVO();

        //查询用户信息
        Result<UserBean> userRes = userCommonServiceRemote.getUserById(appId, param.getUserId());
        if (ResultUtils.isSuccess(userRes)) {
            vo.setUserId(userRes.target().getId());
            vo.setUserName(userRes.target().getName());
            vo.setUserBand(userRes.target().getBand());
        } else {
            log.warn("getUserById fail. rCode={},userId={},appId={}", userRes.rCode(), param.getUserId(), appId);
        }

        //查询家族信息
        Result<UserInFamilyDetailBean> familyRes = userFamilyServiceRemote.getUserInFamilyDetail(param.getUserId(), new ContextRequest().setAppId(appId));
        if (ResultUtils.isSuccess(familyRes)) {
            vo.setFamilyName(familyRes.target().getFamilyName());
            vo.setUserRole(getUserRole(familyRes.target()));
        } else {
            log.warn("getUserInFamilyDetail fail. rCode={}", familyRes.rCode());
        }

        //查询库存
        Result<RecommendCardUserStockBean> result = recommendCardServiceRemote.getUserStock(new RequestGetUserStock()
                .setUserId(param.getUserId())
                .setAppId(appId));
        if (ResultUtils.isSuccess(result)) {
            vo.setStock(result.target().getStock());
            vo.setExpireNum(result.target().getExpireNum());
        } else {
            log.warn("getUserStock fail. rCode={},userId={},appId={}", result.rCode(), param.getUserId(), appId);
        }

        return ResultVO.success(Lists.newArrayList(vo));
    }

    private String getUserRole(UserInFamilyDetailBean userFamily){
        if (userFamily.isFamily()) {
            return "家族长";
        } else if (userFamily.isRoom()) {
            return "厅主";
        } else if (userFamily.isPlayer()) {
            return "主播";
        } else {
            return "普通用户";
        }
    }

}
