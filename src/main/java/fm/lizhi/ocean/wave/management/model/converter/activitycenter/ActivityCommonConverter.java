package fm.lizhi.ocean.wave.management.model.converter.activitycenter;

import fm.lizhi.ocean.wave.management.model.result.activitycenter.GetBaseActivityConfigResult;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetBaseActivityConfig;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityCommonService;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * 活动公共转换器, 主要处理{@link ActivityCommonService}相关bean的转换
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityCommonConverter {

    /**
     * 转换为获取基础活动配置WEB结果
     *
     * @param resp 获取基础活动配置响应
     * @return 获取基础活动配置WEB结果
     */
    GetBaseActivityConfigResult toGetBaseActivityConfigResult(ResponseGetBaseActivityConfig resp);
}
