package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import lombok.Data;

import java.util.List;

/**
 * 新建或更新活动模板参数的基础类
 */
@Data
public class SaveActivityTemplateParam {

    /**
     * 模板名称
     */
    private String name;

    /**
     * 分类id
     */
    private Long classId;

    /**
     * 活动目标
     */
    private String goal;

    /**
     * 活动介绍
     */
    private String introduction;

    /**
     * 活动流程列表
     */
    private List<Process> processes;

    /**
     * 辅助道具图片列表
     */
    private List<String> auxiliaryPropUrls;

    /**
     * 活动海报
     */
    private String posterUrl;

    /**
     * 玩法工具列表
     */
    private List<Integer> activityTools;

    /**
     * 房间公告
     */
    private String roomAnnouncement;

    /**
     * 房间公告图片列表
     */
    private List<String> roomAnnouncementImages;

    /**
     * 房间背景id列表
     */
    private List<Long> roomBackgroundIds;

    /**
     * 房间背景可选数量限制 -1表示没有限制
     */
    private Integer roomBackgroundLimit = 1;

    /**
     * 房间头像框id列表
     */
    private List<Long> avatarWidgetIds;

    /**
     * 礼物列表ID列表
     */
    private List<Long> giftIds;

    /**
     * 头像框可选数量限制 -1表示没有限制
     */
    private Integer avatarWidgetLimit = 1;

    /**
     * 流量资源列表
     */
    private List<FlowResource> flowResources;

    /**
     * 活动限时
     */
    private Integer activityDurationLimit;

    /**
     * 活动开始时间限制
     */
    private Long activityStartTimeLimit;

    /**
     * 活动结束时间限制
     */
    private Long activityEndTimeLimit;



    /**
     * 房间角标ID
     */
    private Long roomMarkId;

    /**
     * 房间角标URL,xm的角标只是一张图片配置，所以没有 ID
     */
    private String roomMarkUrl;

    /**
     * 重复类型
     * @see fm.lizhi.ocean.wavecenter.module.api.background.activitycenter.constants.ActivityTemplateRecurrenceEnum
     */
    private Integer recurrenceType;

    /**
     * 当天+选中的小时+选中的分钟 时间戳，服务端提取小时+分钟
     */
    private Long recurrenceStartTime;

    /**
     * 当天+选中的小时+选中的分钟 时间戳，服务端提取小时+分钟
     */
    private Long recurrenceEndTime;

    /**
     * 活动模板流程
     */
    @Data
    public static class Process {

        /**
         * 环节名称
         */
        private String name;

        /**
         * 时长
         */
        private String duration;

        /**
         * 说明
         */
        private String explanation;
    }

    /**
     * 活动模板流量资源图片扩展信息
     */
    @Data
    public static class FlowResourceImageExtra {

        /**
         * 颜色
         */
        private String color;

        /**
         * 宽高比
         */
        private String scale;
    }

    /**
     * 活动模板流量资源图片
     */
    @Data
    public static class FlowResourceImage {

        /**
         * 图片url
         */
        private String imageUrl;

        /**
         * 图片扩展配置
         */
        private FlowResourceImageExtra extra;
    }

    /**
     * 活动模板流量资源扩展信息
     */
    @Data
    public static class FlowResourceExtra {

        /**
         * 官频位时长限制, 单位分钟, 当资源类型为官频位时有值
         */
//        private Integer durationLimit;

        /**
         * 官频位可选座位号列表, 当资源类型为官频位时有值
         */
        private List<Integer> officialSeatNumbers;

        /**
         * 挂件id, 当资源类型为挂件时有值
         */
        private Long pendantId;
    }

    /**
     * 活动模板流量资源
     */
    @Data
    public static class FlowResource {

        /**
         * 资源配置id
         */
        private Long resourceConfigId;

        /**
         * 流量资源扩展字段
         */
        private FlowResourceExtra extra;

        /**
         * 资源物料图片列表
         */
        private List<FlowResourceImage> images;
    }
}
