package fm.lizhi.ocean.wave.management.datastore.mapper.family.offlinezone;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.ListAdPositionParam;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneAdPosition;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface OfflineZoneAdPositionExtMapper {

    @Delete("UPDATE `offline_zone_ad_position`\n" +
            "SET `deleted` = 1, `operator` = #{operator}, `modify_time` = NOW()\n" +
            "WHERE `id` = #{id} AND `deleted` = 0")
    int deleteById(@Param("id") long id, @Param("operator") String operator);

    @Select("<script>\n" +
            "  SELECT * FROM `offline_zone_ad_position`\n" +
            "  WHERE `app_id` = #{appId}\n" +
            "    AND `deploy_env` = #{deployEnv}\n" +
            "    AND `deleted` = 0\n" +
            "    <if test=\"param.theme != null and param.theme != ''\">\n" +
            "      AND `theme` like CONCAT('%', #{param.theme}, '%')\n" +
            "    </if>\n" +
            "    <if test=\"param.status != null\">\n" +
            "      AND `status` = #{param.status}\n" +
            "    </if>\n" +
            "  ORDER BY `create_time` DESC, `id` DESC\n" +
            "</script>")
    List<OfflineZoneAdPosition> listAdPosition(@Param("param") ListAdPositionParam param,
                                               @Param("appId") Integer appId,
                                               @Param("deployEnv") String deployEnv);
}
