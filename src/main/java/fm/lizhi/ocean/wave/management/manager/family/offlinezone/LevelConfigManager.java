package fm.lizhi.ocean.wave.management.manager.family.offlinezone;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.management.datastore.dao.family.offlinezone.LevelConfigDao;
import fm.lizhi.ocean.wave.management.datastore.dao.family.offlinezone.LevelRightDao;
import fm.lizhi.ocean.wave.management.model.converter.family.offlinezone.LevelConfigConvert;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.SaveLevelConfigParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.UpdateLevelConfigParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListLevelConfigResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelConfig;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRight;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRightRelation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.ListValuedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 线下专区等级配置管理器
 */
@Component
@Slf4j
public class LevelConfigManager {

    @Autowired
    private LevelConfigConvert levelConfigConvert;

    @Autowired
    private LevelConfigDao levelConfigDao;

    @Autowired
    private LevelRightDao levelRightDao;

    /**
     * 更新等级配置
     *
     * @param param 更新参数
     * @return 更新结果的VO
     */
    public ResultVO<Void> updateLevelConfig(UpdateLevelConfigParam param) {
        Long id = param.getId();
        List<SaveLevelConfigParam.Right> rights = param.getRights();
        OfflineZoneLevelConfig oldLevel = levelConfigDao.getLevelConfigById(id);
        if (oldLevel == null) {
            return ResultVO.failure("等级ID不存在: " + id);
        }
        if (!Objects.equals(oldLevel.getDeployEnv(), ConfigUtils.getEnvRequired().name())) {
            return ResultVO.failure("等级ID不属于当前环境: " + id);
        }
        if (oldLevel.getDeleted()) {
            return ResultVO.failure("等级ID已被删除: " + id);
        }
        List<Long> rightIds = rights.stream().map(SaveLevelConfigParam.Right::getRightId).collect(Collectors.toList());
        Map<Long, OfflineZoneLevelRight> rightsMap = levelRightDao.getLevelRightsMap(rightIds);
        for (Long rightId : rightIds) {
            OfflineZoneLevelRight rightEntity = rightsMap.get(rightId);
            if (rightEntity == null) {
                return ResultVO.failure("权益ID不存在: " + rightId);
            }
            if (rightEntity.getDeleted()) {
                return ResultVO.failure("你选择的权益已被删除: " + rightEntity.getName() + " (ID: " + rightId + ")");
            }
        }
        boolean shouldUpdateRightRelations = shouldUpdateRightRelations(param);
        levelConfigDao.updateLevelConfig(param, shouldUpdateRightRelations);
        return ResultVO.success();
    }

    private boolean shouldUpdateRightRelations(UpdateLevelConfigParam param) {
        Long levelId = param.getId();
        List<SaveLevelConfigParam.Right> newRelations = ListUtils.emptyIfNull(param.getRights());
        List<OfflineZoneLevelRightRelation> oldRelations = levelConfigDao.getLevelRightRelationsByLevelId(levelId);
        if (newRelations.size() != oldRelations.size()) {
            return true;
        }
        int total = oldRelations.size();
        for (int i = 0; i < total; i++) {
            SaveLevelConfigParam.Right newRelation = newRelations.get(i);
            OfflineZoneLevelRightRelation oldRelation = oldRelations.get(i);
            if (!Objects.equals(newRelation.getRightId(), oldRelation.getRightId())
                || !Objects.equals(newRelation.getUnlocked(), oldRelation.getUnlocked())
                || !Objects.equals(newRelation.getUnlockedLevelId(), oldRelation.getUnlockedLevelId())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 列出所有等级配置
     *
     * @return 等级配置列表的VO
     */
    public ResultVO<List<ListLevelConfigResult>> listLevelConfig() {
        List<OfflineZoneLevelConfig> levelConfigs = levelConfigDao.listLevelConfig();
        List<Long> levelIds = levelConfigs.stream().map(OfflineZoneLevelConfig::getId).collect(Collectors.toList());
        ListValuedMap<Long, OfflineZoneLevelRightRelation> levelRightRelationsMap = levelConfigDao.getLevelRightRelationsMapByLevelIds(levelIds);
        List<ListLevelConfigResult> resultList = levelConfigConvert.toListLevelConfigResults(levelConfigs, levelRightRelationsMap);
        return ResultVO.success(resultList);
    }
}
