package fm.lizhi.ocean.wave.management.controller;

import fm.lizhi.ocean.wave.management.manager.SystemManager;
import fm.lizhi.ocean.wave.management.model.param.system.LoginParam;
import fm.lizhi.ocean.wave.management.model.result.system.EnvResult;
import fm.lizhi.ocean.wave.management.model.result.system.UserResult;
import fm.lizhi.ocean.wave.management.model.vo.MenuItemVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SsoFilter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 系统接口控制器.
 * <p><b>注意:</b> 接口路径{@code "/system}不校验请求头{@link SessionExtUtils#APP_ID_HEADER},
 * 因此该控制器下的方法调用{@link SessionExtUtils#getAppId()}方法需要判空.
 */
@RestController
@RequestMapping("/system")
public class SystemController {

    @Autowired
    private SystemManager systemManager;

    /**
     * 登录, 该请求路径不能在单点登录的登录白名单中, 因为必须经过{@link SsoFilter}处理, 但可以在权限白名单中.
     * <p><b>流程如下:</b>
     * <ol>
     *     <li>前端获取单点登录的domain, 比如{@code ssoDomain = "https://lzsso.lizhi.fm"}</li>
     *     <li>前端确定登录后要跳转的前端页面, 比如{@code loginBackUrl = "https://wavemanagement-public.lzpscn1.com/static/index.html"}</li>
     *     <li>前端拼接传递给单点登录的回调URL, 比如{@code ssoBackUrl = "https://wavemanagement-public.lzpscn1.com/server/system/login?loginBackUrl=" + encodeURIComponent(loginBackUrl)}</li>
     *     <li>前端拼接访问单点登录的URL, 规则为{@code ssoDomain + "/login?backUrl=" + encodeURIComponent(ssoBackUrl)}</li>
     *     <li>前端跳转到上一步拼出来的单点登录的登录页面</li>
     *     <li>用户在单点登录的页面输入账号密码或者飞书扫码登录</li>
     *     <li>用户鉴权成功后浏览器跳转到当前接口路径, 会带上{@value SsoFilter#SSO_TOKEN_NAME}</li>
     *     <li>{@link SsoFilter}第一次拦截会拿{@value SsoFilter#SSO_TOKEN_NAME}换token写入cookie, 并移除{@value SsoFilter#SSO_TOKEN_NAME}重新跳转到当前接口路径</li>
     *     <li>{@link SsoFilter}第二次拦截会校验cookie中的token, 并放行到当前接口代码</li>
     *     <li>当前接口代码会返回重定向到传入的{@code loginBackUrl}</li>
     *     <li>前端跳转到登录后的页面</li>
     * </ol>
     *
     * @param param    登录参数
     * @param response HTTP响应, 用于重定向
     * @throws IOException 重定向异常
     */
    @GetMapping("/login")
    public void login(LoginParam param, HttpServletResponse response) throws IOException {
        String loginBackUrl = param.getLoginBackUrl();
        if (StringUtils.isNotBlank(loginBackUrl)) {
            response.sendRedirect(loginBackUrl);
        } else {
            response.sendRedirect("/");
        }
    }

    /**
     * 退出登录回跳接口, 该请求路径必须在单点登录的登出白名单中.
     *
     * @param response HTTP响应, 用于重定向
     * @throws IOException 重定向异常
     */
    @GetMapping("/logoutBack")
    public void logoutBack(HttpServletResponse response) throws IOException {
        response.sendRedirect("/static/index.html");
    }

    /**
     * 获取当前系统环境信息
     *
     * @return 当前系统环境信息
     */
    @GetMapping("/env")
    public ResultVO<EnvResult> env() {
        EnvResult envResult = systemManager.getEnv();
        return ResultVO.success(envResult);
    }

    /**
     * 获取当前系统用户信息
     *
     * @return 当前系统用户信息
     */
    @GetMapping("/user")
    public ResultVO<UserResult> user() {
        UserResult userResult = systemManager.getUser();
        return ResultVO.success(userResult);
    }

    /**
     * 获取菜单列表, 注意, 因为前端开发时间不够, 只获取所有菜单URL去进行匹配, 其中目录名称、顺序和层级结构是前端写死的, 并不采用服务端返回的.
     * 如果后续前端有空做完整的改造, 服务端应该配合修改MenuItemVO为树状嵌套结构, 并按排序值返回, 以实现动态的菜单结构.
     *
     * @return 菜单列表
     */
    @GetMapping("/menu")
    public ResultVO<List<MenuItemVO>> menu() {
        List<MenuItemVO> menuItemVOList = systemManager.getMenuList();
        return ResultVO.success(menuItemVOList);
    }
}
