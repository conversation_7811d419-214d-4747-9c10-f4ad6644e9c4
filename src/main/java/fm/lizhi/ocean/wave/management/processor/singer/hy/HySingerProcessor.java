package fm.lizhi.ocean.wave.management.processor.singer.hy;

import com.google.common.collect.Lists;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.wave.management.config.apollo.singer.SingerConfig;
import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerHallApplyDao;
import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerInfoDao;
import fm.lizhi.ocean.wave.management.manager.singer.SingerPushManager;
import fm.lizhi.ocean.wave.management.processor.singer.ISingerProcessor;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerSingHallApplyRecord;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateOperateReasonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class HySingerProcessor implements ISingerProcessor {

    @Resource
    private SingerInfoDao singerInfoDao;

    @Autowired
    private SingerPushManager singerPushManager;

    @Autowired
    private GuidGenerator guidGenerator;

    @Autowired
    private SingerConfig singerConfig;

    @Autowired
    private SingerHallApplyDao singerHallApplyDao;

    @Override
    public List<Long> getRelatedEliminateSingerRecordIds(List<Long> userIds, int appId, int singerType) {
        //小陪伴的高级歌手淘汰和认证歌手淘汰，都不会影响关联淘汰
        List<SingerStatusEnum> statusList = Lists.newArrayList(SingerStatusEnum.AUTHENTICATING);
        //认证中的高级歌手，需要关联淘汰
        List<SingerInfo> singerInfos = singerInfoDao.getSingerInfoNotInSingerType(BusinessEvnEnum.HEI_YE.getAppId(), userIds, statusList, SingerTypeEnum.NEW);
        return singerInfos.stream().map(SingerInfo::getId).collect(Collectors.toList());
    }

    @Override
    public void reissueDecorateAfterEliminate(int appId, List<Long> singerIds, int eliminateSingerType, String operator) {
        if (eliminateSingerType == SingerTypeEnum.NEW.getType()) {
            //淘汰认证歌手，就没有补发操作
            return;
        }
        //如果是淘汰高级歌手，需要补发认证歌手的装扮,查询出所有的认证歌手
        List<SingerInfo> newSingerList = singerInfoDao.getSingerInfoByUserIds(appId, singerIds, Lists.newArrayList(SingerStatusEnum.EFFECTIVE), SingerTypeEnum.NEW, true);
        if (CollectionUtils.isEmpty(newSingerList)) {
            log.info("reissueDecorateAfterEliminate: no singer need reissue decorate. appId:{}, singerIds:{}", appId, singerIds);
            return;
        }
        singerPushManager.sendSingerPassKafkaMessage(appId, newSingerList, operator,
                SingerDecorateOperateReasonConstant.SINGER_TYPE_CHANGE, guidGenerator.genTransactionId());
        log.info("hy.reissueDecorateAfterEliminate, singerIdsSize:{}, newSingerListSize:{}", singerIds.size(), newSingerList.size());
    }

    @Override
    public boolean isNeedRemoveSingerAfterRemoveWhiteList(int appId, Long njId) {
        if (singerConfig.getHy().isLessRelevanceSwitch()) {
            //如果是弱关联，不需要移除
            return false;
        }

        //强关联的话，还需要判断是否是点唱厅
        List<Integer> statusList = Lists.newArrayList(SingerHallApplyStatusEnum.APPLYED.getStatus(), SingerHallApplyStatusEnum.APPLYING.getStatus());
        List<SingerSingHallApplyRecord> singerSingHallApplyRecords = singerHallApplyDao.batchGetSingerHallApplyRecordList(appId, Lists.newArrayList(njId), statusList);
        log.info("hy.isNeedRemoveSingerAfterRemoveWhiteList, njId={},singerSingHallApplyRecords:{}", njId, singerSingHallApplyRecords);
        return CollectionUtils.isEmpty(singerSingHallApplyRecords);
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }


}
