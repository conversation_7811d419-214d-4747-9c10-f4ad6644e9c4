package fm.lizhi.ocean.wave.management.model.param.activitycenter.ai;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 背景去除参数
 */
@Data
public class RemoveImageBackgroundParam {

    /**
     * 会话id
     */
    @NotNull(message = "会话id不能为空")
    @Size(max = 64, message = "会话id长度不能超过{max}个字符")
    private String sessionId;

    /**
     * 原图URL列表
     */
    @NotNull(message = "原图URL列表不能为空")
    @Size(min = 1, max = 8, message = "原图URL列表长度不能超过{max}个元素")
    private List<String> imageUrls;
}
