package fm.lizhi.ocean.wave.management.model.dto.singer;

import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperatorEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 歌手装扮流水初始化
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SingerDecorateFlowInitParamDTO {

    /**
     * 应用ID
     */
    private int appId;

    /**
     * 需要处理的歌手库列表
     */
    private List<SingerInfo> singerInfoList;

    /**
     * 操作类型 1: 发放 2: 回收
     */
    private SingerDecorateFlowOperateEnum operateType;

    /**
     * 操作人, 不做模糊查询, 如果是 SingerDecorateFlowOperatorEnum#MANUAL(人工)，可直接写入操作人名称
     * @see SingerDecorateFlowOperatorEnum
     */
    private String operator;

    /**
     * 操作原因
     * @see SingerDecorateOperateReasonConstant
     */
    private String reason;
}
