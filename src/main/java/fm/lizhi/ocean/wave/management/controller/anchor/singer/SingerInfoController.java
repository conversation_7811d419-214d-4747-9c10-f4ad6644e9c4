package fm.lizhi.ocean.wave.management.controller.anchor.singer;

import fm.lizhi.ocean.wave.management.manager.singer.SingerInfoManager;
import fm.lizhi.ocean.wave.management.manager.file.FileExportManager;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerInfoConvert;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.*;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.PageSingerInfoResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerDetailInfoExcelVO;
import fm.lizhi.ocean.wave.management.model.vo.file.FileExportVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@RestController
@RequestMapping("/singer/")
@Slf4j
public class SingerInfoController {

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private FileExportManager fileExportManager;


    /**
     * 歌手库-歌手维度列表
     * @param param
     * @return
     */
    @GetMapping("/singerDetail")
    public ResultVO<PageVO<PageSingerInfoResult>> singerDetail(PageSingerInfoParam param) {
        if (param == null) {
            return ResultVO.failure("参数不能为空");
        }
        Integer appId = SessionExtUtils.getAppId();
        return singerInfoManager.singerDetail(param, appId);
    }

    @GetMapping("/singerDetail/export")
    public void singerDetailExport(PageSingerInfoExcelParam param, HttpServletResponse response) {
        FileExportVO<SingerDetailInfoExcelVO> fileExportVO = new FileExportVO<>();
        fileExportVO.setHead(SingerDetailInfoExcelVO.class);
        fileExportVO.setFileName("歌手库-歌手维度列表");
        fileExportVO.setQueryAll(param.isQueryAll());
        fileExportVO.setPageNo(param.isQueryAll() ? 1 : param.getPageNo());
        fileExportVO.setPageSize(param.isQueryAll() ? 500 : param.getPageSize());

        try {
            fileExportManager.exportToHttpResponse(fileExportVO, response, (pageNo, pageSize)-> {
                param.setPageNo(pageNo);
                param.setPageSize(pageSize);
                ResultVO<PageVO<PageSingerInfoResult>> result = singerInfoManager.singerDetail(param, SessionExtUtils.getAppId());
                if (result.isSuccess()) {
                    List<SingerDetailInfoExcelVO> voList = SingerInfoConvert.INSTANCE.convertPageSingerInfoResult(result.getData().getList());
                    return PageVO.of(result.getData().getTotal(), voList);
                }
                return PageVO.of(0, new ArrayList<>());
            });
        } catch (IOException e) {
            log.error("导出歌手库-歌手维度列表失败", e);
            throw new RuntimeException("导出歌手库-歌手维度列表失败");
        }
    }
    /**
     * 淘汰歌手
     * @param param 淘汰歌手参数
     * @return 操作结果
     */
    @PostMapping("/eliminate")
    public ResultVO<Void> eliminateSinger(@Valid @RequestBody EliminateSingerParam param) {
        if (param == null) {
            return ResultVO.failure("参数不能为空");
        }
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        return singerInfoManager.eliminateSinger(param, appId, operator);
    }

    /**
     * 晋升歌手
     * @param param 晋升歌手参数
     * @return 操作结果
     */
    @PostMapping("/upgrade")
    public ResultVO<Void> upgradeSinger(@Valid @RequestBody UpgradeSingerParam param) {
        if (param == null) {
            return ResultVO.failure("参数不能为空");
        }
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        return singerInfoManager.upgradeSinger(param, appId, operator);
    }
    /**
     * 晋升歌手
     * @param param 晋升歌手参数
     * @return 操作结果
     */
    @PostMapping("/import")
    public ResultVO<BatchImportSingerVO> importSinger(@Valid @RequestBody BatchImportSingerParam param) {
        if (param == null) {
            return ResultVO.failure("参数不能为空");
        }
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        return singerInfoManager.importSinger(param, appId, operator);
    }
}
