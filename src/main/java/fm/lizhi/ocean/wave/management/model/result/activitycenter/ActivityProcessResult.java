package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * @description: 活动环节信息
 * @author: guoyibin
 * @create: 2024/10/23 21:24
 */
@Data
public class ActivityProcessResult {

    /**
     * 环节ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 环节名称
     */
    private String name;

    /**
     * 环节时长
     */
    private String duration;

    /**
     * 环节说明
     */
    private String explanation;
}
