package fm.lizhi.ocean.wave.management.util;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;

/**
 * RPC调用结果工具类
 */
public class ResultUtils {

    /**
     * 将给定的目标对象转换为成功的RPC调用结果
     *
     * @param target 目标对象
     * @param <T>    RPC调用结果的数据类型
     * @return 成功的RPC调用结果
     */
    public static <T> Result<T> success(T target) {
        Result<T> result = new Result<>();
        result.setRCode(GeneralRCode.GENERAL_RCODE_SUCCESS);
        result.setTarget(target);
        return result;
    }

    /**
     * 将给定的异常对象转换为失败的RPC调用结果
     *
     * @param e   异常对象
     * @param <T> RPC调用结果的数据类型
     * @return 失败的RPC调用结果
     */
    public static <T> Result<T> failure(Throwable e) {
        Result<T> result = new Result<>();
        result.setRCode(GeneralRCode.GENERAL_RCODE_UNKNOWN_ERROR);
        result.setMessage(e.toString());
        return result;
    }

    /**
     * 将给定的错误码和消息转换为失败的RPC调用结果
     *
     * @param rCode   错误码
     * @param message 消息
     * @param <T>     RPC调用结果的数据类型
     * @return 失败的RPC调用结果
     */
    public static <T> Result<T> failure(int rCode, String message) {
        Result<T> result = new Result<>();
        result.setRCode(rCode);
        result.setMessage(message);
        return result;
    }

    /**
     * 将给定的RPC调用结果转换为失败的RPC调用结果, 用于泛型转换
     *
     * @param result RPC调用结果
     * @param <S>    原始RPC调用结果的数据类型
     * @param <T>    新的RPC调用结果的数据类型
     * @return 失败的RPC调用结果
     */
    public static <S, T> Result<T> failure(Result<S> result) {
        Result<T> newResult = new Result<>();
        newResult.setRCode(result.rCode());
        newResult.setMessage(result.getMessage());
        return newResult;
    }

    /**
     * 判断给定的RPC调用结果是否成功
     *
     * @param result RPC调用结果
     * @param <T>    RPC调用结果的数据类型
     * @return 是否成功
     */
    public static <T> boolean isSuccess(Result<T> result) {
        return result != null && result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }

    /**
     * 判断给定的RPC调用结果是否失败
     *
     * @param result RPC调用结果
     * @param <T>    RPC调用结果的数据类型
     * @return 是否失败
     */
    public static <T> boolean isFailure(Result<T> result) {
        return !isSuccess(result);
    }
}
