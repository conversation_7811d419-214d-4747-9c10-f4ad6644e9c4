package fm.lizhi.ocean.wave.management.remote.service.activitycenter;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestQueryUserActivitiesBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityInfoDetail;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityApplyService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseBatchActivityAuditData;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseCancelActivity;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityAdminOperateService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityInfoQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 活动申请接口远程调用
 */
@Component
@Slf4j
public class ActivityApplyServiceRemote {

    @Autowired
    private ActivityApplyService activityApplyService;

    @Autowired
    private ActivityAdminOperateService activityAdminOperateService;

    @Autowired
    private ActivityInfoQueryService activityInfoQueryService;;

    /**
     * 驳回申请
     *
     * @param param 参数
     * @return 结果
     */
    public Result<Void> rejectActivityApply(RequestActivityAuditReject param) {
        Result<Void> result = activityAdminOperateService.rejectActivityApply(param);
        log.info("rejectActivityApply resultCode: {},activityId:{}", result.rCode(), param.getActivityId());
        return result;
    }

    public Result<ResponseQueryUserActivitiesBean> queryUserActivities(RequestQueryUserActivitiesBean requestQueryUserActivitiesBean) {
        return activityInfoQueryService.queryUserActivities(requestQueryUserActivitiesBean);
    }

    public Result<ResponseActivityInfoDetail> queryUserActivityDetail(Integer appId, Long activityId) {
        return activityApplyService.queryActivityInfoDetail(activityId, appId);
    }


    public Result<String> agreeActivityApply(RequestActivityAuditAgree requestActivityAuditAgreeBean) {
        return activityAdminOperateService.agreeActivityApply(requestActivityAuditAgreeBean);
    }

    /**
     * 活动提报, 该方法主要用于运营后台添加活动
     *
     * @param requestActivityApplyBean 活动提报参数
     * @return 结果
     */
    public Result<Void> activityApply(RequestActivityApplyBean requestActivityApplyBean) {
        log.info("activityApply requestActivityApplyBean: {}", requestActivityApplyBean);
        Result<Void> result = activityApplyService.activityApply(requestActivityApplyBean);
        if (ResultUtils.isSuccess(result)) {
            log.info("activityApply success");
        } else {
            log.info("activityApply failed. rCode: {}, message: {}", result.rCode(), result.getMessage());
        }
        return result;
    }

    /**
     * 查询活动列表
     *
     * @param requestQueryActivityListBean 查询参数
     * @return 结果
     */
    public Result<ResponseQueryActivityListBean> queryActivityList(RequestQueryActivityListBean requestQueryActivityListBean) {
        log.info("queryActivityList requestQueryActivityListBean: {}", requestQueryActivityListBean);
        Result<ResponseQueryActivityListBean> result = activityApplyService.queryActivityList(requestQueryActivityListBean);
        if (ResultUtils.isSuccess(result)) {
            log.info("queryActivityList success");
        } else {
            log.info("queryActivityList failed. rCode: {}, size: {}", result.rCode(), CollectionUtils.size(result.target().getList()));
        }
        return result;
    }
    public Result<ResponseCancelActivity> cancelActivity(RequestCancelActivity requestActivityCancelBean) {
        return activityAdminOperateService.adminCancelActivity(requestActivityCancelBean);
    }

    public Result<ResponseActivityModifyBean> modifyActivityApply(RequestActivityModifyBean requestModifyActivityApplyBean) {
        return activityAdminOperateService.adminModifyActivity(requestModifyActivityApplyBean);
    }

    /**
     * 批量同意活动申请
     *
     * @param requests 批量请求参数
     * @return 结果
     */
    public Result<ResponseBatchActivityAuditData> batchAgreeActivityApply(RequestBatchActivityAuditAgree requests) {
        return activityAdminOperateService.batchAgreeActivityApply(requests);
    }

    /**
     * 批量拒绝活动申请
     *
     * @param requests 批量请求参数
     * @return 结果
     */
    public Result<ResponseBatchActivityAuditData> batchRejectActivityApply(RequestBatchActivityAuditReject requests) {
        return activityAdminOperateService.batchRejectActivityApply(requests);
    }
}
