package fm.lizhi.ocean.wave.management.manager.singer;

import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerInfoConvert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerHallInfoConvert;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.SingerHallInfoParam;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.SingerHallInfoResult;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.SingerHallSummaryInfoResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.remote.service.anchor.singer.SingerHallInfoRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetAllSingerStatics;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerRoomDetails;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class SingerHallInfoManager {

    @Autowired
    private SingerHallInfoRemote singerHallInfoRemote;

    public ResultVO<SingerHallSummaryInfoResult> getSingerHallSummaryInfo(long njId, int appId) {
        Result<ResponseGetAllSingerStatics> result = singerHallInfoRemote.getSingerHallSummaryInfo(njId, appId);
        if (ResultUtils.isFailure(result)) {
            log.error("获取歌手厅统计信息失败, njId: {}, appId: {}, result: {}", njId, appId, result);
            return ResultVO.failure(result);
        }

        ResponseGetAllSingerStatics target = result.target();
        return ResultVO.success(SingerInfoConvert.INSTANCE.convertSingerHallSummaryInfoResult(target));
    }

    public ResultVO<PageVO<SingerHallInfoResult>> getSingerHallInfo(SingerHallInfoParam param, int appId) {
        Result<PageBean<ResponseSingerRoomDetails>> result = singerHallInfoRemote.getSingerHallInfo(param, appId);
        if (ResultUtils.isFailure(result)) {
            log.error("获取歌手厅信息失败, param: {}, appId: {}, result: {}", param, appId, result);
            return ResultVO.failure(result);
        }

        return ResultVO.success(SingerHallInfoConvert.INSTANCE.pageBean2PageVO(result.target()));
    }


}
