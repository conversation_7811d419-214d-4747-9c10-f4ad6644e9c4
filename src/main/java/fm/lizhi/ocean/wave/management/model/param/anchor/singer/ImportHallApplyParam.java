package fm.lizhi.ocean.wave.management.model.param.anchor.singer;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ImportHallApplyParam {
    /**
     * 厅ID 列表
     */
    @NotNull(message = "厅ID列表不能为空")
    private List<Long> njIds;

    /**
     * 审核状态
     */
    @NotNull(message = "审核状态不能为空")
    private Integer status;
}