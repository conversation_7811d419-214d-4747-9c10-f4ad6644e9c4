package fm.lizhi.ocean.wave.management.model.param.anchor.singer;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
public class BatchImportSingerVO {


    private List<FailImportSingerInfo> failImportSingerInfoList;


    @Data
    @Accessors(chain = true)
    public static class FailImportSingerInfo {

        private String band;

        private Long singerId;

        private String songStyle;

        private Boolean originalSinger;

        private Integer singerType;

        private String failReason;

    }

}
