package fm.lizhi.ocean.wave.management.common.web;

import fm.lizhi.sso.client.SessionUtils;
import fm.lizhi.sso.client.TokenUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.lang.NonNull;

import java.io.IOException;

/**
 * 自动透传SSO token的拦截器
 */
public class RequestAuthenticationInterceptor implements ClientHttpRequestInterceptor {

    @NonNull
    @Override
    public ClientHttpResponse intercept(@NonNull HttpRequest request, @NonNull byte[] body, @NonNull ClientHttpRequestExecution execution) throws IOException {
        String token = SessionUtils.getToken();
        if (StringUtils.isNotBlank(token)) {
            HttpHeaders headers = request.getHeaders();
            headers.add(TokenUtils.X_SSO_AUTHORIZATION, token);
        }
        return execution.execute(request, body);
    }
}
