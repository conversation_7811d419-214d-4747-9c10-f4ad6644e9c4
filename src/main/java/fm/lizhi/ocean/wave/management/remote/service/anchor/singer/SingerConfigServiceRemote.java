package fm.lizhi.ocean.wave.management.remote.service.anchor.singer;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerConfigConvert;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.AddChatSenceConfigParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.ModifyChatSenceConfigParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.UpdateAuditConfigParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.SaveApplyMenuConfigParam;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.SingerChatSenceConfigResult;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSaveApplyMenuConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestUpdateSingerAuditConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.*;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerConfigService;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerChatSceneBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerChatScene;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class SingerConfigServiceRemote {

    @Autowired
    private SingerConfigService singerConfigService;

    /**
     * 获取歌手枚举配置
     *
     * @param appId
     * @return 歌手枚举配置
     */
    public Result<ResponseSingerEnumerateConfig> getEnumerateConfig(Integer appId) {
        return singerConfigService.getEnumerateConfig(appId);
    }

    public Result<Void> updateAuditConfig(UpdateAuditConfigParam param, String operator) {
        RequestUpdateSingerAuditConfig request = SingerConfigConvert.INSTANCE.param2Request(param);
        request.setOperator(operator);
        Result<Void> result = singerConfigService.updateSingerAuditConfig(request);
        log.info("updateSingerAuditConfig code: {}, param={}", result.rCode(), JsonUtil.dumps(param));
        return result;
    }

    public Result<ResponseSingerAuditConfig> getSingerAuditConfig(int appId) {
        Result<ResponseSingerAuditConfig> result = singerConfigService.getSingerPreAuditConfig(appId);
        log.info("getSingerAuditConfig code: {}, appId={}", result.rCode(), appId);
        return result;
    }

    public Result<Void> addChatSenceConfig(AddChatSenceConfigParam param, Integer appId) {
        List<SingerChatSceneBean> sceneBeans = SingerConfigConvert.INSTANCE.chatSenceVO2Request(param.getConfigList());
        RequestSingerChatScene request = new RequestSingerChatScene();
        request.setAppId(appId);
        request.setSceneChatList(sceneBeans);
        Result<Void> result = singerConfigService.addSingerChatScene(request);
        log.info("addChatSenceConfig code: {}, param={}", result.rCode(), JsonUtil.dumps(param));
        return result;
    }

    public Result<Void> deleteChatSenceConfig(String sceneCode, Integer appId) {
        Result<Void> result = singerConfigService.deleteSingerChatScene(appId, sceneCode);
        log.info("deleteChatSenceConfig code: {}, sceneCode={}", result.rCode(), sceneCode);
        return result;
    }

    public Result<List<ResponseSingerChatScene>> getChatSenceConfigs(Integer appId, String sceneCode,
                                                                     Integer singerType) {
        Result<List<ResponseSingerChatScene>> result = singerConfigService.getSingerChatScene(appId, singerType, sceneCode);
        log.info("getChatSenceConfigs code: {}, appId={}, sceneCode={}, singerType={}", result.rCode(), appId, sceneCode, singerType);
        return result;
    }

    /**
     * 获取歌曲曲风数量配置
     *
     * @param appId      应用ID
     * @return 歌曲曲风数量配置
     */
    public Result<ResponseSongStyleConfig> getSongStyleConfig(Integer appId) {
        Result<ResponseSongStyleConfig> result = singerConfigService.getSongStyleConfig(appId);
        log.info("getApplyMenuConfig code: {}, appId={}", result.rCode(), appId);
        return result;
    }

}
