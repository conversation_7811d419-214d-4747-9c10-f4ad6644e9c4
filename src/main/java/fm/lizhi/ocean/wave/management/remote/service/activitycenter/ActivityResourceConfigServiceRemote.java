package fm.lizhi.ocean.wave.management.remote.service.activitycenter;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestPageActivityResources;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityResourceConfigService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityResourceConfigServiceRemote {

    @Autowired
    private ActivityResourceConfigService activityResourceConfigService;


    public Result<Void> save(RequestSaveActivityResource param){
        Result<Void> result = activityResourceConfigService.saveActivityResource(param);
        if (ResultUtils.isFailure(result)){
            log.warn("save activity resource failed, rCode: {}", result.rCode());
        }

        return result;
    }

    public Result<Void> update(RequestUpdateActivityResource param){
        Result<Void> result = activityResourceConfigService.updateActivityResource(param);
        if (ResultUtils.isFailure(result)){
            log.warn("update activity resource failed, rCode: {}", result.rCode());
        }
        return result;
    }

    public Result<Void> delete(Long id, Integer appId, String operator){
        Result<Void> result = activityResourceConfigService.deleteActivityResource(id, appId, operator);
        if (ResultUtils.isFailure(result)){
            log.warn("delete activity resource failed, rCode: {}", result.rCode());
        }
        return result;
    }

    public Result<PageBean<ResponseActivityResource>> list(RequestPageActivityResources param){
        Result<PageBean<ResponseActivityResource>> result = activityResourceConfigService.listActivityResource(param);
        if (ResultUtils.isFailure(result)){
            log.warn("list activity resource failed, rCode: {}", result.rCode());
        }
        return result;
    }

    public Result<List<ResponseActivityResource>> listActivityResourceByLevelId(Integer appId, Long levelId){
        Result<List<ResponseActivityResource>> result = activityResourceConfigService.listActivityResourceByLevelId(levelId, appId);
        if (ResultUtils.isFailure(result)){
            log.warn("list activity resource failed, appId:{}, levelId:{}, rCode: {}",appId, levelId, result.rCode());
        }

        return result;
    }
}
