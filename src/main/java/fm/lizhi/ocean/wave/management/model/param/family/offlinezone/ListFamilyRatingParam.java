package fm.lizhi.ocean.wave.management.model.param.family.offlinezone;

import lombok.Data;

/**
 * 查询公会评级列表参数
 * <AUTHOR>
 */
@Data
public class ListFamilyRatingParam {

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 公会ID，精确查询
     */
    private Long familyId;

    /**
     * 公会名称，模糊查询
     */
    private String familyName;

    /**
     * 等级ID
     */
    private Integer levelId;

    // 分页参数
    private Integer pageNo = 1;

    private Integer pageSize = 10;

}