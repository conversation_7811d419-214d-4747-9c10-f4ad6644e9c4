package fm.lizhi.ocean.wave.management.remote.service.grow;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigAwardBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestDeleteFamilyLevelConfig;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestGetFamilyLevelConfigList;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestSaveFamilyLevelConfig;
import fm.lizhi.ocean.wavecenter.api.grow.level.service.FamilyLevelConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/18 20:53
 */
@Component
public class FamilyLevelConfigServiceRemote{

    @Autowired
    private FamilyLevelConfigService familyLevelConfigService;

    public Result<Void> save(RequestSaveFamilyLevelConfig request) {
        return familyLevelConfigService.save(request);
    }

    public Result<Void> delete(RequestDeleteFamilyLevelConfig request) {
        return familyLevelConfigService.delete(request);
    }

    public Result<List<FamilyLevelConfigAwardBean>> list(RequestGetFamilyLevelConfigList request) {
        return familyLevelConfigService.list(request);
    }
}
