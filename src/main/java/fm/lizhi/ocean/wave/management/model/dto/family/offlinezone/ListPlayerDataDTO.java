package fm.lizhi.ocean.wave.management.model.dto.family.offlinezone;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * 查询线下主播数据列表结果
 * <AUTHOR>
 */
@Data
public class ListPlayerDataDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;


    /**
     * 主播ID
     */
    private Long userId;

    /**
     * 主播名称
     */
    private String userName;

    /**
     * 实名证件号码
     */
    private String idCardNumber;

    /**
     * 实名姓名
     */
    private String idName;

    /**
     * 公会ID
     */
    private Long familyId;

    /**
     * 公会名称
     */
    private String familyName;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 厅主名称
     */
    private String njName;

    /**
     * 签约时间
     */
    private Date beginSignTime;

    /**
     * 主播分类：0 线下，1 线上
     */
    private Integer category;

    // 保护协议相关字段
    /**
     * 保护协议ID
     */
    private Long protectionId;

    /**
     * 协议状态
     * @see fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.ProtectionStatusEnums
     */
    private Integer protectionStatus;

    /**
     * 是否受保护：true受保护，false未受保护
     */
    private Boolean protection;

    /**
     * 协议开始时间
     */
    private Date agreementStartTime;

    /**
     * 协议结束时间
     */
    private Date agreementEndTime;

}