package fm.lizhi.ocean.wave.management.controller;

import fm.lizhi.ocean.wave.management.manager.ActivityToolsConfigManager;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityToolParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityToolParam;
import fm.lizhi.ocean.wave.management.model.vo.ActivityToolsInfoVo;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityClassConfigBean;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 活动玩法工具配置
 * <AUTHOR>
 */
@RestController
@RequestMapping("/activity/tools")
@Slf4j
public class ActivityToolsConfigController {

    @Autowired
    private ActivityToolsConfigManager activityToolsConfigManager;


    /**
     * 保存活动玩法工具
     */
    @PostMapping("/save")
    public ResultVO<Void> save(@RequestBody @Valid SaveActivityToolParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("save activity tools config, param: {}, operator: {}", param, operator);
        return activityToolsConfigManager.save(param, appId, operator);
    }

    /**
     * 更新活动玩法工具
     */
    @PostMapping("/update")
    public ResultVO<Void> update(@RequestBody @Valid UpdateActivityToolParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("update activity tools config, param: {}, operator: {}", param, operator);
        return activityToolsConfigManager.update(param, appId, operator);
    }

    /**
     * 查询活动玩法工具列表
     */
    @GetMapping("/list")
    public ResultVO<List<ActivityToolsInfoVo>> list() {
        Integer appId = SessionExtUtils.getAppId();
        return activityToolsConfigManager.list(appId);

    }

}
