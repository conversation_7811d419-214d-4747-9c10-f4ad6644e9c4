package fm.lizhi.ocean.wave.management.model.vo.permission;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/20 15:02
 */
@Data
public class PermissionVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private String permissionName;

    private String permissionCode;

    /**
     * 权限类型：1=操作权限  2=页面模块权限
     */
    private Integer permissionType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
