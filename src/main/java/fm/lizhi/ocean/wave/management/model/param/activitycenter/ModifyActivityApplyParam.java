package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityProcessBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum;
import lombok.Data;

@Data
public class ModifyActivityApplyParam {

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 业务定义不超过10个字，稍微冗余
     */
    @NotNull(message = "活动主题不能为空")
    private String name;

    /**
     * 活动开始时间
     */
    @NotNull(message = "活动开始时间不能为空")
    private Long startTime;

    /**
     * 活动结束时间
     */
    @NotNull(message = "活动结束时间不能为空")
    private Long endTime;

    /**
     * 申请类型，1：自主提报，2: 官方活动
     */
    private int applyType;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 申请者uid
     */
    @NotNull(message = "请登录后再提起申报")
    private Long applicantUid;

    /**
     * 联系方式
     */
    private String contactNumber;

    /**
     * 主持人ID
     */
    private Long hostId;

    /**
     * 陪档主播ID列表
     */
    private List<Long> accompanyNjIds;

    /**
     * 活动目标，不超过100字
     */
    private String goal;

    /**
     * 活动介绍，不超过100字
     */
    @NotNull(message = "活动介绍不能为空")
    private String introduction;

    /**
     * 活动辅助道具图片地址，多个逗号分隔
     */
    @Valid
    private List<String> auxiliaryPropUrls;

    /**
     * 玩法工具，1:魔法团战，2：跨房PK，3：投票，4：全麦PK
     */
    @Valid
    private List<Integer> activityTools;

    /**
     * 房间公告，不超过500字
     */
    private String roomAnnouncement;

    /**
     * 房间公告图片，不超过3个
     */
    private List<String> roomAnnouncementImages;

    /**
     * 房间背景ID
     */
    @Deprecated
    private Long roomBackgroundId;

    /**
     * 房间背景ID列表
     */
    private List<Long> roomBackgroundIds;

    /**
     * 头像框ID
     */
    @Deprecated
    private Long avatarWidgetId;

    /**
     * 头像框ID列表
     */
    private List<Long> avatarWidgetIds;

    /**
     * 活动环节列表
     */
    @Valid
    private List<ActivityProcessBean> processes;

    /**
     * 最大官频位数量
     */
    private Integer maxSeatCount = 0;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 活动海报图片地址
     */
    private String posterUrl;

    /**
     * 流量资源列表
     */
    @Valid
    private List<ActivityFlowResourceBean> flowResources;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 分类id
     */
    private Long classId;

    /**
     * 活动状态，1：待审批，2：审批通过，3：审批不通过
     */
    private Integer auditStatus;
}
