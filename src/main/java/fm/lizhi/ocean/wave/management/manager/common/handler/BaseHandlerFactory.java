package fm.lizhi.ocean.wave.management.manager.common.handler;

import javax.annotation.PostConstruct;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 基础处理器工厂
 * <AUTHOR>
 * @param <T>
 * @param <H>
 */
public abstract class BaseHandlerFactory<T, H> implements HandlerFactory<T, H> {

    protected final Map<T, H> HANDLERS = new LinkedHashMap<>();

    /**
     * 注册所有handler
     */
    @PostConstruct
    public abstract void registerAllHandlers();

    /**
     * 注册handler
     *
     * @param type    类型
     * @param handler 处理器
     */
    protected void registerHandler(T type, H handler) {
        HANDLERS.put(type, handler);
    }

    @Override
    public H getHandler(T type) {
        return HANDLERS.get(type);
    }

}
