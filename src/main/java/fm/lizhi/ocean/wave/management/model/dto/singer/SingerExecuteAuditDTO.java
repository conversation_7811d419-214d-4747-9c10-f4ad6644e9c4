package fm.lizhi.ocean.wave.management.model.dto.singer;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SingerExecuteAuditDTO {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 失败原因
     */
    private String reason;

    public static SingerExecuteAuditDTO success() {
        return new SingerExecuteAuditDTO(true, "");
    }

    /**
     * 失败
     */
    public static SingerExecuteAuditDTO failure(String reason) {
        return new SingerExecuteAuditDTO(false, reason);
    }
}
