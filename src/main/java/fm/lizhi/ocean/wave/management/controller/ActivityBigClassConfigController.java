package fm.lizhi.ocean.wave.management.controller;

import fm.lizhi.ocean.wave.management.manager.ActivityClassificationConfigManager;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.DeleteBigClassParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveBigClassParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateBigClassParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityBigClassResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 活动大类配置
 * <AUTHOR>
 */
@RestController
@RequestMapping("/activity/bigClass")
@Slf4j
public class ActivityBigClassConfigController {

    @Autowired
    private ActivityClassificationConfigManager activityClassificationConfigManager;

    /**
     * 保存活动大类
     */
    @PostMapping("/save")
    public ResultVO<Void> save(@RequestBody SaveBigClassParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("save activity big param: {}, appId:{}, operator={}", param, appId, operator);
        return activityClassificationConfigManager.saveBigClass(param, appId, operator);
    }

    /**
     * 更新活动大类
     */
    @PostMapping("/update")
    public ResultVO<Void> update(@RequestBody UpdateBigClassParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("update activity big param: {}, appId:{}, operator:{}", param, appId, operator);
        return activityClassificationConfigManager.updateBigClass(param, operator, appId);
    }


    /**
     * 删除活动大类
     */
    @PostMapping("/delete")
    public ResultVO<Void> delete(@RequestBody DeleteBigClassParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("delete activity big class. id:{}, appId:{}, operator:{}",param.getId(), appId, operator);

        return activityClassificationConfigManager.deleteBigClass(param.getId(), appId, operator);
    }

    /**
     * 列表
     */
    @GetMapping("/list")
    public ResultVO<List<ActivityBigClassResult>> list() {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("list activity big class. appId: {}, operator:{}", appId, operator);

        return activityClassificationConfigManager.getBigClassList(appId);

    }


}
