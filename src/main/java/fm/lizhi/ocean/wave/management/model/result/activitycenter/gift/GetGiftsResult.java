package fm.lizhi.ocean.wave.management.model.result.activitycenter.gift;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 批量查询礼物的结果
 */
@Data
public class GetGiftsResult {

    /**
     * 礼物id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 礼物名称
     */
    private String name;

    /**
     * 礼物封面
     */
    private String cover;
}
