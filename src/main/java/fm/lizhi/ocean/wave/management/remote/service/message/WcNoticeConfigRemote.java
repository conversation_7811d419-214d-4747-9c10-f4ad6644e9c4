package fm.lizhi.ocean.wave.management.remote.service.message;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.message.WcNoticeConfigConvert;
import fm.lizhi.ocean.wave.management.model.param.message.WcNoticeConfigParam;
import fm.lizhi.ocean.wave.management.model.param.message.WcNoticeConfigQueryParam;
import fm.lizhi.ocean.wave.management.model.param.message.WcNoticeUpdateStatusParam;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestWcNoticeConfigPage;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestWcNoticeConfigSave;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestWcNoticeUpdateStatus;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseWcNoticeConfigPage;
import fm.lizhi.ocean.wavecenter.api.message.service.WcNoticeConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Remote层：公告配置删除远程服务
 * 封装对WcNoticeConfigService.deleteWcNoticeConfigById的远程调用
 *
 * <AUTHOR>
 */
@Component
public class WcNoticeConfigRemote {

    @Autowired
    private WcNoticeConfigService wcNoticeConfigService;

    /**
     * 保存公告配置
     *
     * @param dto 公告配置参数
     * @return 下游接口返回的ResultDTO
     */
    public Result<Void> saveWcNoticeConfig(WcNoticeConfigParam dto, String operator, Integer appId) {
        RequestWcNoticeConfigSave requestSaveConfig = WcNoticeConfigConvert.I.toRequestSaveConfig(dto, appId, operator);
        return wcNoticeConfigService.saveWcNoticeConfig(requestSaveConfig);
    }

    /**
     * 分页查询公告配置
     *
     * @param param 查询参数
     * @return 分页结果
     */
    public Result<ResponseWcNoticeConfigPage> queryWcNoticeConfigPage(WcNoticeConfigQueryParam param, Integer appId) {
        RequestWcNoticeConfigPage request = WcNoticeConfigConvert.I.toRequestQueryConfig(param, appId);
        return wcNoticeConfigService.queryWcNoticeConfigPage(request);
    }

    /**
     * 删除公告配置
     *
     * @param id 公告配置ID
     * @return 删除结果，Result<Void>
     */
    public Result<Void> deleteWcNoticeConfigById(Long id) {
        return wcNoticeConfigService.deleteWcNoticeConfigById(id);
    }

    /**
     * 上下架
     *
     * @param dto 参数
     * @return 下游接口返回的ResultDTO
     */
    public Result<Void> updateNoticeStatus(WcNoticeUpdateStatusParam dto, String operator, Integer appId) {
        RequestWcNoticeUpdateStatus requestUpdateStatus = WcNoticeConfigConvert.I.toRequestUpdateStatus(dto, appId, operator);
        return wcNoticeConfigService.updateWcNoticeStatus(requestUpdateStatus);
    }
} 