package fm.lizhi.ocean.wave.management.controller.anchor.singer;

import fm.lizhi.ocean.wave.management.manager.singer.SingerVerifyManager;
import fm.lizhi.ocean.wave.management.manager.file.FileExportManager;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerVerifyConvert;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.*;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.*;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerVerifyRecordExcelVO;
import fm.lizhi.ocean.wave.management.model.vo.file.FileExportVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@RestController
@RequestMapping("/singer")
@Slf4j
public class SingerVerifyController {

    @Autowired
    private SingerVerifyManager singerVerifyManager;

    @Autowired
    private FileExportManager fileExportManager;

    @GetMapping("/apply/list")
    public ResultVO<PageVO<SingerVerifyRecordForQueryResult>> getApplyList(@Valid GetApplyListParam param) {
        return ResultVO.success(singerVerifyManager.getApplyList(param, SessionExtUtils.getAppId()));
    }

    @GetMapping("/apply/list/export")
    public void exportApplyList(@Valid GetApplyListExcelParam param, HttpServletResponse response) {
        try {
            FileExportVO<SingerVerifyRecordExcelVO> fileExportVO = new FileExportVO<SingerVerifyRecordExcelVO>();
            fileExportVO.setHead(SingerVerifyRecordExcelVO.class);
            fileExportVO.setFileName("歌手申请列表");
            fileExportVO.setQueryAll(param.isQueryAll());
            fileExportVO.setPageNo(param.isQueryAll() ? 1 : param.getPageNo());
            fileExportVO.setPageSize(param.isQueryAll() ? 500 : param.getPageSize());

            fileExportManager.exportToHttpResponse(fileExportVO, response, (pageNo, pageSize) -> {
                param.setPageNo(pageNo);
                param.setPageSize(pageSize);
                PageVO<SingerVerifyRecordResult> pageVO = singerVerifyManager.getApplyListForExport(param, SessionExtUtils.getAppId());
                List<SingerVerifyRecordExcelVO> voList = SingerVerifyConvert.INSTANCE.singerVerifyRecordResult2ExcelVO(pageVO.getList());
                return PageVO.of(pageVO.getTotal(), voList);
            });
        } catch (IOException e) {
            log.error("导出歌手申请列表失败", e);
            throw new RuntimeException("导出歌手申请列表失败");
        }
    }

    @PostMapping("/updateRemark")
    public ResultVO<Void> updateRemark(@RequestBody @Valid UpdateRemarkParam param) {
        boolean result = singerVerifyManager.updateRemark(param, SessionExtUtils.getAppId());
        return result ? ResultVO.success() : ResultVO.failure("更新失败");
    }

    @PostMapping("/verify/audit")
    public ResultVO<SingerVerifyAuditResult> auditVerify(@RequestBody SingerVerifyAuditParam param) {
        param.setAppId(SessionExtUtils.getAppId());
        param.setOperator(SessionUtils.getAccount());
        return singerVerifyManager.auditVerify(param);
    }

    @GetMapping("/apply/history")
    public ResultVO<SingerVerifyHistoryRecordResult> queryHistoryVerifyRecord(@Valid QueryHistoryVerifyRecordParam param) {
        param.setAppId(SessionExtUtils.getAppId());
        return singerVerifyManager.queryHistoryVerifyRecord(param);
    }

    @PostMapping("/addBlackList")
    public ResultVO<Void> addBlackList(@RequestBody AddBlackListParam param) {
        if (CollectionUtils.isEmpty(param.getUserIds())) {
            return ResultVO.failure("用户ID不能为空");
        }
        return singerVerifyManager.batchBlackList(SessionExtUtils.getAppId(), param.getUserIds());
    }

    @PostMapping("/cancelBlackList")
    public ResultVO<SingerCancelBlackListResult> cancelBlackList(@RequestBody CancelBlackListParam param) {
        if (CollectionUtils.isEmpty(param.getUserIds())) {
            return ResultVO.failure("用户ID不能为空");
        }
        return singerVerifyManager.batchCancelBlackList(SessionExtUtils.getAppId(), param.getUserIds());
    }

}
