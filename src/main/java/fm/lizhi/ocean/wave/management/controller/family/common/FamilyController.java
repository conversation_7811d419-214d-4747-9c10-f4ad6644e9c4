package fm.lizhi.ocean.wave.management.controller.family.common;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.family.common.FamilyConvert;
import fm.lizhi.ocean.wave.management.model.param.family.common.BatchSearchFamilyParam;
import fm.lizhi.ocean.wave.management.model.param.family.common.SearchFamilyParam;
import fm.lizhi.ocean.wave.management.model.result.family.common.SearchFamilyResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.family.FamilyUserVO;
import fm.lizhi.ocean.wave.management.util.JsonUtils;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyUserBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.PageSearchFamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.request.RequestGetFamilyUserByIds;
import fm.lizhi.ocean.wavecenter.api.user.request.RequestPageSearchFamily;
import fm.lizhi.ocean.wavecenter.api.user.service.UserFamilyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公会控制器
 */
@RestController
@RequestMapping("/family")
@Slf4j
public class FamilyController {

    @Autowired
    private FamilyConvert familyConvert;

    @Autowired
    private UserFamilyService userFamilyService;

    /**
     * 分页查询公会
     *
     * @param param 查询参数
     * @return 分页查询结果的VO
     */
    @GetMapping("/search")
    public ResultVO<PageVO<SearchFamilyResult>> searchFamily(@Validated SearchFamilyParam param) {
        RequestPageSearchFamily request = familyConvert.toRequestPageSearchFamily(param);
        Result<PageBean<PageSearchFamilyBean>> result = userFamilyService.pageSearchFamily(request);
        if (ResultUtils.isFailure(result)) {
            log.error("pageSearchFamily error, request={}, rCode={}", JsonUtils.toJsonString(param), result.rCode());
            return ResultVO.failure("查询公会失败，请稍后重试");
        }
        PageBean<PageSearchFamilyBean> pageBean = result.target();
        int total = pageBean.getTotal();
        List<PageSearchFamilyBean> beans = pageBean.getList();
        List<SearchFamilyResult> resultList = familyConvert.toSearchFamilyResults(beans);
        return ResultVO.success(PageVO.of(total, resultList));
    }

    @GetMapping("/batchSearchByFamilyId")
    public ResultVO<List<FamilyUserVO>> batchSearchByFamilyId(@Validated BatchSearchFamilyParam param) {
        Integer appId = SessionExtUtils.getAppId();

        Result<List<FamilyUserBean>> result = userFamilyService.getFamilyUserByFamilyIds(new RequestGetFamilyUserByIds()
                .setFamilyIds(param.getFamilyIdList())
                .setAppId(appId)
        );

        if (ResultUtils.isFailure(result)) {
            log.error("getFamilyUserByFamilyIds error, familyIdList={}, appId={}, rCode={}", param.getFamilyIdList(), appId, result.rCode());
            return ResultVO.failure("查询公会失败，请稍后重试");
        }

        List<FamilyUserBean> familyUserBeans = result.target();
        List<FamilyUserVO> resultList = familyConvert.toFamilyUserVOs(familyUserBeans);
        return ResultVO.success(resultList);
    }
}
