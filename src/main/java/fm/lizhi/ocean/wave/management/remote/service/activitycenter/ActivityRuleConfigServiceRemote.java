package fm.lizhi.ocean.wave.management.remote.service.activitycenter;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRule;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRule;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityRuleConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityRuleConfigServiceRemote {

    @Autowired
    private ActivityRuleConfigService activityRuleConfigService;


    public Result<Void> save(RequestSaveActivityRule request){
        Result<Void> result = activityRuleConfigService.saveActivityRule(request);
        if (ResultUtils.isFailure(result)){
            log.warn("save activity rule fail, request: {}, rCode: {}", request, result.rCode());
        }

        return result;
    }

    public Result<Void> update(RequestUpdateActivityRule request){
        Result<Void> result = activityRuleConfigService.updateActivityRule(request);
        if (ResultUtils.isFailure(result)){
            log.warn("update activity rule fail, request: {}, rCode: {}", request, result.rCode());
        }
        return result;
    }


    public Result<Void> delete(Long id, Integer appId, String operator){
        Result<Void> result = activityRuleConfigService.deleteActivityRule(id, appId, operator);
        if (ResultUtils.isFailure(result)){
            log.warn("delete activity rule fail, id: {}, appId: {}, operator: {}", id, appId, operator);
        }
        return result;
    }

    public Result<List<ActivityRuleConfigBean>> list(Integer appId){
        Result<List<ActivityRuleConfigBean>> result = activityRuleConfigService.listActivityRule(appId);
        if (ResultUtils.isFailure(result)){
            log.warn("list activity rule fail, appId: {}", appId);
        }

        return result;
    }


}
