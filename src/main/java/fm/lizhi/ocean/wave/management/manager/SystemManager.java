package fm.lizhi.ocean.wave.management.manager;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.management.config.apollo.ProjectConfig;
import fm.lizhi.ocean.wave.management.config.apollo.SsoClientConfig;
import fm.lizhi.ocean.wave.management.model.result.system.EnvResult;
import fm.lizhi.ocean.wave.management.model.result.system.UserResult;
import fm.lizhi.ocean.wave.management.model.vo.MenuItemVO;
import fm.lizhi.ocean.wave.management.util.JsonUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import fm.lizhi.sso.client.config.SsoConfigLoader;
import fm.lizhi.sso.enums.ErrorCode;
import fm.lizhi.sso.rpc.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.SetValuedMap;
import org.apache.commons.collections4.multimap.HashSetValuedHashMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 系统管理
 */
@Component
@Slf4j
public class SystemManager {

    /**
     * vtag属性key
     */
    private static final String VTAG_PROPERTY_KEY = "vtag";

    @Autowired
    private SsoClientConfig ssoClientConfig;

    @Autowired
    private ProjectConfig projectConfig;

    @Autowired
    private AuthenticationRpcService authenticationRpcService;

    /**
     * 业务应用角色 -> 菜单列表映射 异步加载线程池
     */
    private final ExecutorService appRoleMenusExecutor = new ThreadPoolExecutor(1, 1, 0, TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(1000),
            new BasicThreadFactory.Builder().daemon(true).namingPattern("appRoleMenusExecutor-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 缓存key: SSO应用编码, value: 业务应用角色 -> 菜单列表映射. 注意, 如果较长时间未访问, 之后第一次访问会获取到旧值, 并异步刷新缓存
     */
    private final LoadingCache<String, SetValuedMap<AppRoleKey, MenuItemVO>> appRoleMenusCache = CacheBuilder.newBuilder()
            .refreshAfterWrite(1, TimeUnit.MINUTES)
            .build(new CacheLoader<String, SetValuedMap<AppRoleKey, MenuItemVO>>() {
                @Override
                public SetValuedMap<AppRoleKey, MenuItemVO> load(String ssoAppCode) {
                    return loadAppRoleMenusMap(ssoAppCode);
                }

                @Override
                public ListenableFuture<SetValuedMap<AppRoleKey, MenuItemVO>> reload(String ssoAppCode, SetValuedMap<AppRoleKey, MenuItemVO> oldValue) {
                    ListenableFutureTask<SetValuedMap<AppRoleKey, MenuItemVO>> futureTask = ListenableFutureTask.create(() -> {
                        try {
                            return loadAppRoleMenusMap(ssoAppCode);
                        } catch (Throwable e) {
                            log.error("re-loadAppRoleMenusMap error, return old value, ssoAppCode={}", ssoAppCode, e);
                            return oldValue;
                        }
                    });
                    appRoleMenusExecutor.submit(futureTask);
                    return futureTask;
                }
            });

    /**
     * 获取当前系统环境信息
     *
     * @return 当前系统环境信息
     */
    public EnvResult getEnv() {
        EnvResult envResult = new EnvResult();
        envResult.setServiceName(ConfigUtils.getServiceName());
        envResult.setRegion(ConfigUtils.getRegion());
        envResult.setBusinessEnv(ConfigUtils.getBusinessEnv());
        envResult.setDeployEnv(ConfigUtils.getEnv() != null ? ConfigUtils.getEnv().name() : null);
        envResult.setVTag(System.getProperty(VTAG_PROPERTY_KEY));
        envResult.setProjectVersion(projectConfig.getVersion());
        envResult.setSsoServerAddress(SsoConfigLoader.getSsoConfig().getSsoServerAddress());
        return envResult;
    }

    /**
     * 获取当前系统用户信息
     *
     * @return 当前系统用户信息
     */
    public UserResult getUser() {
        UserDTO userDTO = getUserDTO();
        List<Integer> authorizedAppIds = SessionExtUtils.getAuthorizedAppIds();
        UserResult userResult = new UserResult();
        userResult.setUserId(userDTO.getUserId());
        userResult.setAccount(userDTO.getAccount());
        userResult.setRealName(userDTO.getRealName());
        userResult.setAvatar(userDTO.getAvatar());
        userResult.setEmail(userDTO.getEmail());
        userResult.setAuthorizedAppIds(authorizedAppIds);
        userResult.setToken(SessionUtils.getToken());
        return userResult;
    }

    /**
     * 查询菜单列表, 根据当前用户的角色名称和所选业务应用ID进行匹配过滤, 所以SSO后台配置的角色名称不能重复.
     *
     * @return 菜单列表
     */
    public List<MenuItemVO> getMenuList() {
        Integer appId = SessionExtUtils.getAppId();
        PermissionRole permissionRole = SessionUtils.getPermissionRole();
        if (appId == null || permissionRole == null || CollectionUtils.isEmpty(permissionRole.getRoleList())) {
            return Collections.emptyList();
        }
        String ssoAppCode = ssoClientConfig.getServiceName();
        SetValuedMap<AppRoleKey, MenuItemVO> appRoleMenusMap = appRoleMenusCache.getUnchecked(ssoAppCode);
        TreeSet<MenuItemVO> allMenuItemVOS = new TreeSet<>(Comparator.comparing(MenuItemVO::getUrl));
        for (String roleName : permissionRole.getRoleList()) {
            AppRoleKey appRoleKey = new AppRoleKey(appId, roleName);
            Set<MenuItemVO> menuItemVOS = appRoleMenusMap.get(appRoleKey);
            allMenuItemVOS.addAll(menuItemVOS);
        }
        return new ArrayList<>(allMenuItemVOS);
    }

    private UserDTO getUserDTO() {
        UserDTO emptyUser = new UserDTO();
        String account = SessionUtils.getAccount();
        if (StringUtils.isEmpty(account)) {
            return emptyUser;
        }
        List<UserDTO> appUsers = SessionUtils.getAppUsers();
        if (CollectionUtils.isEmpty(appUsers)) {
            return emptyUser;
        }
        for (UserDTO appUser : appUsers) {
            if (Objects.equals(appUser.getAccount(), account)) {
                return appUser;
            }
        }
        return emptyUser;
    }

    /**
     * 加载指定单点登录应用下的 业务应用角色 -> 菜单列表映射. 注意! 在SSO后台修改角色名称会影响权限匹配, 必须等待缓存刷新.
     *
     * @param ssoAppCode SSO应用编码
     * @return 业务应用角色 -> 菜单列表映射
     */
    private SetValuedMap<AppRoleKey, MenuItemVO> loadAppRoleMenusMap(String ssoAppCode) {
        HashSetValuedHashMap<AppRoleKey, MenuItemVO> appRoleMenusMap = new HashSetValuedHashMap<>();
        String ssoRpcKey = ssoClientConfig.getRpcKey();
        String ssoRpcType = ssoClientConfig.getRpcType();
        List<RoleItem> roleItems = this.getRoleItems(ssoRpcKey, ssoRpcType, ssoAppCode);
        Map<Integer, PermissionItem> permissionItemMap = this.getPermissionItemMap(ssoRpcKey, ssoRpcType, ssoAppCode);
        for (RoleItem roleItem : roleItems) {
            List<RolePermissionItem> rolePermissionItems = this.getRolePermissionItems(ssoRpcKey, ssoRpcType, ssoAppCode, roleItem.getId());
            Set<Integer> authorizedAppIds = this.praseAuthorizedAppIds(rolePermissionItems, permissionItemMap);
            Set<MenuItemVO> authorizedMenuItemVOS = this.praseAuthorizedMenus(rolePermissionItems, permissionItemMap);
            for (Integer authorizedAppId : authorizedAppIds) {
                // 以业务应用ID+角色名称作为唯一标识, 添加当前角色对应的菜单列表
                AppRoleKey appRoleKey = new AppRoleKey(authorizedAppId, roleItem.getName());
                appRoleMenusMap.get(appRoleKey).addAll(authorizedMenuItemVOS);
            }
        }
        return appRoleMenusMap;
    }

    private Set<Integer> praseAuthorizedAppIds(List<RolePermissionItem> rolePermissionItems, Map<Integer, PermissionItem> permissionItemMap) {
        HashSet<Integer> authorizedAppIds = new HashSet<>();
        for (RolePermissionItem rolePermissionItem : rolePermissionItems) {
            PermissionItem permissionItem = permissionItemMap.get(rolePermissionItem.getPermissionId());
            if (permissionItem != null && !permissionItem.getMenu()) {
                String permissionUrl = permissionItem.getUrl();
                Integer appId = SessionExtUtils.parseAuthorizedAppId(permissionUrl);
                if (appId != null) {
                    authorizedAppIds.add(appId);
                }
            }
        }
        return authorizedAppIds;
    }

    private Set<MenuItemVO> praseAuthorizedMenus(List<RolePermissionItem> rolePermissionItems, Map<Integer, PermissionItem> permissionItemMap) {
        HashSet<MenuItemVO> authorizedMenus = new HashSet<>();
        for (RolePermissionItem rolePermissionItem : rolePermissionItems) {
            PermissionItem permissionItem = permissionItemMap.get(rolePermissionItem.getPermissionId());
            if (permissionItem != null && permissionItem.getMenu()) {
                MenuItemVO menuItemVO = new MenuItemVO()
                        .setId(permissionItem.getId())
                        .setParentId(permissionItem.getParentId())
                        .setName(permissionItem.getName())
                        .setUrl(permissionItem.getUrl())
                        .setExtraAttr(permissionItem.getAttached1());
                authorizedMenus.add(menuItemVO);
            }
        }
        return authorizedMenus;
    }

    private List<RoleItem> getRoleItems(String ssoRpcKey, String ssoRpcType, String ssoAppCode) {
        GetRoleListParam getRoleListParam = new GetRoleListParam();
        getRoleListParam.setKey(ssoRpcKey);
        getRoleListParam.setType(ssoRpcType);
        getRoleListParam.setAppCode(ssoAppCode);
        getRoleListParam.setEnable(true);
        RpcResponse<List<RoleItem>> rpcResponse;
        try {
            rpcResponse = authenticationRpcService.getRoles(getRoleListParam);
        } catch (RuntimeException e) {
            throw new IllegalStateException(String.format("authenticationRpcService.getRoles exception, ssoAppCode=%s", ssoAppCode), e);
        }
        if (rpcResponse.getCode() != ErrorCode.SUCCESS.getCode()) {
            throw new IllegalStateException(String.format("authenticationRpcService.getRoles failed, ssoAppCode=%s, code=%s, message=%s", ssoAppCode, rpcResponse.getCode(), rpcResponse.getMessage()));
        }
        List<RoleItem> roleItems = rpcResponse.getData();
        if (log.isDebugEnabled()) {
            if (roleItems == null) {
                log.debug("authenticationRpcService.getRoles, ssoAppCode={}, roleItems=null", ssoAppCode);
            } else {
                log.debug("authenticationRpcService.getRoles, ssoAppCode={}, roleItems={}", ssoAppCode, JsonUtils.toJsonString(roleItems));
            }
        }
        return ListUtils.emptyIfNull(roleItems);
    }

    private Map<Integer, PermissionItem> getPermissionItemMap(String ssoRpcKey, String ssoRpcType, String ssoAppCode) {
        List<PermissionItem> permissionItems = this.getPermissionItems(ssoRpcKey, ssoRpcType, ssoAppCode);
        Map<Integer, PermissionItem> permissionItemMap = new HashMap<>();
        for (PermissionItem permissionItem : permissionItems) {
            permissionItemMap.put(permissionItem.getId(), permissionItem);
        }
        return permissionItemMap;
    }

    private List<PermissionItem> getPermissionItems(String ssoRpcKey, String ssoRpcType, String ssoAppCode) {
        GetPermissionListParam getPermissionListParam = new GetPermissionListParam();
        getPermissionListParam.setKey(ssoRpcKey);
        getPermissionListParam.setType(ssoRpcType);
        getPermissionListParam.setAppCode(ssoAppCode);
        // 传入空列表表示查询该应用下的所有权限
        getPermissionListParam.setIdList(Collections.emptyList());
        RpcResponse<List<PermissionItem>> rpcResponse;
        try {
            rpcResponse = authenticationRpcService.getPermissions(getPermissionListParam);
        } catch (RuntimeException e) {
            throw new IllegalStateException(String.format("authenticationRpcService.getPermissions exception, ssoAppCode=%s", ssoAppCode), e);
        }
        if (rpcResponse.getCode() != ErrorCode.SUCCESS.getCode()) {
            throw new IllegalStateException(String.format("authenticationRpcService.getPermissions failed, ssoAppCode=%s, code=%s, message=%s", ssoAppCode, rpcResponse.getCode(), rpcResponse.getMessage()));
        }
        List<PermissionItem> permissionItems = rpcResponse.getData();
        if (log.isDebugEnabled()) {
            if (permissionItems == null) {
                log.debug("authenticationRpcService.getPermissions, ssoAppCode={}, permissionItems=null", ssoAppCode);
            } else {
                log.debug("authenticationRpcService.getPermissions, ssoAppCode={}, permissionItems={}", ssoAppCode, JsonUtils.toJsonString(permissionItems));
            }
        }
        return ListUtils.emptyIfNull(permissionItems);
    }

    private List<RolePermissionItem> getRolePermissionItems(String ssoRpcKey, String ssoRpcType, String ssoAppCode, Integer roleId) {
        GetPermissionsFromRole getPermissionsFromRole = new GetPermissionsFromRole();
        getPermissionsFromRole.setKey(ssoRpcKey);
        getPermissionsFromRole.setType(ssoRpcType);
        getPermissionsFromRole.setAppCode(ssoAppCode);
        getPermissionsFromRole.setRoleId(roleId);
        RpcResponse<List<RolePermissionItem>> rpcResponse;
        try {
            rpcResponse = authenticationRpcService.getPermissionsFromRole(getPermissionsFromRole);
        } catch (RuntimeException e) {
            throw new IllegalStateException(String.format("authenticationRpcService.getPermissionsFromRole exception, ssoAppCode=%s, roleId=%s", ssoAppCode, roleId), e);
        }
        if (rpcResponse.getCode() != ErrorCode.SUCCESS.getCode()) {
            throw new IllegalStateException(String.format("authenticationRpcService.getPermissionsFromRole failed, ssoAppCode=%s, roleId=%s, code=%s, message=%s", ssoAppCode, roleId, rpcResponse.getCode(), rpcResponse.getMessage()));
        }
        List<RolePermissionItem> rolePermissionItems = rpcResponse.getData();
        if (log.isDebugEnabled()) {
            if (rolePermissionItems == null) {
                log.debug("authenticationRpcService.getPermissionsFromRole, ssoAppCode={}, roleId={}, rolePermissionItems=null", ssoAppCode, roleId);
            } else {
                log.debug("authenticationRpcService.getPermissionsFromRole, ssoAppCode={}, roleId={}, rolePermissionItems={}", ssoAppCode, roleId, JsonUtils.toJsonString(rolePermissionItems));
            }
        }
        return ListUtils.emptyIfNull(rolePermissionItems);
    }

    /**
     * 应用角色Key, 应用ID和角色名称的组合唯一标识
     */
    @Data
    private static class AppRoleKey {

        /**
         * 用户在运营后台右上角选择的业务线的应用ID
         */
        private final Integer appId;

        /**
         * 单点登录系统的角色名称
         */
        private final String roleName;
    }
}
