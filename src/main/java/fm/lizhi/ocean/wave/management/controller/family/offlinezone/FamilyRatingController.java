package fm.lizhi.ocean.wave.management.controller.family.offlinezone;

import fm.lizhi.ocean.wave.management.manager.family.offlinezone.FamilyRatingManager;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.BatchUpdateFamilyRatingParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.GetFamilyRatingParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.ListFamilyRatingParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.GetFamilyRatingResult;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListFamilyRatingResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 线下厅公会评级控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/offline/familyRating")
@Slf4j
public class FamilyRatingController {

    @Autowired
    private FamilyRatingManager familyRatingManager;

    /**
     * 查询线下厅公会评级列表
     *
     * @param param 查询参数
     * @return 公会评级列表
     */
    @GetMapping("/list")
    public ResultVO<PageVO<ListFamilyRatingResult>> listFamilyRating(@Validated ListFamilyRatingParam param) {
        log.info("查询公会评级列表，参数：{}", param);
        Integer appId = SessionExtUtils.getAppId();

        if (appId == null) {
            return ResultVO.failure("应用ID不能为空");
        }

        param.setAppId(appId);
        return familyRatingManager.listFamilyRating(param);
    }

    /**
     * 批量更新公会评级
     *
     * @param param 批量更新参数
     * @return 更新结果
     */
    @PostMapping("/batchUpdate")
    public ResultVO<Void> batchUpdateFamilyRating(@RequestBody @Validated BatchUpdateFamilyRatingParam param) {
        log.info("批量更新公会评级，参数：{}", param);
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        return familyRatingManager.batchUpdateFamilyRating(param, operator, appId);
    }

    /**
     * 获取公会评级详情
     *
     * @param param 查询参数
     * @return 公会评级详情
     */
    @GetMapping("/get")
    public ResultVO<GetFamilyRatingResult> getFamilyRating(@Validated GetFamilyRatingParam param) {
        return familyRatingManager.getFamilyRating(param);
    }
}