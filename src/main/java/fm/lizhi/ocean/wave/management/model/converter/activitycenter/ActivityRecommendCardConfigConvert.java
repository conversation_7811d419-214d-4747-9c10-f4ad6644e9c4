package fm.lizhi.ocean.wave.management.model.converter.activitycenter;

import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityRecommendCardParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityRecommendCardParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityRecommendCardConfigResult;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityRecommendCardConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRecommendCard;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRecommendCard;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityRecommendCardConfigConvert {

    ActivityRecommendCardConfigConvert I = Mappers.getMapper(ActivityRecommendCardConfigConvert.class);

    @Mappings({
            @Mapping(source = "appId", target = "appId"),
            @Mapping(source = "operator", target = "operator"),
    })
    RequestSaveActivityRecommendCard toRequestSaveActivityRecommendCard(SaveActivityRecommendCardParam param, Integer appId, String operator);

    @Mappings({
            @Mapping(source = "appId", target = "appId"),
            @Mapping(source = "operator", target = "operator"),
    })
    RequestUpdateActivityRecommendCard toRequestUpdateActivityRecommendCard(UpdateActivityRecommendCardParam param, Integer appId, String operator);

    List<ActivityRecommendCardConfigResult> ActivityRecommendCardConfigResults(List<ActivityRecommendCardConfigBean> target);

}
