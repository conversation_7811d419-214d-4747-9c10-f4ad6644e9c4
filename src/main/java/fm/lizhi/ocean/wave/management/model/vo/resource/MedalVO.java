package fm.lizhi.ocean.wave.management.model.vo.resource;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class MedalVO {

    /**
     * 勋章ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 勋章名称
     */
    private String name;

    /**
     * 勋章图片
     */
    private String image;

}
