package fm.lizhi.ocean.wave.management.model.vo.anchor.singer;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class SingerVerifyRecordExcelVO {

    /**
     * 歌手ID
     */
    @ExcelProperty("歌手ID")
    private String singerUserId;

    /**
     * 用户名
     */
    @ExcelProperty("用户名")
    private String name;

    /**
     * 波段号
     */
    @ExcelProperty("波段号")
    private String band;

    /**
     * 性别
     */
    @ExcelProperty("性别")
    private String gender;

    /**
     * 家族名称
     */
    @ExcelProperty("家族名称")
    private String familyName;

    /**
     * 认证歌名
     */
    @ExcelProperty("认证歌名")
    private String songName;

    /**
     * 擅长曲风
     */
    @ExcelProperty("擅长曲风")
    private String songStyle;

    /**
     * 厅主ID
     */
    @ExcelProperty("厅主ID")
    private String njId;

    /**
     * 厅主昵称
     */
    @ExcelProperty("厅主昵称")
    private String njName;

    /**
     * 波段号
     */
    @ExcelProperty("厅主波段号")
    private String njBand;

     /**
     * 是否原创
     */
    @ExcelProperty("是否原创")
    private String originalSinger;

    /**
     * 原创链接
     */
    @ExcelProperty("原创链接")
    private String originalSongUrl;

    /**
     * 社媒截图
     */
    @ExcelProperty("社媒截图")
    private String socialVerifyImageList;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

    /**
     * 驳回原因
     */
    @ExcelProperty("驳回原因")
    private String rejectReason;

    /**
     * 审核状态
     */
    @ExcelProperty("审核状态")
    private String auditStatus;

    /**
     * 操作人
     */
    @ExcelProperty("操作人")
    private String operator;

    /**
     * 提交日期
     */
    @ExcelProperty("提交日期")
    private String createTime;

    /**
     * 审核时间
     */
    @ExcelProperty("审核时间")
    private String auditTime;

    /**
     * 音频地址
     */
    @ExcelProperty("音频地址")
    private String audioPath;


    /**
     * 联系方式
     */
    @ExcelProperty("联系微信")
    private String contactNumber;
}
