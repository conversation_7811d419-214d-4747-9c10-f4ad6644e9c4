package fm.lizhi.ocean.wave.management.model.param.activitycenter.ai;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class ActivityAiResultRateParam {
    /**
     * 结果类型
     * 1: 文字
     * 2: 图片
     */
    @NotNull(message = "类型不能为空")
    @Min(value = 1, message = "类型最小值为1")
    @Max(value = 2, message = "类型最大值为2")
    private Integer type;
    /**
     * 追踪ID
     */
    @NotNull(message = "追踪ID不能为空")
    private String serialId;

    /**
     * 评分
     */
    @NotNull(message = "评分不能为空")
    private Integer score;
    /**
     * 建议
     */
    private String advice;


} 