package fm.lizhi.ocean.wave.management.model.param.activitycenter.ai;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import fm.lizhi.ocean.godzilla.api.constant.ActivityPlanImageSceneEnum;
import fm.lizhi.ocean.godzilla.api.constant.ActivityPlanImageStyleEnum;
import fm.lizhi.ocean.wave.management.common.validation.EnumValue;
import lombok.Data;

import javax.validation.constraints.*;
import java.beans.Transient;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 文生图参数
 */
@Data
public class TextToImageParam {

    /**
     * 会话id
     */
    @NotNull(message = "会话id不能为空")
    @Size(max = 64, message = "会话id长度不能超过{max}个字符")
    private String sessionId;

    /**
     * 图片风格
     */
    @NotNull(message = "图片风格不能为空")
    @EnumValue(value = ActivityPlanImageStyleEnum.class, message = "图片风格不合法")
    private Integer imageStyle;

    /**
     * 图片场景
     */
    @NotNull(message = "图片场景不能为空")
    @EnumValue(value = ActivityPlanImageSceneEnum.class, message = "图片场景不合法")
    private Integer imageScene;

    /**
     * 宽度
     */
    @NotNull(message = "宽度不能为空")
    @Min(value = 64, message = "宽度不能小于{value}")
    @Max(value = 2048, message = "宽度不能大于{value}")
    @JsonSetter(nulls = Nulls.SKIP)
    private Integer width = 0;

    /**
     * 高度
     */
    @NotNull(message = "高度不能为空")
    @Min(value = 64, message = "高度不能小于{value}")
    @Max(value = 2048, message = "高度不能大于{value}")
    @JsonSetter(nulls = Nulls.SKIP)
    private Integer height = 0;

    /**
     * 用户输入
     */
    @NotNull(message = "用户输入不能为空")
    @Size(min = 1, max = 500, message = "用户输入长度必须在{min}到{max}个字符之间")
    private String userInput;

    /**
     * 生成图片数量
     */
    @NotNull(message = "生成图片数量不能为空")
    @Min(value = 1, message = "生成图片数量不能小于{value}")
    @Max(value = 8, message = "生成图片数量不能大于{value}")
    @JsonSetter(nulls = Nulls.SKIP)
    private Integer count = 1;

    /**
     * 校验宽高比
     *
     * @return 是否合法
     */
    @AssertTrue(message = "宽和高的长边除以短边不能大于16")
    @Transient
    protected boolean isWidthHeightRatioValid() {
        if (width == null || height == null) {
            return true;
        }
        BigDecimal longerSide = BigDecimal.valueOf(Math.max(width, height));
        BigDecimal shorterSide = BigDecimal.valueOf(Math.min(width, height));
        return longerSide.divide(shorterSide, 2, RoundingMode.DOWN).compareTo(BigDecimal.valueOf(16)) <= 0;
    }
}
