package fm.lizhi.ocean.wave.management.remote.service.resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.constants.BusinessEvnEnum;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestGetDecorates;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.service.DecorateService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class DecorateServiceRemote {

    @Autowired
    private DecorateService decorateService;

    public Result<PageBean<DecorateInfoBean>> getMountList(String name, Integer page, Integer size, int appId) {
        Result<PageBean<DecorateInfoBean>> result = decorateService.getDecorates(new RequestGetDecorates()
            .setDecorateName(name)
            .setPageNo(page)
            .setPageSize(size)
            .setDecorateType(PlatformDecorateTypeEnum.VEHICLE.getType())
            .setAppId(appId)
        );
        if (ResultUtils.isFailure(result)) {
            log.warn("getMountList failed. name:{}, page:{}, size:{}, rCode:{}", name, page, size, result.rCode());
        }
        return result;
    }

    public Result<PageBean<DecorateInfoBean>> getMedalList(String name, Integer page, Integer size, int appId) {
        Result<PageBean<DecorateInfoBean>> result = decorateService.getDecorates(new RequestGetDecorates()
            .setDecorateName(name)
            .setPageNo(page)
            .setPageSize(size)
            .setDecorateType(PlatformDecorateTypeEnum.MEDAL.getType())
            .setAppId(appId)
        );
        if (ResultUtils.isFailure(result)) {
            log.warn("getMedalList failed. name:{}, page:{}, size:{}, rCode:{}", name, page, size, result.rCode());
        }
        return result;
    }
}
