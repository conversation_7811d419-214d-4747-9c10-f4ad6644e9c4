package fm.lizhi.ocean.wave.management.model.param.utility.webp;

import lombok.Data;

/**
 * Webp node服务转换参数
 */
@Data
public class WebpNodeConvertParam {

    /**
     * 源文件格式: SVGA / PAG
     */
    private String sourceFormat;

    /**
     * 源文件URL. 注意这里必须是node服务容器能访问到的域名, 所以外层需要做必要的域名映射
     */
    private String sourceUrl;

    /**
     * 业务线名称, 用于日志跟踪, 无其他作用, 对应ConfigUtils#getBusinessEnv()
     */
    private String businessEnv;

    /**
     * 业务类型, 用于日志跟踪, 无其他作用
     */
    private String bizType;

    /**
     * 压缩级别, 0~6, 值越低, 文件越大
     */
    private Integer compressLevel;

    /**
     * 是否启用混合压缩模式
     */
    private Boolean compressMixed;
}
