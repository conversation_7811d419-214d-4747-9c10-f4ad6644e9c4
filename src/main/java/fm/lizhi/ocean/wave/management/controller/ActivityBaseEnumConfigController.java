package fm.lizhi.ocean.wave.management.controller;

import fm.lizhi.ocean.wave.management.manager.ActivityBaseEnumConfigManager;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityBaseConfigResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/activity")
public class ActivityBaseEnumConfigController {

    @Autowired
    private ActivityBaseEnumConfigManager activityBaseEnumConfigManager;

    /**
     * 获取基础枚举配置
     */
    @GetMapping("/getBaseEnumConfig")
    public ResultVO<ActivityBaseConfigResult> getBaseEnumConfig() {
        return activityBaseEnumConfigManager.getBaseEnumConfig(SessionExtUtils.getAppId());
    }

}
