package fm.lizhi.ocean.wave.management.controller.utility.webp;

import fm.lizhi.ocean.wave.management.manager.utility.webp.WebpConvertManager;
import fm.lizhi.ocean.wave.management.model.converter.utility.webp.WebpConvertConvert;
import fm.lizhi.ocean.wave.management.model.param.utility.webp.WebpConvertParam;
import fm.lizhi.ocean.wave.management.model.param.utility.webp.WebpSyncConvertParam;
import fm.lizhi.ocean.wave.management.model.result.utility.webp.WebpConvertResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.utility.webp.WebpSyncConvertVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * webp转换控制器
 */
@Slf4j
@RestController
@RequestMapping("/utility/webp")
public class WebpConvertController {

    @Autowired
    private WebpConvertConvert webpConvertConvert;

    @Autowired
    private WebpConvertManager webpConvertManager;

    @PostMapping("/convertToWebp")
    public ResultVO<WebpSyncConvertVO> convertToWebp(@Validated @RequestBody WebpSyncConvertParam param) {
        WebpConvertParam convertParam = webpConvertConvert.toHttpRequestConvertParam(param);
        log.info("Received webp convert http request, param={}", convertParam);
        WebpConvertResult convertResult = webpConvertManager.webpConvert(convertParam);
        log.info("Finished webp convert, convertResult={}", convertResult);
        WebpSyncConvertVO webpSyncConvertVO = webpConvertConvert.toWebpSyncConvertVO(convertResult);
        if (webpSyncConvertVO.isFailure()) {
            String msg = StringUtils.defaultIfBlank(webpSyncConvertVO.getMsg(), "转换失败, code: " + webpSyncConvertVO.getCode());
            return ResultVO.failure(msg);
        }
        return ResultVO.success(webpSyncConvertVO);
    }
}
