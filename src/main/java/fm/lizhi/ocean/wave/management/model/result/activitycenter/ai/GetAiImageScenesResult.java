package fm.lizhi.ocean.wave.management.model.result.activitycenter.ai;

import lombok.Data;

import java.util.List;

/**
 * 获取AI图片场景枚举结果
 */
@Data
public class GetAiImageScenesResult {

    /**
     * 图片场景列表
     */
    private List<ImageScene> imageScenes;

    /**
     * 图片场景
     */
    @Data
    public static class ImageScene {

        /**
         * 场景值
         */
        private Integer value;

        /**
         * 场景名称
         */
        private String name;

        /**
         * 图片宽度
         */
        private Integer width;

        /**
         * 图片高度
         */
        private Integer height;
    }
}
