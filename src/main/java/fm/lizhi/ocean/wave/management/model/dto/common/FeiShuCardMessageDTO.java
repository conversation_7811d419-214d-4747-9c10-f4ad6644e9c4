package fm.lizhi.ocean.wave.management.model.dto.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 飞书卡片消息数据结构, 用于飞书自定义机器人发送卡片消息. 参考
 * <a href="https://open.feishu.cn/document/common-capabilities/message-card/message-cards-content/card-structure/card-content">
 * 了解卡片结构</a>
 */
@Data
public class FeiShuCardMessageDTO {

    /**
     * 消息类型, 卡片消息固定为 interactive
     */
    @JsonProperty("msg_type")
    private final String msgType = "interactive";
    /**
     * 卡片结构
     */
    private Card card;

    /**
     * 飞书卡片
     */
    @Data
    public static class Card {
        /**
         * 卡片头部
         */
        private Header header;
        /**
         * 卡片内容
         */
        private List<Element> elements;
    }

    /**
     * 飞书卡片头部
     */
    @Data
    public static class Header {

        /**
         * 配置卡片的主标题信息
         */
        private Title title;
        /**
         * 标题主题颜色
         *
         * @see Colors
         */
        private String template;
    }

    /**
     * 飞书卡片标题
     */
    @Data
    public static class Title {
        /**
         * 标题文本标识
         */
        private final String tag = "plain_text";
        /**
         * 卡片主标题内容
         */
        private String content;
    }

    /**
     * 飞书卡片元素, 纯标记性接口, 不定义任何方法, 仅为了保证List泛型的类型安全
     */
    public interface Element {
    }

    /**
     * 多列布局容器
     */
    @Data
    public static class ColumnSet implements Element {

        /**
         * 多列布局容器的标识
         */
        private final String tag = "column_set";
        /**
         * 多列布局的背景色样式
         *
         * @see Colors
         */
        @JsonProperty("background_style")
        private String backgroundStyle;
        /**
         * 多列布局容器内的列
         */
        private List<Column> columns;
    }

    /**
     * 单列布局容器
     */
    @Data
    public static class Column {

        /**
         * 列宽度与列内元素宽度一致
         */
        public static final String WIDTH_AUTO = "auto";
        /**
         * 列宽度按 weight 参数定义的权重分布
         */
        public static final String WIDTH_WEIGHTED = "weighted";
        /**
         * 顶对齐
         */
        public static final String VERTICAL_ALIGN_TOP = "top";
        /**
         * 居中对齐
         */
        public static final String VERTICAL_ALIGN_CENTER = "center";
        /**
         * 底部对齐
         */
        public static final String VERTICAL_ALIGN_BOTTOM = "bottom";

        /**
         * 单列布局容器的标识
         */
        private final String tag = "column";
        /**
         * 列宽度属性
         */
        private String width;
        /**
         * 当 width 取值 weighted 时生效, 表示当前列的宽度占比. 取值范围: 1 ~ 5
         */
        private Integer weight;
        /**
         * 列内成员垂直对齐方式
         */
        @JsonProperty("vertical_align")
        private String verticalAlign;
        /**
         * 需要在列内展示的卡片元素
         */
        private List<Element> elements;
    }

    /**
     * Markdown富文本
     */
    @Data
    public static class Markdown implements Element {

        /**
         * 文本左对齐
         */
        public static final String TEXT_ALIGN_LEFT = "left";
        /**
         * 文本居中对齐
         */
        public static final String TEXT_ALIGN_CENTER = "center";
        /**
         * 文本右对齐
         */
        public static final String TEXT_ALIGN_RIGHT = "right";

        /**
         * Markdown 组件的标识
         */
        private final String tag = "markdown";
        /**
         * 使用已支持的 Markdown 语法构造 Markdown 内容
         */
        private String content;
        /**
         * 设置文本内容的对齐方式
         */
        @JsonProperty("text_align")
        private String textAlign;
    }

    /**
     * 水平分割线
     */
    @Data
    public static class Hr implements Element {

        /**
         * 水平分割线的标识
         */
        private final String tag = "hr";
    }

    /**
     * 颜色枚举值, 参考
     * <a href="https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/enumerations-for-fields-related-to-color">
     * 颜色枚举值</a>
     */
    @Data
    public static class Colors {
        /**
         * 背景-白
         */
        public static final String BG_WHITE = "bg-white";
        /**
         * 白
         */
        public static final String WHITE = "white";
        /**
         * 蓝
         */
        public static final String BLUE = "blue";
        /**
         * 粉
         */
        public static final String CARMINE = "carmine";
        /**
         * 绿
         */
        public static final String GREEN = "green";
        /**
         * 靛蓝
         */
        public static final String INDIGO = "indigo";
        /**
         * 柠檬绿
         */
        public static final String LIME = "lime";
        /**
         * 灰, 中性色-neutral
         */
        public static final String GREY = "grey";
        /**
         * 橙
         */
        public static final String ORANGE = "orange";
        /**
         * 紫
         */
        public static final String PURPLE = "purple";
        /**
         * 红
         */
        public static final String RED = "red";
        /**
         * 向日葵黄
         */
        public static final String SUNFLOWER = "sunflower";
        /**
         * 青
         */
        public static final String TURQUOISE = "turquoise";
        /**
         * 紫红
         */
        public static final String VIOLET = "violet";
        /**
         * 天蓝
         */
        public static final String WATHET = "wathet";
        /**
         * 黄
         */
        public static final String YELLOW = "yellow";
    }
}
