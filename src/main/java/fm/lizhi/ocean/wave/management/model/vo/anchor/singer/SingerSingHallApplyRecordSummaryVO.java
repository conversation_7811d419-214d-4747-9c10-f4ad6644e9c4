package fm.lizhi.ocean.wave.management.model.vo.anchor.singer;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wave.management.model.vo.family.FamilyVO;
import fm.lizhi.ocean.wave.management.model.vo.user.UserVO;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * 点唱厅申请记录-汇总
 *
 * <AUTHOR>
 * @date 2025-03-27 03:27:23
 */
@Data
@Accessors(chain = true)
public class SingerSingHallApplyRecordSummaryVO {
    /**
     * ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 家族ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyId;
    
    private FamilyVO familyInfo;
    /**
     * 厅主ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long njId;
    private UserVO njInfo;

    /**
     * 审核状态，0-审核中，1-审核通过，2-审核未通过
     */
    private Integer auditStatus;


    /**
     * 认证歌手数
     */
    private Long singerAuthCnt;

    /**
     * 优质歌手数
     */
    private Long seniorSingerAuthCnt;

    /**
     * 提交时间
     */
    private Long applyTime;

    /**
     * 添加时间
     */
    private Long createTime;

    /**
     * 操作人
     */
    private String operator;
}