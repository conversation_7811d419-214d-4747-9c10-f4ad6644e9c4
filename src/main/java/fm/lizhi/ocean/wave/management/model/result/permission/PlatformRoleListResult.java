package fm.lizhi.ocean.wave.management.model.result.permission;

import fm.lizhi.ocean.wave.management.model.vo.permission.PermissionVO;
import fm.lizhi.ocean.wave.management.model.vo.permission.PlatformRoleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/20 15:06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PlatformRoleListResult extends PlatformRoleVO {

    /**
     * 关联权限
     */
    private List<PermissionVO> permissions;

}
