package fm.lizhi.ocean.wave.management.model.param.anchor.singer;

import com.alibaba.excel.util.StringUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量歌手
 */
@Data
@Accessors(chain = true)
public class BatchImportSingerParam {

    /**
     * 导入的歌手信息
     */
    @NotEmpty(message = "歌手的信息不能为空")
    @NotNull(message = "歌手的信息不能为空")
    private List<@Valid ImportSingerParam> singerInfoList;


    @Data
    public static class ImportSingerParam {
        @NotNull(message = "用户ID不能为空")
        private Long singerId;

        @NotNull(message = "曲风不能为空")
        @NotBlank(message = "曲风不能为空")
        private String songStyle;

        @NotNull(message = "原创不能为空")
        private Boolean originalSinger;
        @NotNull(message = "歌手等级不能为空")
        private Integer singerType;

        private String contactNumber = StringUtils.EMPTY;
    }
}
