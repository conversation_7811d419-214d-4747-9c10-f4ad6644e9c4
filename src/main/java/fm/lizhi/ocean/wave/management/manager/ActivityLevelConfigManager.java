package fm.lizhi.ocean.wave.management.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ActivityLevelConfigConvert;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityLevelParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityLevelParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityLevelConfigResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.remote.service.activitycenter.ActivityLevelConfigServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityLevel;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityLevel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityLevelConfigManager {

    @Autowired
    private ActivityLevelConfigServiceRemote activityLevelConfigServiceRemote;


    /**
     * 保存
     */
    public ResultVO<Boolean> save(SaveActivityLevelParam param, Integer appId, String operator) {

        RequestSaveActivityLevel request = ActivityLevelConfigConvert.I.toRequestSaveActivityLevel(param, appId, operator);
        Result<Boolean> result = activityLevelConfigServiceRemote.save(request);

        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result);
        }

        return ResultVO.success(result.target());
    }

    /**
     * 更新
     */
    public ResultVO<Boolean> update(UpdateActivityLevelParam param, Integer appId, String operator) {

        RequestUpdateActivityLevel request = ActivityLevelConfigConvert.I.toRequestUpdateActivityLevel(param, appId, operator);
        Result<Boolean> result = activityLevelConfigServiceRemote.update(request);

        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result);
        }

        return ResultVO.success(result.target());
    }

    /**
     * 删除
     */
    public ResultVO<Boolean> delete(Long id, Integer appId, String operator) {
        Result<Boolean> result = activityLevelConfigServiceRemote.delete(id, appId, operator);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result);
        }
        return ResultVO.success(result.target());
    }

    /**
     * 列表
     */
    public ResultVO<List<ActivityLevelConfigResult>> list(Integer appId) {

        Result<List<fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean>> result = activityLevelConfigServiceRemote.list(appId);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result);
        }

        List<ActivityLevelConfigResult> list = ActivityLevelConfigConvert.I.toActivityLevelConfigBeanList(result.target());
        return ResultVO.success(list);
    }

    public ResultVO<ActivityLevelConfigResult> getLevelByClassId(Integer appId, Long classId) {
        Result<ActivityLevelConfigBean> result = activityLevelConfigServiceRemote.getLevelByClassId(classId, appId);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result);
        }

        return ResultVO.success(ActivityLevelConfigConvert.I.toActivityLevelConfigResult(result.target()));
    }
}
