package fm.lizhi.ocean.wave.management.model.param.family.offlinezone;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 更新等级配置参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UpdateLevelConfigParam extends SaveLevelConfigParam {

    /**
     * 等级ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;
}
