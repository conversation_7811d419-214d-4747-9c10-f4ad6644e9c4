package fm.lizhi.ocean.wave.management.config;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;

/**
 * WebMvc异常处理器, 因为当前项目不是直接对用户提供服务, 所以可以直接返回异常信息
 */
@RestControllerAdvice
@Slf4j
public class WebMvcExceptionHandler {

    /**
     * 通用异常处理器, 如果其他异常处理器没有命中, 则由此处理
     *
     * @param e       异常
     * @param request 请求
     * @return 通用返回
     */
    @ExceptionHandler(Exception.class)
    public ResultVO<Void> handleException(Exception e, HttpServletRequest request) {
        if (BlockException.isBlockException(e)) {
            log.warn("Sentinel限制异常, uri: {}", request.getRequestURI(), e);
            return ResultVO.failure("请求频繁触发Sentinel限制");
        }
        log.error("未知异常, uri: {}", request.getRequestURI(), e);
        return ResultVO.failure(String.format("未知异常: %s", e.getMessage()));
    }

    /**
     * 参数校验异常处理器
     *
     * @param e       异常
     * @param request 请求
     * @return 参数校验返回
     */
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    public ResultVO<Void> handleMethodArgumentNotValidException(Exception e, HttpServletRequest request) {
        BindingResult bindingResult = e instanceof MethodArgumentNotValidException
                ? ((MethodArgumentNotValidException) e).getBindingResult()
                : ((BindException) e).getBindingResult();
        FieldError fieldError = bindingResult.getFieldError();
        ObjectError globalError = bindingResult.getGlobalError();
        String message;
        if (fieldError != null) {
            message = fieldError.getField() + ": " + fieldError.getDefaultMessage();
        } else if (globalError != null) {
            message = globalError.getObjectName() + ": " + globalError.getDefaultMessage();
        } else {
            message = "无效参数";
        }
        log.info("参数校验不通过, uri: {}, message: {}", request, message);
        return ResultVO.failure(message);
    }

    /**
     * Sentinel限制异常处理器
     *
     * @param e       异常
     * @param request 请求
     * @return Sentinel限制返回
     */
    @ExceptionHandler(BlockException.class)
    public ResultVO<Void> handleBlockException(BlockException e, HttpServletRequest request) {
        log.warn("Sentinel限制异常, uri: {}", request.getRequestURI(), e);
        return ResultVO.failure("请求频繁触发Sentinel限制");
    }

    /**
     * 客户端中断异常处理器. 注意该异常处理器仅用于处理当前服务对外提供HTTP的情况, 如果是内部对第三方的HTTP调用, 请注意自行捕获此异常
     *
     * @param e 客户端中断异常
     */
    @ExceptionHandler(ClientAbortException.class)
    public void handleClientAbortException(ClientAbortException e, HttpServletRequest request) {
        log.warn("客户端中断异常, uri: {}, message: {}", request.getRequestURI(), e.getMessage());
    }

    /**
     * 请求报文格式错误异常处理器
     *
     * @param e 请求报文格式错误异常
     * @return 请求报文格式错误返回
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResultVO<Void> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        log.info("请求报文格式错误异常, message: {}", e.getMessage());
        return ResultVO.failure("请求报文格式错误");
    }

    /**
     * 请求缺少参数异常处理器
     *
     * @param e 请求缺少参数异常
     * @return 请求缺少参数返回
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResultVO<Void> missingServletRequestParameterException(MissingServletRequestParameterException e, HttpServletRequest request) {
        log.info("请求缺少参数, uri: {}, parameterName: {}", request.getRequestURI(), e.getParameterName());
        return ResultVO.failure(String.format("请求缺少参数, parameterName: %s", e.getParameterName()));
    }

    /**
     * 请求参数类型不匹配异常处理器
     *
     * @param e       请求参数类型不匹配异常
     * @param request 请求
     * @return 请求参数类型不匹配返回
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResultVO<Void> methodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        log.info("请求参数类型不匹配, uri: {}, propertyName: {}", request.getRequestURI(), e.getPropertyName());
        return ResultVO.failure(String.format("请求参数类型不匹配, propertyName: %s", e.getPropertyName()));
    }

    /**
     * 请求接口不支持异常处理器
     *
     * @param e       请求接口不支持异常
     * @param request 请求
     * @return 请求接口不支持返回
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResultVO<Void> methodNotSupportedException(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        log.info("请求接口不支持{}, uri: {}", e.getMethod(), request.getRequestURI());
        return ResultVO.failure(String.format("请求接口不支持%s", e.getMethod()));
    }
}
