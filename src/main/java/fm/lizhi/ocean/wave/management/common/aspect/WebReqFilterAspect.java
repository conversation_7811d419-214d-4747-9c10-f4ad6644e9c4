package fm.lizhi.ocean.wave.management.common.aspect;

import fm.lizhi.ocean.wave.management.config.apollo.BusinessConfig;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * WebMvc日志切面
 *
 * <AUTHOR>
 */
@Aspect
@Slf4j
@ControllerAdvice
@Component
@Order
public class WebReqFilterAspect {

    @Autowired
    private BusinessConfig businessConfig;

    /**
     * WebMvc日志
     *
     * @param joinPoint 切点
     * @return 执行结果
     * @throws Throwable 异常
     */
    @Around("@within(org.springframework.web.bind.annotation.RestController)")
    public Object webReqFilter(ProceedingJoinPoint joinPoint) throws Throwable {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (!(requestAttributes instanceof ServletRequestAttributes)) {
            log.info("RequestAttributes is not instance of ServletRequestAttributes, skip log.");
            return joinPoint.proceed();
        }
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        String uri = request.getRequestURI();
        try {
            //拦截是否升级中
            Pair<Boolean, String> res = businessConfig.checkUpgrade(uri);
            if (res.getLeft()) {
                return ResultVO.failure(res.getRight());
            }
        } catch (Exception e) {
            log.error("webReqFilter checkUpgrade happen error:", e);
        }
        try {
            return joinPoint.proceed();
        } finally {
            //后续有需要执行的，放在这里
        }
    }
}
