package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 厅签约成员列表结果
 */
@Data
public class ListRoomPlayerResult {

    /**
     * 用户id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 波段号
     */
    private String band;

    /**
     * 昵称
     */
    private String name;

    /**
     * 头像
     */
    private String photo;
}
