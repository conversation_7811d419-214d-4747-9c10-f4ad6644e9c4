package fm.lizhi.ocean.wave.management.kafka.singer.producer;

import fm.lizhi.common.kafka.common.SendResult;
import fm.lizhi.common.kafka.ioc.api.KafkaTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingerKafkaProducer {

    @Autowired
    @Qualifier("publicKafkaTemplate")
    private KafkaTemplate kafkaTemplate;

    /**
     * 发送消息
     *
     * @param topic 主题
     * @param key   键
     * @param msg   消息
     */
    public void send(String topic, String key, String msg) {
        SendResult sendResult = kafkaTemplate.send(topic, key, msg);
        if (sendResult.isSuccess()) {
            log.info("SingerKafkaProducer send success - topic:{}, key:{}, msg:{}", topic, key, msg);
        } else {
            log.error("SingerKafkaProducer send fail - topic:{}, key:{}, msg:{}", topic, key, msg);
        }
    }

}
