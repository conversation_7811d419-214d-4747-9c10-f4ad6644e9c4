package fm.lizhi.ocean.wave.management.model.param.anchor.singer;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Accessors(chain = true)
public class GetApplyListParam {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 厅ID
     */
    private Long njId;

    /**
     * 主播波段号
     */
    private String njBand;

    /**
     * 是否在黑名单中
     */
    private Boolean inBlackList;

    /**
     * 歌手类型
     */
    private Integer singerType;

    /**
     * 最小申请时间
     */
    private Long minApplyTime;

    /**
     * 最大申请时间
     */
    private Long maxApplyTime;

    /**
     * 歌曲风格
     */
    private List<String> songStyle;

    /**
     * 审核状态 0: 未审核 1: 审核中 2: 审核通过 3: 审核不通过
     */
    private List<Integer> auditStatus;

    /**
     * 审核结果
     */
    private Integer auditResult;

    /**
     * 是否原唱
     */
    private Boolean originalSinger;

    /**
     * 歌手波段号
     */
    private String singerBand;

    /**
     * 排序指标
     */
    private String orderMetrics;

    /**
     * 排序类型
     */
    private String orderType;

    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小为1")
    private Integer pageNo;

    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小最小为1")
    @Max(value = 100, message = "每页大小最大为100")
    private Integer pageSize;
}
