package fm.lizhi.ocean.wave.management.model.param.family.common;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import lombok.Data;

/**
 * 搜索公会参数
 */
@Data
public class SearchFamilyParam {

    /**
     * 分页页码
     */
    @JsonAlias({"pageNumber", "pageNo"})
    @JsonSetter(nulls = Nulls.SKIP)
    private Integer pageNo = 1;

    /**
     * 分页大小
     */
    @JsonSetter(nulls = Nulls.SKIP)
    private Integer pageSize = 20;

    /**
     * 公会名称
     */
    private String familyName;
}
