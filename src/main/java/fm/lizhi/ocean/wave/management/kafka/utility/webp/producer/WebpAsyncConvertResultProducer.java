package fm.lizhi.ocean.wave.management.kafka.utility.webp.producer;

import fm.lizhi.common.kafka.common.SendResult;
import fm.lizhi.common.kafka.ioc.api.KafkaTemplate;
import fm.lizhi.ocean.wave.management.model.result.utility.webp.WebpAsyncConvertResult;
import fm.lizhi.ocean.wave.management.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * webp异步转换结果生产者
 */
@Component
@Slf4j
public class WebpAsyncConvertResultProducer {

    @Autowired
    @Qualifier("publicKafkaTemplate")
    private KafkaTemplate kafkaTemplate;

    /**
     * 发送webp异步转换结果
     *
     * @param result webp异步转换结果
     */
    public void sendResult(WebpAsyncConvertResult result) {
        String key = result.getBizRequestId();
        String message = JsonUtils.toJsonString(result);
        SendResult sendResult = kafkaTemplate.send("ocean_wave_topic_webp_convert_result", key, message);
        if (sendResult.isSuccess()) {
            log.info("Send WebpAsyncConvertResult success, partition={}, offset={}, result={}", sendResult.getPartition(), sendResult.getOffset(), message);
            return;
        }
        throw new IllegalStateException("webp异步转换结果发送失败", sendResult.getCause());
    }
}
