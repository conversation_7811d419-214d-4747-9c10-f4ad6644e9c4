package fm.lizhi.ocean.wave.management.config;

import com.dianping.cat.aop.SpringMvcUrlAspect;
import fm.lizhi.ocean.wave.management.common.aspect.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

/**
 * Aspect配置, 在这里统一声明bean, 方便排序
 */
@Configuration
public class AspectConfiguration {

    /**
     * spring mvc CAT 切面
     *
     * @return spring mvc CAT 切面
     */
    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE + 500)
    public SpringMvcUrlAspect springMvcUrlAspect() {
        return new SpringMvcUrlAspect();
    }

    /**
     * WebMvc日志切面, 排序值必须比神灯日志traceId切面排序值大, 保证先生成traceId, 再打印日志
     *
     * @return WebMvc日志切面
     */
    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE + 1000)
    public WebMvcLogAspect webMvcLogAspect() {
        return new WebMvcLogAspect();
    }

    /**
     * 认证切面
     *
     * @return 认证切面
     */
    @Bean
    @Order(Ordered.LOWEST_PRECEDENCE - 3000)
    public AuthenticationAspect authenticationAspect() {
        return new AuthenticationAspect();
    }

    /**
     * 方法校验切面
     *
     * @return 方法校验切面
     */
    @Bean
    @Order(Ordered.LOWEST_PRECEDENCE - 2000)
    public ValidationAspect validationAspect() {
        return new ValidationAspect();
    }

    /**
     * 远程异常切面
     *
     * @return 远程异常切面
     */
    @Bean
    @Order(Ordered.LOWEST_PRECEDENCE - 1000)
    public RemoteExceptionAspect remoteExceptionAspect() {
        return new RemoteExceptionAspect();
    }

    /**
     * 上下文切面，一定要在认证切面切面之后执行
     *
     * @return 上下文切面
     */
    @Bean
    @Order(Ordered.LOWEST_PRECEDENCE - 2000)
    public ServiceContextAspect serviceContextAspect() {
        return new ServiceContextAspect();
    }
}
