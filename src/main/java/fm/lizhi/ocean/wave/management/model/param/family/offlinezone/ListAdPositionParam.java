package fm.lizhi.ocean.wave.management.model.param.family.offlinezone;

import fm.lizhi.ocean.wave.server.common.validator.EnumValue;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneAdPositionStatusEnum;
import lombok.Data;

/**
 * 列出广告展位参数
 */
@Data
public class ListAdPositionParam {

    /**
     * 横幅主题，模糊查询
     */
    private String theme;

    /**
     * 上架状态
     */
    @EnumValue(OfflineZoneAdPositionStatusEnum.class)
    private Integer status;
}
