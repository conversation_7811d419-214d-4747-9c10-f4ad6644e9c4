package fm.lizhi.ocean.wave.management.manager.singer.handler;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.management.manager.singer.SingerVerifyApplyManager;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerVerifyConvert;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerAuditParamDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerExecuteAuditDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.UpdateSingerVerifyStatusParamDTO;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 歌手认证待定状态处理器
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingerAuditWaitDecideHandler extends AbstractSingerAuditHandler {

    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;

    @Override
    public SingerExecuteAuditDTO auditHandle(SingerAuditParamDTO param, SingerVerifyRecord verifyRecord) {
        // 选中时，歌手库状态设置为认证中
        UpdateSingerVerifyStatusParamDTO paramDTO = SingerVerifyConvert.INSTANCE.buildUpdateParam(verifyRecord, param, SingerAuditStatusEnum.WAIT_DECIDE.getStatus(),
                SingerStatusEnum.AUTHENTICATING.getStatus(), false, verifyRecord.getSingerType());
        boolean res = singerVerifyApplyManager.updateSingerVerifyRecordStatus(paramDTO);
        log.info("SingerAuditWaitDecideHandler.auditHandle update singer verify record status, paramDTO:{}, res:{}", JsonUtil.dumps(paramDTO), res);
        return res ? SingerExecuteAuditDTO.success() : SingerExecuteAuditDTO.failure("修改状态失败，请稍候重试!");
    }

    @Override
    public SingerAuditStatusEnum getAuditStatusEnum() {
        return SingerAuditStatusEnum.WAIT_DECIDE;
    }
}
