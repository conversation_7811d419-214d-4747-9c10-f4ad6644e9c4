package fm.lizhi.ocean.wave.management.util;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.sso.client.SessionUtils;
import fm.lizhi.sso.rpc.PermissionRole;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.TreeSet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 会话扩展工具类, 用于获取会话中的扩展信息. 如果是单点登录信息获取, 请直接使用{@link SessionUtils}.
 * <p>
 * <b>注意:</b> 该类基于ThreadLocal实现, 请在Controller入口调用. 如果需要跨线程, 后续再统一整改支持.
 *
 * @see SessionUtils
 */
@Slf4j
public class SessionExtUtils {

    /**
     * appId请求头
     */
    public static final String APP_ID_HEADER = "X-App-Id";

    /**
     * app数据权限url的正则编译对象, 用于匹配app数据权限url. 比如"/appDataPermission/57333013", 其中appId为57333013
     */
    private static final Pattern APP_DATA_PERMISSION_URL_PATTERN = Pattern.compile("^/appDataPermission/(?<appId>\\d+)$");
    /**
     * app数据权限url的appId捕获组名, 用于从正则匹配结果中获取appId
     */
    private static final String APP_DATA_PERMISSION_URL_PATTERN_GROUP_APP_ID = "appId";

    /**
     * 获取当前会话中的appId, 可能为null
     *
     * @return appId
     */
    public static Integer getAppId() {
        RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
        if (!(requestAttributes instanceof ServletRequestAttributes)) {
            log.info("RequestAttributes is not instance of ServletRequestAttributes, return null.");
            return null;
        }
        String headerValue = ((ServletRequestAttributes) requestAttributes).getRequest().getHeader(APP_ID_HEADER);
        if (StringUtils.isBlank(headerValue)) {
            log.info("Header {} value is blank, return null.", APP_ID_HEADER);
            return null;
        }
        try {
            return Integer.parseInt(headerValue);
        } catch (NumberFormatException e) {
            log.info("Header {} value {} is not a number, return null.", APP_ID_HEADER, headerValue);
            return null;
        }
    }

    /**
     * 获取当前用户已被授权的appId列表, 可能为空列表, 调用者必须保证当前用户已登录
     *
     * @return 当前用户已被授权的appId列表
     */
    public static List<Integer> getAuthorizedAppIds() {
        PermissionRole permissionRole = SessionUtils.getPermissionRole();
        if (permissionRole == null || permissionRole.getPermissionUrlList() == null) {
            return Collections.emptyList();
        }
        TreeSet<Integer> authorizedAppIds = new TreeSet<>();
        for (String permissionUrl : permissionRole.getPermissionUrlList()) {
            Integer appId = parseAuthorizedAppId(permissionUrl);
            if (appId != null) {
                authorizedAppIds.add(appId);
            }
        }
        return new ArrayList<>(authorizedAppIds);
    }

    /**
     * 从给定的权限URL中解析出appId, 如果解析失败或者appId无效则返回null
     *
     * @param permissionUrl 权限URL
     * @return 解析出的appId, 可能为null
     */
    public static Integer parseAuthorizedAppId(String permissionUrl) {
        if (StringUtils.isBlank(permissionUrl)) {
            return null;
        }
        Matcher matcher = APP_DATA_PERMISSION_URL_PATTERN.matcher(permissionUrl);
        if (!matcher.find()) {
            return null;
        }
        String appIdString = matcher.group(APP_DATA_PERMISSION_URL_PATTERN_GROUP_APP_ID);
        int appId = Integer.parseInt(appIdString);
        BusinessEvnEnum businessEvnEnum = BusinessEvnEnum.from(appId);
        if (businessEvnEnum == null || businessEvnEnum.getOnline() != 1) {
            return null;
        }
        return appId;
    }
}
