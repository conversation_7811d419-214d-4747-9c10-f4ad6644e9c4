package fm.lizhi.ocean.wave.management.model.vo.award.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/26 11:26
 */
@Data
public class AwardDeliverRecordV2VO {
    /**
     * 奖励发放记录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 公会ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyId;

    /**
     * 公会名称
     */
    private String familyName;

    /**
     * 家族长用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyUserId;

    /**
     * 家族长用户名
     */
    private String familyUserName;

    /**
     * 发放周期开始时间, 毫秒时间戳, 周一的00:00:00.000
     */
    private Long awardStartTime;

    /**
     * 发放周期结束时间, 毫秒时间戳, 周日的23:59:59.999
     */
    private Long awardEndTime;

    /**
     * 发放时间, 毫秒时间戳
     */
    private Long deliverTime;

    /**
     * 发放状态, 1-发放中, 2-发放成功, 3-发放失败, 4-部分失败
     */
    private Integer status;

    /**
     * 记录生成时间, 毫秒时间戳
     */
    private Long createTime;

    /**
     * (PP)推荐卡总数
     */
    private Integer totalRecommendCardNumber;

    /**
     * (PP)等级推荐卡数量
     */
    private Integer levelRecommendCardNumber;

    /**
     * (PP)流水增长推荐卡数量
     */
    private Integer flowGrowthRecommendCardNumber;

    /**
     * (PP)新厅留存推荐卡数量
     */
    private Integer newRoomRetainRecommendCardNumber;

    /**
     * (PP)0流失厅推荐卡数量
     */
    private Integer zeroLostRoomRecommendCardNumber;

    /**
     * (PP)特殊推荐卡数量
     */
    private Integer specialRecommendCardNumber;

    /**
     * (PP)新厅名额总数
     */
    private Integer totalNewRoomNumber;

    /**
     * (PP)等级新厅名额数量
     */
    private Integer levelNewRoomNumber;

    /**
     * (PP)流水涨幅新厅名额数量
     */
    private Integer flowGrowthNewRoomNumber;

    /**
     * (PP)流失厅新厅名额数量
     */
    private Integer lostRoomNewRoomNumber;

    /**
     * (PP)新厅留存新厅名额数量
     */
    private Integer newRoomRetainNewRoomNumber;
}