package fm.lizhi.ocean.wave.management.model.converter.award.family;

import fm.lizhi.ocean.wave.management.model.converter.CommonConvert;
import fm.lizhi.ocean.wave.management.model.vo.award.family.*;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.AwardDeliverRecordV1Bean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.AwardDeliverRecordV2Bean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.FamilyAwardDeliverItemBean;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardDeliverRecordStatusEnum;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/26 15:29
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        uses = CommonConvert.class
)
public interface FamilyAwardDeliverConvert {

    FamilyAwardDeliverConvert I = Mappers.getMapper(FamilyAwardDeliverConvert.class);

    AwardDeliverRecordV2VO awardDeliverRecordV2Bean2VO(AwardDeliverRecordV2Bean bean);

    List<AwardDeliverRecordV2VO> awardDeliverRecordV2Beans2VOs(List<AwardDeliverRecordV2Bean> beans);

    AwardDeliverRecordV1VO awardDeliverRecordV1Bean2VO(AwardDeliverRecordV1Bean bean);

    List<AwardDeliverRecordV1VO> awardDeliverRecordV1Beans2VOs(List<AwardDeliverRecordV1Bean> beans);

    @Mapping(source = "status", target = "statusStr", qualifiedByName = "recordStatusStr")
    AwardDeliverRecordV2ExcelVO awardDeliverRecordV2Bean2ExcelVO(AwardDeliverRecordV2Bean bean);

    List<AwardDeliverRecordV2ExcelVO> awardDeliverRecordV2Beans2ExcelVOs(List<AwardDeliverRecordV2Bean> beans);

    @Mapping(source = "status", target = "statusStr", qualifiedByName = "recordStatusStr")
    AwardDeliverRecordV1ExcelVO awardDeliverRecordV1Bean2ExcelVO(AwardDeliverRecordV1Bean bean);

    List<AwardDeliverRecordV1ExcelVO> awardDeliverRecordV1Beans2ExcelVOs(List<AwardDeliverRecordV1Bean> beans);

    @Named("recordStatusStr")
    default String recordStatusStr(Integer status) {
        if (status == null) {
            return "";
        }
        if (FamilyAwardDeliverRecordStatusEnum.DELIVERING.getValue().equals(status)) {
            return "发放中";
        }
        if (FamilyAwardDeliverRecordStatusEnum.DELIVER_SUCCESS.getValue().equals(status)) {
            return "发放成功";
        }
        if (FamilyAwardDeliverRecordStatusEnum.DELIVER_FAIL.getValue().equals(status)) {
            return "发放失败";
        }
        if (FamilyAwardDeliverRecordStatusEnum.DELIVER_PART_FAIL.getValue().equals(status)) {
            return "部分发放失败";
        }
        return "";
    }

    @Mapping(target = "itemId", source = "id")
    FamilyAwardDeliverItemVO deliverItemBean2VO(FamilyAwardDeliverItemBean bean);

    List<FamilyAwardDeliverItemVO> deliverItemBeans2VOs(List<FamilyAwardDeliverItemBean> beans);

}
