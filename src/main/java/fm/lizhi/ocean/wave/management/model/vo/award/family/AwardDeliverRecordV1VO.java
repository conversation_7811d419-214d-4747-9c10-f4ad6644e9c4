package fm.lizhi.ocean.wave.management.model.vo.award.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/26 17:15
 */
@Data
public class AwardDeliverRecordV1VO {
    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 公会id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyId;
    /**
     * 公会名称
     */
    private String familyName;
    /**
     * 公会长用户id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyUserId;
    /**
     * 公会长昵称
     */
    private String familyUserName;
    /**
     * 发放周期开始时间, 毫秒时间戳, 周一的00:00:00.000
     */
    private Long awardStartTime;
    /**
     * 发放周期结束时间, 毫秒时间戳, 周日的23:59:59.999
     */
    private Long awardEndTime;
    /**
     * 发放时间, 毫秒时间戳
     */
    private Long deliverTime;
    /**
     * 发放状态, 1-发放中, 2-发放成功, 3-发放失败, 4-部分失败
     */
    private Integer status;
    /**
     * 记录生成时间, 毫秒时间戳
     */
    private Long createTime;
    /**
     * (陪伴/西米)推荐卡数量
     */
    private Integer recommendCardNumber;
    /**
     * (陪伴/西米)座驾id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long vehicleId;
    /**
     * (陪伴/西米)座驾名称
     */
    private String vehicleName;
    /**
     * (陪伴/西米)座驾图片
     */
    private String vehicleImage;
    /**
     * (陪伴/西米)勋章id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long medalId;
    /**
     * (陪伴/西米)勋章名称
     */
    private String medalName;
    /**
     * (陪伴/西米)勋章图片
     */
    private String medalImage;
    /**
     * (陪伴/西米)短号id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long shortNumberId;
    /**
     * (陪伴/西米)短号名称
     */
    private String shortNumberName;
}