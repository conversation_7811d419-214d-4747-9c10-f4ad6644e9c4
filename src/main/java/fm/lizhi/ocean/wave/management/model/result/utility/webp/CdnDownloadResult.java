package fm.lizhi.ocean.wave.management.model.result.utility.webp;

import fm.lizhi.ocean.wave.management.util.PathUtils;
import lombok.Data;

import java.beans.Transient;
import java.nio.file.Path;

/**
 * CDN下载结果
 */
@Data
public class CdnDownloadResult {

    /**
     * 成功
     */
    public static final int CODE_SUCCESS = 0;
    /**
     * 参数错误
     */
    public static final int CODE_PARAM_ERROR = 1;
    /**
     * 文件未找到, 特指404
     */
    public static final int CODE_NOT_FOUND = 2;
    /**
     * 内部错误, 指本服务的错误, 例如本地文件写入失败
     */
    public static final int CODE_INTERNAL_ERROR = 3;
    /**
     * 下载失败
     */
    public static final int CODE_DOWNLOAD_FAILURE = 4;

    /**
     * 状态码
     */
    private int code;

    /**
     * 本地路径
     */
    private Path localPath;

    /**
     * 文件SHA256值
     */
    private String sha;

    /**
     * 文件字节数
     */
    private long size;

    /**
     * 构造下载成功的结果
     *
     * @param localPath 本地路径
     * @return 成功的结果
     */
    public static CdnDownloadResult success(Path localPath) {
        CdnDownloadResult result = new CdnDownloadResult();
        result.setCode(CODE_SUCCESS);
        result.setLocalPath(localPath);
        result.setSha(PathUtils.sha256Hex(localPath));
        result.setSize(PathUtils.size(localPath));
        return result;
    }

    /**
     * 构造参数错误的结果
     *
     * @return 参数错误的结果
     */
    public static CdnDownloadResult paramError() {
        CdnDownloadResult result = new CdnDownloadResult();
        result.setCode(CODE_PARAM_ERROR);
        return result;
    }

    /**
     * 构造未发现的结果
     *
     * @return 未发现的结果
     */
    public static CdnDownloadResult notFound() {
        CdnDownloadResult result = new CdnDownloadResult();
        result.setCode(CODE_NOT_FOUND);
        return result;
    }

    /**
     * 构造内部错误的结果
     *
     * @return 内部错误的结果
     */
    public static CdnDownloadResult internalError() {
        CdnDownloadResult result = new CdnDownloadResult();
        result.setCode(CODE_INTERNAL_ERROR);
        return result;
    }

    /**
     * 构造下载失败的结果
     *
     * @return 下载失败的结果
     */
    public static CdnDownloadResult downloadFailure() {
        CdnDownloadResult result = new CdnDownloadResult();
        result.setCode(CODE_DOWNLOAD_FAILURE);
        return result;
    }

    /**
     * 是否成功
     *
     * @return 是否成功
     */
    @Transient
    public boolean isSuccess() {
        return code == CODE_SUCCESS;
    }

    /**
     * 是否失败
     *
     * @return 是否失败
     */
    @Transient
    public boolean isFailure() {
        return !isSuccess();
    }

    /**
     * 是否参数错误
     *
     * @return 是否参数错误
     */
    @Transient
    public boolean isParamError() {
        return code == CODE_PARAM_ERROR;
    }

    /**
     * 是否未发现
     *
     * @return 是否未发现
     */
    @Transient
    public boolean isNotFound() {
        return code == CODE_NOT_FOUND;
    }

    /**
     * 是否内部错误
     *
     * @return 是否内部错误
     */
    @Transient
    public boolean isInternalError() {
        return code == CODE_INTERNAL_ERROR;
    }

    /**
     * 是否下载失败
     *
     * @return 是否下载失败
     */
    @Transient
    public boolean isDownloadFailure() {
        return code == CODE_DOWNLOAD_FAILURE;
    }
}
