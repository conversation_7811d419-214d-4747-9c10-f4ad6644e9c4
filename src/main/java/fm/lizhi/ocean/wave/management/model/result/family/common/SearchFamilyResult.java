package fm.lizhi.ocean.wave.management.model.result.family.common;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.api.sign.constant.FamilyTypeEnum;
import lombok.Data;

/**
 * 搜索公会结果
 */
@Data
public class SearchFamilyResult {

    /**
     * 家族ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyId;

    /**
     * 家族名称
     */
    private String familyName;

    /**
     * 家族简介
     */
    private String familyNote;

    /**
     * 家族图标
     */
    private String familyIconUrl;

    /**
     * 家族类型
     *
     * @see FamilyTypeEnum#getCode()
     */
    private String familyType;

    /**
     * 家族长用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyUserId;

    /**
     * 家族长用户波段号
     */
    private String familyUserBand;

    /**
     * 家族长用户昵称
     */
    private String familyUserName;

    /**
     * 家族长用户头像
     */
    private String familyUserAvatar;

    /**
     * 家族创建时间, 毫秒时间戳
     */
    private Long createTime;
}
