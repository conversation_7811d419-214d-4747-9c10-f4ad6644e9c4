package fm.lizhi.ocean.wave.management.remote.service.anchor.singer;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerChatSceneEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSendSingerChat;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 歌手装扮远程服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingerChatRemote {

    @Autowired
    private SingerChatService singerChatService;

    /**
     * 发送私信
     *
     * @param appId      应用ID
     * @param singerId   歌手ID
     * @param singerType 歌手类型
     * @param songStyle  歌曲风格
     * @param scene      场景
     */
    public void sendSingerChat(int appId, long singerId, int singerType, String songStyle, SingerChatSceneEnum scene) {
        if (scene == null) {
            return;
        }
        RequestSendSingerChat request = new RequestSendSingerChat();
        request.setAppId(appId);
        request.setSingerId(singerId);
        request.setSingerType(singerType);
        request.setSongStyle(songStyle);
        request.setScene(scene);
        Result<Void> result = singerChatService.sendSingerChat(request);
        log.info("sendSingerChat.request: {}, code: {}, message: {}", JsonUtil.dumps(request), result.rCode(), result.getMessage());
    }

}
