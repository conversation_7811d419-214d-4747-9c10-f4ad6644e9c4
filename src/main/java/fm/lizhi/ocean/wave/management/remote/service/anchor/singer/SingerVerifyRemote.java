package fm.lizhi.ocean.wave.management.remote.service.anchor.singer;

import java.util.List;

import javax.annotation.Resource;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.*;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseBatchCancelBlackList;
import org.springframework.stereotype.Component;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerVerifyConvert;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.GetApplyListParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.QueryHistoryVerifyRecordParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.SingerVerifyAuditParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.UpdateRemarkParam;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseQueryHistoryVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseVerifyAudit;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerVerifyAdminService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class SingerVerifyRemote {

    @Resource
    private SingerVerifyAdminService singerVerifyService;


    public Result<PageBean<ResponseGetSingerVerifyRecord>> getSingerVerifyRecord(GetApplyListParam param, Integer appId) {
        log.info("获取歌手认证记录列表请求, param: {}, appId: {}", param, appId);
        RequestGetSingerVerifyRecord request = SingerVerifyConvert.INSTANCE.param2Request(param, appId);
        Result<PageBean<ResponseGetSingerVerifyRecord>> result = singerVerifyService.getSingerVerifyRecord(request);
        log.info("获取歌手认证记录列表响应, result: {}", result);
        return result;
    }

    public Result<Void> updateRemark(UpdateRemarkParam param, Integer appId) {
        log.info("更新歌手认证备注请求, param: {}, appId: {}", param, appId);
        RequestModifyVerifyApplyRemark request = SingerVerifyConvert.INSTANCE.param2RemarkRequest(param, appId);
        Result<Void> result = singerVerifyService.modifyVerifyApplyRemark(request);
        log.info("更新歌手认证备注响应, result: {}", result);
        return result;
    }

    /**
     * 歌手认证审核操作
     *
     * @param param 歌手认证审核操作参数
     * @return 歌手认证审核操作结果
     */
    public Result<ResponseVerifyAudit> auditVerify(SingerVerifyAuditParam param) {
        log.info("歌手认证审核操作请求, param: {}", param);
        RequestVerifyAudit request = SingerVerifyConvert.INSTANCE.param2OperateRequest(param);
        Result<ResponseVerifyAudit> result = singerVerifyService.verifyAudit(request);
        log.info("歌手认证审核操作响应, result: {}", result.rCode());
        return result;
    }

    /**
     * 查询歌手历史记录
     *
     * @param param 查询歌手历史记录参数
     * @return 查询歌手历史记录结果
     */
    public Result<List<ResponseQueryHistoryVerifyRecord>> queryHistoryVerifyRecord(QueryHistoryVerifyRecordParam param) {
        log.info("查询歌手历史记录请求, param: {}", param);
        RequestQueryHistoryVerifyRecord request = SingerVerifyConvert.INSTANCE.param2QueryHistoryVerifyRecordRequest(param);
        Result<List<ResponseQueryHistoryVerifyRecord>> result = singerVerifyService.queryHistoryVerifyRecord(request);
        log.info("查询歌手历史记录响应, result: {}", result);
        return result;
    }

    /**
     * 批量拉黑歌手
     *
     * @param appId 应用ID
     * @param userIds   用户ID
     * @return 结果
     */
    public Result<Void> batchBlackList(Integer appId, List<Long> userIds) {
        log.info("批量拉黑歌手请求, ids: {}", userIds);
        RequestBatchAddBlackList list = new RequestBatchAddBlackList();
        list.setUserIds(userIds);
        list.setAppId(appId);
        Result<Void> result = singerVerifyService.batchAddBlackList(list);
        log.info("批量拉黑歌手响应, result: {}", result);
        return result;
    }

    /**
     * 批量拉黑歌手
     *
     * @param appId 应用ID
     * @param userIds   用户ID
     * @return 结果
     */
    public Result<ResponseBatchCancelBlackList> batchCancelBlackList(Integer appId, List<Long> userIds) {
        log.info("批量拉黑歌手请求, ids: {}", userIds);
        RequestBatchCancelBlackList list = new RequestBatchCancelBlackList();
        list.setUserIds(userIds);
        list.setAppId(appId);
        Result<ResponseBatchCancelBlackList> result = singerVerifyService.batchCancelBlackList(list);
        log.info("批量拉黑歌手响应, result: {}", result);
        return result;
    }

}