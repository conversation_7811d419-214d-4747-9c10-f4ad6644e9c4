package fm.lizhi.ocean.wave.management.model.result.family.offlinezone;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 获取公会评级结果
 */
@Data
public class GetFamilyRatingResult {

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 公会ID
     */
    private Long familyId;

    /**
     * 等级配置ID
     */
    private Long levelId;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 环境：TEST/PRE/PRO
     */
    private String deployEnv;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long modifyTime;

    /**
     * 构造空结果, 表示未评级过
     *
     * @return 空结果
     */
    public static GetFamilyRatingResult empty() {
        return new GetFamilyRatingResult();
    }
}
