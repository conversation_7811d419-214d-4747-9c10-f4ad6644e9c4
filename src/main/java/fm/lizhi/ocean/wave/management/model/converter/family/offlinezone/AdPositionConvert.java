package fm.lizhi.ocean.wave.management.model.converter.family.offlinezone;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.management.config.apollo.CommonConfig;
import fm.lizhi.ocean.wave.management.model.converter.CommonConvert;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.AddAdPositionParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.UpdateAdPositionParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.AddAdPositionResult;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListAdPositionResult;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wave.management.util.UrlUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneAdPosition;
import fm.lizhi.sso.client.SessionUtils;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * 线下专区广告展位转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
                ConfigUtils.class,
                Date.class,
                SessionUtils.class,
                SessionExtUtils.class,
                UrlUtils.class,
        },
        uses = {
                CommonConvert.class,
        }
)
public abstract class AdPositionConvert {

    @Autowired
    protected CommonConfig commonConfig;

    /**
     * 转换为用于新增的广告展位实体
     *
     * @param param 新增参数
     * @return 广告展位实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "appId", expression = "java(SessionExtUtils.getAppId())")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "banner", expression = "java(UrlUtils.removeHostOrEmpty(param.getBanner()))")
    @Mapping(target = "deleted", constant = "false")
    @Mapping(target = "operator", expression = "java(SessionUtils.getAccount())")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    public abstract OfflineZoneAdPosition toCreateAdPositionEntity(AddAdPositionParam param);

    /**
     * 转换为新增广告展位结果
     *
     * @param id 新增的广告展位ID
     * @return 新增结果
     */
    @Mapping(target = "id", source = "id")
    public abstract AddAdPositionResult toAddAdPositionResult(Long id);

    /**
     * 转换为用于更新的广告展位实体
     *
     * @param param 更新参数
     * @return 广告展位实体
     */
    @Mapping(target = "appId", ignore = true)
    @Mapping(target = "deployEnv", ignore = true)
    @Mapping(target = "deleted", constant = "false")
    @Mapping(target = "banner", expression = "java(UrlUtils.removeHostOrEmpty(param.getBanner()))")
    @Mapping(target = "operator", expression = "java(SessionUtils.getAccount())")
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    public abstract OfflineZoneAdPosition toUpdateAdPositionEntity(UpdateAdPositionParam param);

    /**
     * 将广告展位实体列表转换为结果列表
     *
     * @param entities 广告展位实体列表
     * @return 广告展位结果列表
     */
    public abstract List<ListAdPositionResult> toListAdPositionResult(List<OfflineZoneAdPosition> entities);

    @Mapping(target = "banner", source = "entity.banner", qualifiedByName = "addWaveCdnHost")
    protected abstract ListAdPositionResult toListAdPositionResult(OfflineZoneAdPosition entity);

    @Named("addWaveCdnHost")
    protected String addWaveCdnHost(String path) {
        return UrlUtils.addHostOrEmpty(path, commonConfig.getWaveCdn().getCdnHost());
    }
}
