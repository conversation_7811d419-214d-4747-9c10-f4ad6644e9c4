package fm.lizhi.ocean.wave.management.model.result.anchor.singer;

import java.util.List;

import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerSingHallApplyRecordSummaryVO;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 点唱厅考核分页响应
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PageHallApplyResult {

    private List<SingerSingHallApplyRecordSummaryVO> list;

    private int total;
}
