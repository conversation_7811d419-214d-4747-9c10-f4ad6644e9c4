package fm.lizhi.ocean.wave.management.manager.utility.webp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.common.validation.BeanValidator;
import fm.lizhi.ocean.wave.management.common.validation.ValidateResult;
import fm.lizhi.ocean.wave.management.config.apollo.UtilityConfig;
import fm.lizhi.ocean.wave.management.model.converter.utility.webp.WebpConvertConvert;
import fm.lizhi.ocean.wave.management.model.param.utility.webp.WebpConvertParam;
import fm.lizhi.ocean.wave.management.model.param.utility.webp.WebpNodeConvertParam;
import fm.lizhi.ocean.wave.management.model.result.utility.webp.*;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.platform.api.common.service.CommonService;
import fm.lizhi.ocean.wave.platform.api.platform.webp.constant.WebpConvertErrorCodeConstants;
import fm.lizhi.ocean.wave.platform.api.platform.webp.request.RequestGetBizRecord;
import fm.lizhi.ocean.wave.platform.api.platform.webp.request.RequestGetConvertCache;
import fm.lizhi.ocean.wave.platform.api.platform.webp.request.RequestSaveBizRecord;
import fm.lizhi.ocean.wave.platform.api.platform.webp.response.ResponseGetBizRecord;
import fm.lizhi.ocean.wave.platform.api.platform.webp.response.ResponseGetConvertCache;
import fm.lizhi.ocean.wave.platform.api.platform.webp.service.WebpConvertService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.file.Path;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * webp转换管理器. 因为DC项目负担较大, 因此把主要逻辑放在management项目, DC接口仅作为数据存储查询, 后续有必要时再将逻辑下沉到DC.
 */
@Slf4j
@Component
public class WebpConvertManager {

    @Autowired
    private UtilityConfig utilityConfig;

    @Autowired
    private BeanValidator beanValidator;

    @Autowired
    private WebpCdnManager webpCdnManager;

    @Autowired
    private WebpNodeManager webpNodeManager;

    @Autowired
    private WebpConvertConvert webpConvertConvert;

    @Autowired
    private WebpConvertService webpConvertService;

    /**
     * webp转换, 主要逻辑在这里
     *
     * @param param 转换参数
     * @return 转换结果
     */
    public WebpConvertResult webpConvert(WebpConvertParam param) {
        log.info("WebpConvertParam={}", param);
        // 基础参数校验
        ValidateResult validateResult = beanValidator.validate(param);
        if (validateResult.isInvalid()) {
            String invalidMessage = validateResult.getMessage();
            log.info("WebpConvertParam is invalid, param={}, invalidMessage={}", param, invalidMessage);
            return webpConvertConvert.toErrorResult(param, CommonService.PARAM_ERROR, invalidMessage, NumberUtils.LONG_ZERO);
        }
        // 查询是否已存在
        Integer appId = param.getAppId();
        String bizRequestId = param.getBizRequestId();
        ResponseGetBizRecord bizRecord = getBizRecord(appId, bizRequestId);
        if (bizRecord != null) {
            return webpConvertConvert.toExistingResult(param, bizRecord);
        }
        // 执行webp转换
        WebpConvertResult convertResult = doWebpConvert(param);
        // 保存业务记录
        saveBizRecord(convertResult);
        return convertResult;
    }

    private WebpConvertResult doWebpConvert(WebpConvertParam param) {
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            return doWebpConvert(param, stopWatch);
        } catch (RuntimeException e) {
            log.error("webpConvert error, param={}", param, e);
            String errorText;
            if (StringUtils.isNotBlank(e.getMessage())) {
                errorText = "转换异常" + e.getClass().getName() + ": " + e.getMessage();
            } else {
                errorText = "转换异常" + e.getClass().getName();
            }
            return webpConvertConvert.toErrorResult(param, WebpConvertErrorCodeConstants.CONVERT_FAILURE, errorText, stopWatch.getTime(TimeUnit.SECONDS));
        }
    }

    private WebpConvertResult doWebpConvert(WebpConvertParam param, StopWatch stopWatch) {
        Integer appId = param.getAppId();
        String sourcePath = param.getSourcePath();
        String sourceType = param.getSourceType();
        Integer qualityOption = param.getQualityOption();
        // 先尝试下载源文件, 如果失败则返回错误
        CdnDownloadResult sourceDownloadResult = webpCdnManager.downloadByPath(sourcePath, appId);
        if (sourceDownloadResult.isFailure()) {
            if (sourceDownloadResult.isParamError() || sourceDownloadResult.isNotFound()) {
                return webpConvertConvert.toErrorResult(param, CommonService.PARAM_ERROR, "源文件不存在", stopWatch.getTime(TimeUnit.SECONDS));
            } else {
                return webpConvertConvert.toErrorResult(param, WebpConvertErrorCodeConstants.CDN_ERROR, "源文件下载失败", stopWatch.getTime(TimeUnit.SECONDS));
            }
        }
        String sourceSha = sourceDownloadResult.getSha();
        long sourceSize = sourceDownloadResult.getSize();
        // 如果启用了转换缓存, 则先查询转换缓存
        if (utilityConfig.getWebp().isConvertCacheEnabled()) {
            ResponseGetConvertCache convertCache = getConvertCache(appId, sourceType, sourceSha, qualityOption);
            if (convertCache != null) {
                if (Objects.equals(convertCache.getAppId(), appId)) {
                    // 如果转换缓存的appId和当前请求的appId一致, 则直接返回转换结果
                    return webpConvertConvert.toCachedResult(param, sourceSha, sourceSize, convertCache);
                } else {
                    // 如果转换缓存的appId和当前请求的appId不一致, 则需要转存webp文件
                    CdnDownloadResult cdnDownloadResult = webpCdnManager.downloadByPath(convertCache.getWebpPath(), convertCache.getAppId());
                    if (cdnDownloadResult.isSuccess()) {
                        Path webpLocalPath = cdnDownloadResult.getLocalPath();
                        CdnUploadResult cdnUploadResult = webpCdnManager.uploadWebp(webpLocalPath, appId);
                        if (cdnUploadResult.isFailure()) {
                            // 如果转存失败则后续重新转换再上传也很可能失败, 因此直接返回失败
                            return webpConvertConvert.toErrorResult(param, WebpConvertErrorCodeConstants.ROME_FS_ERROR, "上传webp文件失败", stopWatch.getTime(TimeUnit.SECONDS));
                        }
                        // 使用转存后的webp文件作为结果, 使用上一次的耗时作为耗时
                        Long existingCostSeconds = convertCache.getCostSeconds();
                        return webpConvertConvert.toSuccessResult(param, sourceSha, sourceSize, cdnUploadResult, existingCostSeconds);
                    }
                    // 如果转换缓存下载失败, 则重新走转换流程
                }
            }
        }
        // 调用node服务转换
        WebpNodeConvertParam nodeConvertParam = webpConvertConvert.toNodeConvertParam(param);
        WebpNodeResult<WebpNodeConvertResult> webpNodeResult = webpNodeManager.webpConvert(nodeConvertParam);
        if (webpNodeResult.isTimeout()) {
            return webpConvertConvert.toTimeoutResult(param, webpNodeResult.getRCode(), webpNodeResult.getMsg(), stopWatch.getTime(TimeUnit.SECONDS));
        }
        if (webpNodeResult.isFailure()) {
            return webpConvertConvert.toErrorResult(param, webpNodeResult.getRCode(), webpNodeResult.getMsg(), stopWatch.getTime(TimeUnit.SECONDS));
        }
        // 上传转换后的webp文件
        String webpBase64 = webpNodeResult.getData().getFileBase64();
        CdnUploadResult cdnUploadResult = webpCdnManager.uploadWebpByBase64(webpBase64, appId);
        if (cdnUploadResult.isFailure()) {
            return webpConvertConvert.toErrorResult(param, WebpConvertErrorCodeConstants.ROME_FS_ERROR, "上传webp文件失败", stopWatch.getTime(TimeUnit.SECONDS));
        }
        return webpConvertConvert.toSuccessResult(param, sourceSha, sourceSize, cdnUploadResult, stopWatch.getTime(TimeUnit.SECONDS));
    }

    private ResponseGetBizRecord getBizRecord(int appId, String bizRequestId) {
        RequestGetBizRecord req = new RequestGetBizRecord();
        req.setAppId(appId);
        req.setBizRequestId(bizRequestId);
        Result<ResponseGetBizRecord> result = webpConvertService.getBizRecord(req);
        if (ResultUtils.isSuccess(result)) {
            ResponseGetBizRecord resp = result.target();
            log.info("getBizRecord success, req={}, resp={}", req, resp);
            return resp;
        }
        int rCode = result.rCode();
        if (rCode == WebpConvertService.GET_BIZ_RECORD_NOT_EXISTS) {
            log.info("getBizRecord not exists, req={}", req);
        } else {
            log.warn("getBizRecord failed, req={}, rCode={}", req, rCode);
        }
        return null;
    }

    private ResponseGetConvertCache getConvertCache(int appId, String sourceType, String sourceSha, int qualityOption) {
        RequestGetConvertCache req = new RequestGetConvertCache();
        req.setAppId(appId);
        req.setSourceType(sourceType);
        req.setSourceSha(sourceSha);
        req.setQualityOption(qualityOption);
        Result<ResponseGetConvertCache> result = webpConvertService.getConvertCache(req);
        if (ResultUtils.isSuccess(result)) {
            ResponseGetConvertCache resp = result.target();
            log.info("getConvertCache success, req={}, resp={}", req, resp);
            return resp;
        }
        int rCode = result.rCode();
        if (rCode == WebpConvertService.GET_CONVERT_CACHE_NOT_EXISTS) {
            log.info("getConvertCache not exists, req={}", req);
        } else {
            log.warn("getConvertCache failed, req={}, rCode={}", req, rCode);
        }
        return null;
    }

    private void saveBizRecord(WebpConvertResult convertResult) {
        RequestSaveBizRecord req = webpConvertConvert.toRequestSaveBizRecord(convertResult);
        Result<Void> result = webpConvertService.saveBizRecord(req);
        if (ResultUtils.isSuccess(result)) {
            log.info("saveBizRecord success, req={}", req);
        } else {
            int rCode = result.rCode();
            log.warn("saveBizRecord failed, req={}, rCode={}", req, rCode);
        }
    }
}
