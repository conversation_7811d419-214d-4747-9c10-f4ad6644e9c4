package fm.lizhi.ocean.wave.management.model.vo.award.family;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/26 17:15
 */
@Data
public class AwardDeliverRecordV1ExcelVO {
    /**
     * 奖励发放记录ID
     */
    @ExcelProperty("奖励发放记录ID")
    private String id;

    /**
     * 公会id
     */
    @ExcelProperty("公会ID")
    private String familyId;

    /**
     * 公会名称
     */
    @ExcelProperty("公会名称")
    private String familyName;

    /**
     * 公会长用户id
     */
    @ExcelProperty("公会长用户ID")
    private String familyUserId;

    /**
     * 公会长昵称
     */
    @ExcelProperty("公会长用户名")
    private String familyUserName;

    /**
     * 发放周期开始时间, 毫秒时间戳, 周一的00:00:00.000
     */
    @ExcelProperty("发放周期开始时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private Date awardStartTime;

    /**
     * 发放周期结束时间, 毫秒时间戳, 周日的23:59:59.999
     */
    @ExcelProperty("发放周期结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private Date awardEndTime;

    /**
     * 发放时间, 毫秒时间戳
     */
    @ExcelProperty("发放时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private Date deliverTime;

    /**
     * 发放状态, 1-发放中, 2-发放成功, 3-发放失败, 4-部分失败
     */
    @ExcelProperty("发放状态")
    private String statusStr;
    @ExcelIgnore
    private Integer status;

    /**
     * 记录生成时间, 毫秒时间戳
     */
    @ExcelProperty("记录生成时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * (陪伴/西米)推荐卡数量
     */
    @ExcelProperty("推荐卡数量")
    private Integer recommendCardNumber;

    /**
     * (陪伴/西米)座驾id
     */
    @ExcelProperty("座驾ID")
    private String vehicleId;

    /**
     * (陪伴/西米)座驾名称
     */
    @ExcelProperty("座驾名称")
    private String vehicleName;

    /**
     * (陪伴/西米)勋章id
     */
    @ExcelProperty("勋章ID")
    private String medalId;

    /**
     * (陪伴/西米)勋章名称
     */
    @ExcelProperty("勋章名称")
    private String medalName;

    /**
     * (陪伴/西米)短号名称
     */
    @ExcelProperty("短号名称")
    private String shortNumberName;
}