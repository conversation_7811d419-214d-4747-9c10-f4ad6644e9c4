package fm.lizhi.ocean.wave.management.model.dto.singer;

import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class UpdateSingerVerifyStatusParamDTO {

    private Long id;

    private Integer currentAuditStatus;

    private Integer targetAuditStatus;

    private Integer targetSingerStatus;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 审核不通过原因
     */
    private String rejectReason;

    /**
     * 预审核不过原因
     */
    private String preAuditRejectReason;

    /**
     * 是否需要更新歌手信息
     */
    private boolean needUpdateSingerInfo;

    /**
     * 通过的歌手类型
     */
    private Integer passSingerType;

    private SingerVerifyRecord singerVerifyRecord;

    /**
     * 通过的曲风
     */
    private String passSongStyle;

    /**
     * 是否是原创歌手
     */
    private boolean originalSinger;



}
