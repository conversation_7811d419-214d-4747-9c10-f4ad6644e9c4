package fm.lizhi.ocean.wave.management.remote.service.activitycenter;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityLevel;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityLevel;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityLevelConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityLevelConfigServiceRemote {


    @Autowired
    private ActivityLevelConfigService activityLevelConfigService;

    /**
     * 保存
     */
    public Result<Boolean> save(RequestSaveActivityLevel request) {
        Result<Boolean> result = activityLevelConfigService.saveLevel(request);
        if (ResultUtils.isFailure(result)){
            log.warn("save activity level fail, request: {}, rCode: {}", request, result.rCode());
        }

        return result;
    }

    /**
     * 更新
     */
    public Result<Boolean> update(RequestUpdateActivityLevel request) {
        Result<Boolean> result = activityLevelConfigService.updateLevel(request);
        if (ResultUtils.isFailure(result)){
            log.warn("update activity level fail, request: {}, rCode: {}", request, result.rCode());
        }
        return result;
    }

    /**
     * 删除
     */
    public Result<Boolean> delete(Long id, Integer appId, String operator) {
        Result<Boolean> result = activityLevelConfigService.deleteLevel(id, appId, operator);
        if (ResultUtils.isFailure(result)){
            log.warn("delete activity level fail, id: {}, appId:{}, operator:{} rCode: {}", id, appId, operator, result.rCode());
        }
        return result;
    }

    /**
     * 列表
     */
    public Result<List<ActivityLevelConfigBean>> list(Integer appId) {
        Result<List<ActivityLevelConfigBean>> result = activityLevelConfigService.listByAppId(appId);
        if (ResultUtils.isFailure(result)){
            log.warn("list activity level fail, appId: {}, rCode: {}", appId, result.rCode());
        }
        return result;
    }


    /**
     * 根据分类 ID 查询等级
     */
    public Result<ActivityLevelConfigBean> getLevelByClassId(Long classId, Integer appId) {
        Result<ActivityLevelConfigBean> result = activityLevelConfigService.getLevelByClassId(classId, appId);
        if (ResultUtils.isFailure(result)){
            log.warn("getLevelByClassId fail, classId: {}, appId: {}, rCode: {}", classId, appId, result.rCode());
        }
        return result;
    }


}
