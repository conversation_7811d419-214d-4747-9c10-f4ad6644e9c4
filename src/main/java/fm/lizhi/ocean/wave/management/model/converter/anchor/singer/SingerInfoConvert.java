package fm.lizhi.ocean.wave.management.model.converter.anchor.singer;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerInfoDTO;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.BatchImportSingerVO;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.EliminateSingerParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.PageSingerInfoParam;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.UpgradeSingerParam;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.PageSingerInfoResult;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.SingerHallSummaryInfoResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerDetailInfoExcelVO;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerRecordOperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestEliminateSinger;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestPageSingerInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestUpgradeSinger;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetAllSingerStatics;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponsePageSingerInfo;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.ImportSingerInfoBean;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.ImportSingerInfoResultBean;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.request.RequestBatchImportSinger;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
imports = {
    SingerTypeEnum.class,
    SingerStatusEnum.class,
    java.text.SimpleDateFormat.class
})
public interface SingerInfoConvert {
    
    SingerInfoConvert INSTANCE = Mappers.getMapper(SingerInfoConvert.class);

    @Mapping(target = "singerType", expression = "java(SingerTypeEnum.getByType(param.getSingerType()))")
    RequestPageSingerInfo convertRequestPageSingerInfo(PageSingerInfoParam param, Integer appId);

    PageVO<PageSingerInfoResult> convertPageSingerInfoResult(PageBean<ResponsePageSingerInfo> target);

    RequestEliminateSinger convertRequestEliminateSinger(EliminateSingerParam param, Integer appId, String operator, boolean isManual);

    @Mapping(target = "singerType", expression = "java(SingerTypeEnum.getByType(param.getSingerType()))")
    RequestUpgradeSinger convertRequestUpgradeSinger(UpgradeSingerParam param, Integer appId, String operator);

    @Mappings({
        @Mapping(source = "singerInfo.id", target = "singerUserId"),
        @Mapping(source = "singerInfo.name", target = "singerName"),
        @Mapping(source = "singerInfo.band", target = "singerBand"),
        @Mapping(target = "singerGender", expression = "java(source.getSingerInfo().getGender() == 1 ? \"女\" : \"男\")"),
        @Mapping(source = "familyInfo.familyName", target = "familyName"),
        @Mapping(source = "njInfo.id", target = "njId"),
        @Mapping(source = "njInfo.name", target = "njName"),
        @Mapping(source = "njInfo.band", target = "njBand"),
        @Mapping(target = "originalSinger", expression = "java(source.getOriginalSinger() ? \"是\" : \"否\")"),
        @Mapping(target = "rewardsIssued", expression = "java(source.getRewardsIssued() ? \"是\" : \"否\")"),
        @Mapping(target = "eliminationTime", expression = "java(source.getEliminationTime() != null ? new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\").format(new java.util.Date(source.getEliminationTime())) : \"\")"),
        @Mapping(target = "auditTime", expression = "java(source.getAuditTime() != null ? new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\").format(new java.util.Date(source.getAuditTime())) : \"\")"),
        @Mapping(target = "singerStatus", expression = "java(source.getSingerStatus() == 1 ? \"认证中\" : source.getSingerStatus() == 2 ? \"生效中\" : \"已淘汰\")")
    })
    SingerDetailInfoExcelVO convertPageSingerInfoResult(PageSingerInfoResult source);
    
    List<SingerDetailInfoExcelVO> convertPageSingerInfoResult(List<PageSingerInfoResult> list);

    default List<SingerStatusEnum> integerListToSingerStatusEnumList(List<Integer> list){
        if (CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list.stream().map(SingerStatusEnum::getByStatus).collect(Collectors.toList());
    }

    SingerHallSummaryInfoResult convertSingerHallSummaryInfoResult(ResponseGetAllSingerStatics target);

    @Mapping(target = "modifyTime", ignore = true)
    @Mapping(target = "deployEnv", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    SingerInfo convertSingerInfo(SingerInfo singerInfoDTO);

    default SingerInfo buildSingerInfo(SingerVerifyRecord singerVerifyRecord, SingerInfo singerInfoDTO,
                                       Integer singerStatus, Integer passSingerType, String operator, String passSongStyle, boolean originalSinger) {
        SingerInfo singerInfo = new SingerInfo();
        if (singerInfoDTO != null) {
            singerInfo = convertSingerInfo(singerInfoDTO);
            singerInfo.setSingerVerifyId(singerVerifyRecord.getId());
            singerInfo.setSingerStatus(singerStatus);
            singerInfo.setNjId(singerVerifyRecord.getNjId());
            singerInfo.setFamilyId(singerVerifyRecord.getFamilyId());
            singerInfo.setOriginalSinger(originalSinger);
            singerInfo.setSongStyle(passSongStyle);
            singerInfo.setAuditTime(singerStatus == SingerStatusEnum.EFFECTIVE.getStatus() ? new Date() : null);
            singerInfo.setEliminationTime(singerStatus != SingerStatusEnum.ELIMINATED.getStatus() ? null : new Date());
            singerInfo.setEliminationReason(singerStatus != SingerStatusEnum.ELIMINATED.getStatus() ? "" : singerInfoDTO.getEliminationReason());
            singerInfo.setOperator(operator);
            singerInfo.setContactNumber(singerVerifyRecord.getContactNumber());
            return singerInfo;
        }

        singerInfo.setSingerVerifyId(singerVerifyRecord.getId());
        singerInfo.setContactNumber(singerVerifyRecord.getContactNumber());
        singerInfo.setAppId(singerVerifyRecord.getAppId());
        singerInfo.setUserId(singerVerifyRecord.getUserId());
        singerInfo.setSingerStatus(singerStatus);
        singerInfo.setOriginalSinger(originalSinger);
        singerInfo.setSongStyle(passSongStyle);
        singerInfo.setRewardsIssued(false);
        singerInfo.setSingerType(passSingerType);
        singerInfo.setAuditTime(singerStatus == SingerStatusEnum.EFFECTIVE.getStatus() ? new Date() : null);
        singerInfo.setEliminationTime(singerStatus != SingerStatusEnum.ELIMINATED.getStatus() ? null : new Date());
        singerInfo.setEliminationReason("");
        singerInfo.setOperator(operator);
        singerInfo.setCreateTime(singerVerifyRecord.getCreateTime());
        singerInfo.setModifyTime(singerVerifyRecord.getModifyTime());
        singerInfo.setNjId(singerVerifyRecord.getNjId());
        singerInfo.setFamilyId(singerVerifyRecord.getFamilyId());
        singerInfo.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return singerInfo;
    }

    default SingerRecordOperateTypeEnum singerStatusConvertOperateType(Integer singerStatus) {
        if (singerStatus == SingerStatusEnum.EFFECTIVE.getStatus()) {
            return SingerRecordOperateTypeEnum.PASS;
        }
        if (singerStatus == SingerStatusEnum.ELIMINATED.getStatus()) {
            return SingerRecordOperateTypeEnum.ELIMINATE;
        }
        return null;
    }

    @Mapping(target = "eliminationTime", expression = "java(singerInfo.getEliminationTime() != null ? singerInfo.getEliminationTime().getTime() : 0L)")
    @Mapping(target = "auditTime", expression = "java(singerInfo.getAuditTime() != null ? singerInfo.getAuditTime().getTime() : 0L)")
    @Mapping(target = "createTime", expression = "java(singerInfo.getCreateTime() != null ? singerInfo.getCreateTime().getTime() : 0L)")
    @Mapping(target = "modifyTime", expression = "java(singerInfo.getModifyTime() != null ? singerInfo.getModifyTime().getTime() : 0L)")
    SingerInfoDTO convertSingerInfoDTO(SingerInfo singerInfo);

    RequestBatchImportSinger toRequestBatchImportSinger(List<ImportSingerInfoBean> singerInfoList, Integer appId, String operator);

    @Mapping(target = "failImportSingerInfoList", source = "failImportSingerInfoList")
    List<BatchImportSingerVO.FailImportSingerInfo> toBatchImportSingerVO(List<ImportSingerInfoResultBean> failImportSingerInfoList);
}