package fm.lizhi.ocean.wave.management.controller.grow.tasktemplate;

import fm.lizhi.ocean.wave.management.model.param.grow.tasktemplate.DeleteTaskTemplateParam;
import fm.lizhi.ocean.wave.management.model.param.grow.tasktemplate.TaskTemplateManageListParam;
import fm.lizhi.ocean.wave.management.model.vo.grow.capability.AssessMetricVO;
import fm.lizhi.ocean.wave.management.model.vo.grow.tasktemplate.TaskTemplateManageListVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.grow.ability.constants.AssessMetricEnum;
import fm.lizhi.sso.client.SessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import fm.lizhi.ocean.wave.management.manager.grow.tasktemplate.TaskTemplateManager;
import fm.lizhi.ocean.wave.management.model.param.grow.tasktemplate.TaskTemplateSaveParam;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;

import java.util.ArrayList;
import java.util.List;

/**
 * 任务模板 Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping("/grow/taskTemplate")
public class TaskTemplateController {

    @Autowired
    private TaskTemplateManager taskTemplateManager;

    /**
     * 返回所有支持的指标
     * @return
     */
    @GetMapping("/metricList")
    public ResultVO<List<AssessMetricVO>> metricList(){
        Integer appId = SessionExtUtils.getAppId();
        BusinessEvnEnum businessEvnEnum = BusinessEvnEnum.from(appId);
        List<AssessMetricVO> metricList = new ArrayList<>();
        for (AssessMetricEnum assessMetricEnum : AssessMetricEnum.values()) {
            metricList.add(new AssessMetricVO().setMetricCode(assessMetricEnum.getCode()).setMetricName(assessMetricEnum.getName(businessEvnEnum)));
        }
        return ResultVO.success(metricList);
    }

    /**
     * 保存任务模板
     * @param param 保存参数
     * @return 只返回状态码，0为成功
     */
    @PostMapping("/save")
    public ResultVO<Void> save(@RequestBody TaskTemplateSaveParam param) {
        return taskTemplateManager.saveTaskTemplate(param);
    }

    /**
     * 查询任务模板管理列表
     * @param param 查询参数
     * @return 任务模板管理列表
     */
    @GetMapping("/manageList")
    public ResultVO<TaskTemplateManageListVO> manageList(TaskTemplateManageListParam param) {
        return taskTemplateManager.manageList(param);
    }

    /**
     * 删除任务模板
     * @return 只返回状态码，0为成功，失败400
     */
    @PostMapping("/delete")
    public ResultVO<Void> delete(@Validated @RequestBody DeleteTaskTemplateParam param) {
        return taskTemplateManager.deleteGrowTaskTemplate(param.getIds());
    }
} 