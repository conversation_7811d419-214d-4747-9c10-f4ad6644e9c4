package fm.lizhi.ocean.wave.management.model.param.anchor.singer;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 晋升歌手
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class UpgradeSingerParam {

    /**
     * 歌手库ID
     */
    private List<Long> ids;

    /**
     * 晋升类型
     */
    private int singerType;
}
