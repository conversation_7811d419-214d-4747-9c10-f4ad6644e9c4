package fm.lizhi.ocean.wave.management.model.converter.family.offlinezone;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wave.management.model.converter.CommonConvert;
import fm.lizhi.ocean.wave.management.model.dto.family.offlinezone.ListPlayerDataDTO;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.BatchResetPlayerDataParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListPlayerDataResult;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestBatchResetAgreementValidity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        uses = CommonConvert.class
)
public interface PlayerDataConvert {

    PlayerDataConvert I = Mappers.getMapper(PlayerDataConvert.class);

    @Mapping(target = "playerInfo.id", source = "playerBean.id")
    @Mapping(target = "playerInfo.name", source = "playerBean.name")
    @Mapping(target = "playerInfo.band", source = "playerBean.band")
    @Mapping(target = "njInfo.id", source = "njBean.id")
    @Mapping(target = "njInfo.name", source = "njBean.name")
    @Mapping(target = "njInfo.band", source = "njBean.band")
    @Mapping(target = "familyInfo.id", source = "playerDataDTO.familyId")
    @Mapping(target = "familyInfo.name", source = "playerDataDTO.familyName")
    @Mapping(target = "id", source = "playerDataDTO.id")
    ListPlayerDataResult convertListPlayerDataResult(ListPlayerDataDTO playerDataDTO, UserBean playerBean, UserBean njBean);

    default List<ListPlayerDataResult> convertListPlayerDataResultList(PageList<ListPlayerDataDTO> pageList, List<UserBean> userBeanList){
        if (CollectionUtils.isEmpty(pageList)) {
            return Collections.emptyList();
        }

        Map<Long, UserBean> userBeanMap = userBeanList.stream().collect(Collectors.toMap(UserBean::getId, Function.identity(), (a, b) -> a));

        return pageList.stream().map(dto -> {
            return convertListPlayerDataResult(dto,
                    MapUtils.getObject(userBeanMap, dto.getUserId()),
                    MapUtils.getObject(userBeanMap, dto.getNjId())
            );
        }).collect(Collectors.toList());


    }

    RequestBatchResetAgreementValidity buildBatchResetAgreementValidity(BatchResetPlayerDataParam param, Integer appId, String operator);
}
