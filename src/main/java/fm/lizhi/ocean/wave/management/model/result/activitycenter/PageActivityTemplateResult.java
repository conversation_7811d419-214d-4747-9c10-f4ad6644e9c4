package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 分页查询活动模板结果
 */
@Data
public class PageActivityTemplateResult {

    /**
     * 活动模板id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 大类id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bigClassId;

    /**
     * 大类名称
     */
    private String bigClassName;

    /**
     * 分类id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    /**
     * 分类名称
     */
    private String className;

    /**
     * 是否已删除
     */
    private Boolean deleted;

    /**
     * 封面地址
     */
    private String cover;

    /**
     * 上下架状态
     */
    private Integer status;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 是否热门推荐
     */
    private Boolean hotRec;

    /**
     * 热门推荐权重
     */
    private Integer hotWeight;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 操作人
     */
    private String operator;
}
