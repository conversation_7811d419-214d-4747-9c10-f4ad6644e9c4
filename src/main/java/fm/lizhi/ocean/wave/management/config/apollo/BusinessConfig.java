package fm.lizhi.ocean.wave.management.config.apollo;

import fm.lizhi.ocean.lamp.common.config.annotation.JsonStringProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 业务配置
 */
@ConfigurationProperties(prefix = "business")
@Data
public class BusinessConfig {

    /**
     * 业务配置, 按json数组配置
     */
    @JsonStringProperty
    private List<BusinessProperties> configs = Collections.emptyList();

    /**
     * 升级配置，按json数组配置
     */
    @JsonStringProperty
    private List<BusinessUpgradeProperties> upgradeConfigs = Collections.emptyList();


    /**
     * 根据应用id获取业务配置
     *
     * @param appId 应用id
     * @return 业务配置
     */
    public BusinessProperties getPropertiesByAppId(Integer appId) {
        for (BusinessProperties properties : CollectionUtils.emptyIfNull(configs)) {
            if (Objects.equals(appId, properties.getAppId())) {
                return properties;
            }
        }
        throw new IllegalStateException("未找到应用id为" + appId + "的业务配置");
    }

    /**
     * 校验是否升级中
     *
     * @param uri uri
     * @return left: true: 拦截，false: 不拦截， right: 说明
     */
    public Pair<Boolean, String> checkUpgrade(String uri) {
        if (CollectionUtils.isEmpty(upgradeConfigs)) {
            return Pair.of(false, "");
        }
        Optional<BusinessUpgradeProperties> first = upgradeConfigs.stream()
                .filter(config -> uri.startsWith(config.getUri()))
                .findFirst();
        if (!first.isPresent() || first.get().getStartTime() == null || first.get().getEndTime() == null) {
            return Pair.of(false, "");
        }
        BusinessUpgradeProperties config = first.get();
        //判断是否开启
        long now = System.currentTimeMillis();
        boolean filterReq = config.isEnabled() && now >= config.getStartTime().getTime() && now <= config.getEndTime().getTime();
        return Pair.of(filterReq, config.getUpgradeRemark());
    }
}
