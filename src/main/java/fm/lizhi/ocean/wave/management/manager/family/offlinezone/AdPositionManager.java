package fm.lizhi.ocean.wave.management.manager.family.offlinezone;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.management.datastore.dao.family.offlinezone.AdPositionDao;
import fm.lizhi.ocean.wave.management.model.converter.family.offlinezone.AdPositionConvert;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.AddAdPositionParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.DeleteAdPositionParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.ListAdPositionParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.UpdateAdPositionParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.AddAdPositionResult;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListAdPositionResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneAdPosition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 线下专区广告展位管理器
 */
@Component
@Slf4j
public class AdPositionManager {

    @Autowired
    private AdPositionConvert adPositionConvert;

    @Autowired
    private AdPositionDao adPositionDao;

    /**
     * 新增广告展位
     *
     * @param param 新增参数
     * @return 新增结果的VO
     */
    public ResultVO<AddAdPositionResult> addAdPosition(AddAdPositionParam param) {
        long id = adPositionDao.addAdPosition(param);
        AddAdPositionResult result = adPositionConvert.toAddAdPositionResult(id);
        return ResultVO.success(result);
    }

    /**
     * 更新广告展位
     *
     * @param param 更新参数
     * @return 更新结果的VO
     */
    public ResultVO<Void> updateAdPosition(UpdateAdPositionParam param) {
        Long id = param.getId();
        OfflineZoneAdPosition oldAdPosition = adPositionDao.getAdPosition(id);
        if (oldAdPosition == null) {
            return ResultVO.failure("广告展位ID不存在: " + id);
        }
        if (!Objects.equals(oldAdPosition.getDeployEnv(), ConfigUtils.getEnvRequired().name())) {
            return ResultVO.failure("广告展位ID不属于当前环境: " + id);
        }
        if (oldAdPosition.getDeleted()) {
            return ResultVO.failure("广告展位ID已被删除: " + id);
        }
        adPositionDao.updateAdPosition(param);
        return ResultVO.success();
    }

    /**
     * 删除广告展位
     *
     * @param param 删除参数
     * @return 删除结果的VO
     */
    public ResultVO<Void> deleteAdPosition(DeleteAdPositionParam param) {
        Long id = param.getId();
        OfflineZoneAdPosition oldAdPosition = adPositionDao.getAdPosition(id);
        if (oldAdPosition == null) {
            return ResultVO.failure("广告展位ID不存在: " + id);
        }
        if (!Objects.equals(oldAdPosition.getDeployEnv(), ConfigUtils.getEnvRequired().name())) {
            return ResultVO.failure("广告展位ID不属于当前环境: " + id);
        }
        if (oldAdPosition.getDeleted()) {
            return ResultVO.failure("广告展位ID已被删除: " + id);
        }
        adPositionDao.deleteAdPosition(param);
        return ResultVO.success();
    }

    /**
     * 列出广告展位
     *
     * @param param 列出参数
     * @return 广告展位列表的VO
     */
    public ResultVO<List<ListAdPositionResult>> listAdPosition(ListAdPositionParam param) {
        List<OfflineZoneAdPosition> entities = adPositionDao.listAdPosition(param);
        List<ListAdPositionResult> results = adPositionConvert.toListAdPositionResult(entities);
        return ResultVO.success(results);
    }
}
