package fm.lizhi.ocean.wave.management.common.aspect;

import fm.lizhi.ocean.wave.management.config.apollo.SsoClientConfig;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * 鉴权切面
 */
@Aspect
@Slf4j
public class AuthenticationAspect {

    /**
     * 跳过鉴权的路径模式
     */
    private static final List<String> EXCLUDE_AUTHENTICATE_PATTERNS = Arrays.asList(
            "/system/**",
            "/waveVersion/**"
    );

    /**
     * 路径匹配器
     */
    private static final PathMatcher PATH_MATCHER = new AntPathMatcher();

    @Autowired
    private SsoClientConfig ssoClientConfig;

    /**
     * 对所有返回值为ResultVO的RestController方法进行鉴权, 如果没有appId请求头则提前返回
     *
     * @param joinPoint 切点
     * @return 如果认证不通过, 返回认证不通过的结果, 否则返回原方法的结果
     * @throws Throwable 异常
     */
    @Around("@within(org.springframework.web.bind.annotation.RestController)" +
            " && execution(public fm.lizhi.ocean.wave.management.model.vo.ResultVO *(..))")
    public Object authenticateAppId(ProceedingJoinPoint joinPoint) throws Throwable {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (!(requestAttributes instanceof ServletRequestAttributes)) {
            log.info("RequestAttributes is not instance of ServletRequestAttributes, skip authenticate.");
            return joinPoint.proceed();
        }
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        String requestPath = request.getRequestURI().substring(request.getContextPath().length());
        for (String pattern : EXCLUDE_AUTHENTICATE_PATTERNS) {
            if (PATH_MATCHER.match(pattern, requestPath)) {
                log.debug("requestPath={} match excludeAuthenticatePattern={}, skip authenticate.", requestPath, pattern);
                return joinPoint.proceed();
            }
        }
        String[] excludeLoginUrlPattern = ssoClientConfig.getExcludeLoginUrlPattern();
        if (ArrayUtils.isNotEmpty(excludeLoginUrlPattern)) {
            for (String pattern : excludeLoginUrlPattern) {
                if (PATH_MATCHER.match(pattern, requestPath)) {
                    log.debug("requestPath={} match excludeLoginUrlPattern={}, skip authenticate.", requestPath, pattern);
                    return joinPoint.proceed();
                }
            }
        }
        Integer appId = SessionExtUtils.getAppId();
        if (appId == null) {
            log.info("Cannot get appId from request header {}", SessionExtUtils.APP_ID_HEADER);
            return ResultVO.failure("没有合法的应用请求头值, key=" + SessionExtUtils.APP_ID_HEADER);
        }
        List<Integer> authorizedAppIds = SessionExtUtils.getAuthorizedAppIds();
        if (!authorizedAppIds.contains(appId)) {
            log.info("User {} is not authorized for appId {}", SessionUtils.getAccount(), appId);
            return ResultVO.failure("当前用户没有权限访问该应用, appId=" + appId);
        }
        return joinPoint.proceed();
    }
}
