package fm.lizhi.ocean.wave.management.manager.singer.handler;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.management.manager.singer.SingerVerifyApplyManager;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerVerifyConvert;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerAuditParamDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerExecuteAuditDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.UpdateSingerVerifyStatusParamDTO;
import fm.lizhi.ocean.wave.management.remote.service.anchor.singer.SingerChatRemote;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerChatSceneEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 歌手认证拒绝处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingerAuditRejectHandler extends AbstractSingerAuditHandler {

    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;

    @Autowired
    private SingerChatRemote singerChatRemote;

    @Override
    public SingerExecuteAuditDTO auditHandle(SingerAuditParamDTO param, SingerVerifyRecord verifyRecord) {
        // 如果原状态是选中，需要修改歌手库的状态
        Integer targetSingerStatus = verifyRecord.getAuditStatus() == SingerAuditStatusEnum.SELECTED.getStatus() ? SingerStatusEnum.ELIMINATED.getStatus() : null;
        UpdateSingerVerifyStatusParamDTO paramDTO = SingerVerifyConvert.INSTANCE.buildUpdateParam(verifyRecord, param, SingerAuditStatusEnum.REJECTED.getStatus(),
                targetSingerStatus, targetSingerStatus != null, verifyRecord.getSingerType());
        boolean updateRes = singerVerifyApplyManager.updateSingerVerifyRecordStatus(paramDTO);
        log.info("SingerAuditRejectHandler.auditHandle param：{},res:{}", JsonUtil.dumps(paramDTO), updateRes);
        if (updateRes) {
            SingerChatSceneEnum chatSceneEnum = verifyRecord.getAuditStatus() == SingerAuditStatusEnum.SELECTED.getStatus()
                    ? SingerChatSceneEnum.SELECTED_THAN_NO_PASS : SingerChatSceneEnum.HUMAN_AUDIT_NOT_PASS;
            singerChatRemote.sendSingerChat(verifyRecord.getAppId(), verifyRecord.getUserId(), verifyRecord.getSingerType(), param.getPassSongStyle(), chatSceneEnum);
        }
        return updateRes ? SingerExecuteAuditDTO.success() : SingerExecuteAuditDTO.failure("修改状态失败，请稍候重试!");
    }

    @Override
    public SingerAuditStatusEnum getAuditStatusEnum() {
        return SingerAuditStatusEnum.REJECTED;
    }
}
