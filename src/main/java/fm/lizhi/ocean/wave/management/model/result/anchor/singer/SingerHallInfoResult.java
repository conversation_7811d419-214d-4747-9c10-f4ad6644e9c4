package fm.lizhi.ocean.wave.management.model.result.anchor.singer;

import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.RoomDetailUserInfoVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerFamilyInfoVO;
import lombok.Data;

@Data
public class SingerHallInfoResult {

    /**
     * 家族信息
     */ 
    private SingerFamilyInfoVO familyInfo;

    /**
     * 高级歌手认证数量
     */
    private int seniorSingerAuthCnt;

    /**
     * 歌手认证数量
     */
    private int singerAuthCnt;

    /**
     * 营收(钻石)
     */
    private long income;

    /**
     * 歌手信息
     */
    private RoomDetailUserInfoVO njInfo;
}
