package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class GetActivityResourceResult {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 资源code，只有自动配置的资源有
     */
    private String resourceCode;

    /**
     * 资源介绍
     */
    private String introduction;

    /**
     * 资源图片，斜杠开头
     */
    private String imageUrl;

    /**
     * 资源配置类型，1：自动，2：手动
     */
    private Integer deployType;

    /**
     * 状态，0：禁用，1：启用
     */
    private Integer status;

    /**
     * 是否删除 0:未删除 1:已删除
     */
    private Integer deleted;

    /**
     * 是否必选
     */
    private Boolean required;

    /**
     * 操作者
     */
    private String operator;


    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 是否支持上传
     */
    private boolean supportUpload;



}
