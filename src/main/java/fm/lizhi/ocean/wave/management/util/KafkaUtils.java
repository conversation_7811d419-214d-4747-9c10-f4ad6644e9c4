package fm.lizhi.ocean.wave.management.util;

import com.ctrip.framework.apollo.core.enums.Env;
import com.lizhi.commons.config.core.util.ConfigUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * kafka工具类
 */
public class KafkaUtils {

    private static final String ROUTING_SPLITTER = "!%#";

    /**
     * 移除kafka消息中的路由信息. 该路由信息由基础架构的消息中间件添加, 用于测试环境按迭代路由.
     *
     * @param body 消息体
     * @return 去除路由信息后的消息体
     */
    public static String removeBodyRoutingInfo(String body) {
        if (StringUtils.isBlank(body) || StringUtils.startsWith(body, "{")) {
            return body;
        }
        Env env = ConfigUtils.getEnv();
        if (env == Env.TEST || env == Env.LOCAL || env == Env.DEV) {
            int splitterIndex = StringUtils.indexOf(body, ROUTING_SPLITTER);
            if (splitterIndex != StringUtils.INDEX_NOT_FOUND) {
                int pureBodyStart = splitterIndex + ROUTING_SPLITTER.length();
                return StringUtils.substring(body, pureBodyStart);
            }
        }
        return body;
    }
}
