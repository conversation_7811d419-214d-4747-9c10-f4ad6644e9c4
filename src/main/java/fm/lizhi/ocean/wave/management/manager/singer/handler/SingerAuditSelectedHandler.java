package fm.lizhi.ocean.wave.management.manager.singer.handler;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerInfoDao;
import fm.lizhi.ocean.wave.management.manager.singer.SingerVerifyApplyManager;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerVerifyConvert;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerAuditParamDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerExecuteAuditDTO;
import fm.lizhi.ocean.wave.management.model.dto.singer.UpdateSingerVerifyStatusParamDTO;
import fm.lizhi.ocean.wave.management.remote.service.anchor.singer.SingerChatRemote;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerChatSceneEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 歌手认证选中状态处理器
 * 只有高级歌手认证才有选中状态
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingerAuditSelectedHandler extends AbstractSingerAuditHandler {

    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;
    @Autowired
    private SingerInfoDao singerInfoDao;
    @Autowired
    private SingerChatRemote singerChatRemote;

    @Override
    public SingerExecuteAuditDTO auditHandle(SingerAuditParamDTO param, SingerVerifyRecord verifyRecord) {
        //选中操作时，已经是认证歌手了或者歌手在审核中，不能再提交申请
        SingerInfo singerInfo = singerInfoDao.getSingerInfo(verifyRecord.getAppId(), verifyRecord.getUserId(), verifyRecord.getSingerType(), true);
        if (singerInfo != null && (singerInfo.getSingerStatus() == SingerStatusEnum.AUTHENTICATING.getStatus() || singerInfo.getSingerStatus() == SingerStatusEnum.EFFECTIVE.getStatus())) {
            //打印已经是歌手了，不能再提交申请，打印出歌手信息
            log.info("SingerAuditSelectedHandler.auditHandle already singer，singerId:{},singerType：{},singerStatus:{}", singerInfo.getId(), singerInfo.getSingerType(), singerInfo.getSingerStatus());
            return SingerExecuteAuditDTO.failure("用户已经是歌手或者歌手认证中了，不能审批选中");
        }
        // 选中时，歌手库状态设置为认证中
        UpdateSingerVerifyStatusParamDTO paramDTO = SingerVerifyConvert.INSTANCE.buildUpdateParam(verifyRecord, param, SingerAuditStatusEnum.SELECTED.getStatus(),
                SingerStatusEnum.AUTHENTICATING.getStatus(), true, verifyRecord.getSingerType());
        boolean updateRes = singerVerifyApplyManager.updateSingerVerifyRecordStatus(paramDTO);
        log.info("SingerAuditSelectedHandler.auditHandle update singer verify record status, paramDTO:{}, res:{}", JsonUtil.dumps(paramDTO), updateRes);
        if (updateRes) {
            singerChatRemote.sendSingerChat(verifyRecord.getAppId(), verifyRecord.getUserId(), verifyRecord.getSingerType(), param.getPassSongStyle(), SingerChatSceneEnum.SELECTED);
        }
        return updateRes ? SingerExecuteAuditDTO.success() : SingerExecuteAuditDTO.failure("修改状态失败，请稍候重试!");
    }

    @Override
    public SingerAuditStatusEnum getAuditStatusEnum() {
        return SingerAuditStatusEnum.SELECTED;
    }
}
