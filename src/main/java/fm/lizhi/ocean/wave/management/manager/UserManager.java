package fm.lizhi.ocean.wave.management.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.param.user.GetUserParam;
import fm.lizhi.ocean.wave.management.model.vo.user.SearchUserVO;
import fm.lizhi.ocean.wave.management.remote.service.user.UserCommonServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;

import fm.lizhi.ocean.wavecenter.base.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
public class UserManager {

    @Autowired
    private UserCommonServiceRemote userCommonServiceRemote;

    public SearchUserVO getUser(Integer appId, GetUserParam param) {
        Result<UserBean> result = userCommonServiceRemote.getUserByKeyword(param.getKeyword(), appId);
        if (ResultUtils.isFailure(result)) {
            return null;
        }
        UserBean userBean = result.target();
        if (userBean == null) {
            return null;
        }
        SearchUserVO searchUserVO = new SearchUserVO();
        searchUserVO.setUserId(userBean.getId());
        searchUserVO.setUserName(userBean.getName());
        return searchUserVO;
    }

    /**
     * 根据波段号获取用户信息
     *
     * @param appId 应用id
     * @param band  波段号
     * @return 用户信息
     */
    public Optional<UserBean> getUserByBand(int appId, String band){
        Result<UserBean> result = userCommonServiceRemote.getUserByBand(appId, band);
        if (RpcResult.isFail(result)) {
            log.warn("查询用户失败, band: {}", band);
            return Optional.empty();
        }
        return Optional.ofNullable(result.target());
    }

}
