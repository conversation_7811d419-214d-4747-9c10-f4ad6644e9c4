package fm.lizhi.ocean.wave.management.model.param.anchor.singer;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PageSingerInfoParam {


    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于0")
    private int pageNo;

    /**
     * 页数
     */
    @NotNull(message = "页数不能为空")
    @Min(value = 1, message = "页数不能小于0")
    private int pageSize;


    /**
     * 歌手类型
     */
    @NotNull(message = "歌手类型不能为空")
    private int singerType;

    /**
     * 厅ID
     */
     private Long njId;

    /**
     * 主播 ID
     */
    private Long userId;

    /**
     * 起始通过时间
     */
    private Long startAuditTime;

    /**
     * 结束通过时间
     */
    private Long endAuditTime;

    /**
     * 起始淘汰时间
     */
    private Long startEliminationTime;

    /**
     * 结束淘汰时间
     */
    private Long endEliminationTime;

    /**
     * 歌手状态
     */
    private List<Integer> singerStatus;

    /**
     * 歌曲风格
     */
    private List<String> songStyle;

    /**
     * 厅主波段号
     */
    private String njBand;

    /**
     * 歌手波段号
     */
    private String singerBand;

    /**
     * 是否原创歌手
     */
    private Boolean originalSinger;

    /**
     * 排序指标
     */
    private String orderMetrics;

    /**
     * 排序类型
     */
    private String orderType;

    private Boolean whiteListSinger;

}
