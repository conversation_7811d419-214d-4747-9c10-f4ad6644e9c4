package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * 活动分类
 *
 * <AUTHOR>
 * @date 2024-10-10 06:34:31
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ActivityClassConfigResult {

    /**
     * 分类ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 大类ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bigClassId;

    /**
     * 类型名称
     */
    private String name;

    /**
     * 等级ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long levelId;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 操作人
     */
    private String operator;
}