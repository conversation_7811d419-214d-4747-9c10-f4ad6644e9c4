package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description: 用户活动提报信息导出入参
 * @author: ch<PERSON><PERSON><PERSON>
 * @create: 2024/10/23 17:26
 */
@Data
public class QueryUserActivitiesExportParamVO implements QueryUserActivitiesParam{

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 最大活动开始时间
     */
    private Long endTime;

    /**
     * 活动结束时间
     */
    private Long startTime;

    /**
     * 活动名
     */
    private String name;

    /**
     * 提报用户波段号
     */
    private String applyUserBand;

    /**
     * 提报厅主波段号
     */
    private String applyHallBand;

    /**
     * 提报厅ID
     */
    private Long familyId;

    /**
     * 活动提报开始时间
     */
    private Long applyStartTime;

    /**
     * 活动提报结束时间
     */
    private Long applyEndTime;


    /**
     * 活动分类ID
     */
    private List<Long> classId;

    /**
     * 活动状态
     *
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityStatusEnum
     */
    private Integer activityStatus;

    /**
     * 申报类型
     *
     * @see ActivityApplyTypeEnum
     */
    private Integer applyType;
}
