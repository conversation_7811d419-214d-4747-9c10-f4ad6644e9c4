package fm.lizhi.ocean.wave.management.model.converter.activitycenter;

import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveBigClassParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveClassificationParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateBigClassParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateClassificationParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityBigClassResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityClassConfigResult;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityBigClassBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityClassConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityBigClass;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityClassification;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityBigClass;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityClassification;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityClassificationConfigConvert {

    ActivityClassificationConfigConvert I = Mappers.getMapper(ActivityClassificationConfigConvert.class);


    @Mappings({
            @Mapping(target = "appId", source = "appId"),
            @Mapping(target = "operator", source = "operator"),
            @Mapping(target = "name", source = "param.className"),
    })
    RequestSaveActivityBigClass toRequestSaveActivityBigClass(SaveBigClassParam param, Integer appId, String operator);

    @Mappings({
            @Mapping(target = "operator", source = "operator"),
            @Mapping(target = "appId", source = "appId"),
            @Mapping(target = "name", source = "param.className"),
            @Mapping(target = "id", source = "param.id"),
    })
    RequestUpdateActivityBigClass toRequestUpdateActivityBigClass(UpdateBigClassParam param, String operator, Integer appId);

    List<ActivityBigClassResult> toActivityBigClassResults(List<ActivityBigClassBean> target);

    @Mappings({
            @Mapping(target = "operator", source = "operator"),
            @Mapping(target = "appId", source = "appId"),
            @Mapping(target = "name", source = "param.className"),
            @Mapping(target = "bigClassId", source = "param.bigClassId"),
            @Mapping(target = "levelId", source = "param.levelId"),
    })
    RequestSaveActivityClassification toRequestSaveActivityClassification(SaveClassificationParam param, String operator, Integer appId);

    @Mappings({
            @Mapping(target = "operator", source = "operator"),
            @Mapping(target = "appId", source = "appId"),
            @Mapping(target = "name", source = "param.className"),
            @Mapping(target = "id", source = "param.id"),
            @Mapping(target = "levelId", source = "param.levelId"),
    })
    RequestUpdateActivityClassification toRequestUpdateActivityClassification(UpdateClassificationParam param, String operator, Integer appId);

    List<ActivityClassConfigResult> toActivityClassConfigResults(List<ActivityClassConfigBean> target);
}
