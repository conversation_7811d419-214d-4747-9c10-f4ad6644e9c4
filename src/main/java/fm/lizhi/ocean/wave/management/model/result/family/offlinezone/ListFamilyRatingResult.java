package fm.lizhi.ocean.wave.management.model.result.family.offlinezone;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import fm.lizhi.ocean.wave.management.model.vo.family.FamilyVO;
import fm.lizhi.ocean.wave.management.model.vo.family.offlinezone.OfflineLevelInfoVO;
import lombok.Data;

/**
 * 查询公会评级列表结果
 */
@Data
public class ListFamilyRatingResult {
/**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 家族信息
     */
    private FamilyVO familyInfo;


    /**
     * 等级信息
     */
    private OfflineLevelInfoVO levelInfo;

    /**
     * 基地数
     */
    private Integer basicCnt;


    /**
     * 线下厅数
     */
    private Long offlineHallCnt;

    /**
     * 线下厅数占比
     */

    private BigDecimal offlineHallCntRate;

    /**
     * 线下厅收入
     */
    private BigDecimal offlineHallIncome;

    /**
     * 线下厅收入占比
     */
    private BigDecimal offlineHallIncomeRate;

    /**
     * 线下主播数
     */
    private Integer offlinePlayerCnt;

    /**
     * 线下主播数占比
     */
    private BigDecimal offlinePlayerCntRate;

    /**
     * 受保护主播数
     */
    private Integer protectedPlayerCnt;

    /**
     * 受保护主播数占比
     */
    private BigDecimal protectedPlayerCntRate;

    /**
     * 受保护主播数(主播同意时会实时更新)
     */

    private Integer protectedPlayerCntRealTime;

    /**
     * 受保护主播数占比(主播同意时会实时更新)
     */

    private BigDecimal protectedPlayerCntRateRealTime;

    /**
     * 更新时间
     */
    private Long modifyTime;

    /**
     * 操作人
     */
    private String operator;


}