package fm.lizhi.ocean.wave.management.model.param.family.offlinezone;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneLearningClassStatusEnum;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneLearningClassTypeEnum;
import lombok.Data;

/**
 * 学习课堂列表参数
 */
@Data
public class ListLearningClassParam {

    /**
     * 分页页码
     */
    @JsonAlias({"pageNumber", "pageNo"})
    @JsonSetter(nulls = Nulls.SKIP)
    private Integer pageNumber = 1;

    /**
     * 分页大小
     */
    @JsonSetter(nulls = Nulls.SKIP)
    private Integer pageSize = 10;

    /**
     * 标题, 模糊查询
     */
    private String title;

    /**
     * 学习课堂资料类型
     *
     * @see OfflineZoneLearningClassTypeEnum
     */
    private Integer type;

    /**
     * 状态
     *
     * @see OfflineZoneLearningClassStatusEnum
     */
    private Integer status;
}
