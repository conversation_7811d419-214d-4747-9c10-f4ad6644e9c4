package fm.lizhi.ocean.wave.management.controller.award.family;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.award.family.FamilyOtherAwardRuleConvert;
import fm.lizhi.ocean.wave.management.model.param.award.family.ListFamilySpecialRecommendCardParam;
import fm.lizhi.ocean.wave.management.model.param.award.family.UploadFamilySpecialRecommendCardParam;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.award.family.ListFamilySpecialRecommendCardNameVO;
import fm.lizhi.ocean.wave.management.remote.service.award.family.FamilyOtherAwardRuleServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.ListFamilySpecialRecommendCardNameBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公会其他奖励规则
 */
@RestController
@RequestMapping("/award/family/otherAwardRule")
@Slf4j
public class FamilyOtherAwardRuleController {

    @Autowired
    private FamilyOtherAwardRuleServiceRemote familyOtherAwardRuleServiceRemote;

    @PostMapping("/specialRecommendCard/upload")
    public ResultVO<Void> uploadSpecialRecommendCard(@RequestBody UploadFamilySpecialRecommendCardParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("Upload family special recommend card, param={}, appId={}, operator={}", param, appId, operator);
        Result<Void> result = familyOtherAwardRuleServiceRemote.uploadFamilySpecialRecommendCardName(param, appId, operator);
        if (ResultUtils.isFailure(result)) {
            log.info("Upload family special recommend card failed, param={}, rCode={}, msg={}", param, result.rCode(), result.getMessage());
            return ResultVO.failure(result);
        }
        log.info("Upload family special recommend card success");
        return ResultVO.success();
    }

    @PostMapping("/specialRecommendCard/clear")
    public ResultVO<Void> clearSpecialRecommendCard() {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        log.info("Clear family special recommend card, appId={}, operator={}", appId, operator);
        Result<Void> result = familyOtherAwardRuleServiceRemote.clearFamilySpecialRecommendCardName(appId, operator);
        if (ResultUtils.isFailure(result)) {
            log.info("Clear family special recommend card failed, rCode={}, msg={}", result.rCode(), result.getMessage());
            return ResultVO.failure(result);
        }
        log.info("Clear family special recommend card success");
        return ResultVO.success();
    }

    @GetMapping("/specialRecommendCard/list")
    public ResultVO<PageVO<ListFamilySpecialRecommendCardNameVO>> listSpecialRecommendCard(ListFamilySpecialRecommendCardParam param) {
        Integer appId = SessionExtUtils.getAppId();
        log.info("List family special recommend card, param={}, appId={}", param, appId);
        Result<PageBean<ListFamilySpecialRecommendCardNameBean>> result = familyOtherAwardRuleServiceRemote.listFamilySpecialRecommendCardName(param, appId);
        if (ResultUtils.isFailure(result)) {
            log.info("List family special recommend card failed, rCode={}, msg={}", result.rCode(), result.getMessage());
            return ResultVO.failure(result);
        }
        PageBean<ListFamilySpecialRecommendCardNameBean> pageBean = result.target();
        log.info("List family special recommend card success, total={}, listSize={}", pageBean.getTotal(), pageBean.getList().size());
        log.debug("List family special recommend card success, list={}", pageBean.getList());
        List<ListFamilySpecialRecommendCardNameVO> VOS = FamilyOtherAwardRuleConvert.I.toListFamilySpecialRecommendCardNameVOS(pageBean.getList());
        PageVO<ListFamilySpecialRecommendCardNameVO> pageVO = PageVO.of(pageBean.getTotal(), VOS);
        return ResultVO.success(pageVO);
    }
}
