package fm.lizhi.ocean.wave.management.controller.family.offlinezone;

import fm.lizhi.ocean.wave.management.manager.family.offlinezone.AdPositionManager;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.AddAdPositionParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.DeleteAdPositionParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.ListAdPositionParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.UpdateAdPositionParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.AddAdPositionResult;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListAdPositionResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 线下专区广告展位控制器
 */
@RestController
@RequestMapping("/offline/adPosition")
@Slf4j
public class AdPositionController {

    @Autowired
    private AdPositionManager adPositionManager;

    /**
     * 新增广告展位
     *
     * @param param 新增参数
     * @return 新增结果的VO
     */
    @PostMapping("/add")
    public ResultVO<AddAdPositionResult> addAdPosition(@RequestBody @Validated AddAdPositionParam param) {
        return adPositionManager.addAdPosition(param);
    }

    /**
     * 更新广告展位
     *
     * @param param 更新参数
     * @return 更新结果的VO
     */
    @PostMapping("/update")
    public ResultVO<Void> updateAdPosition(@RequestBody @Validated UpdateAdPositionParam param) {
        return adPositionManager.updateAdPosition(param);
    }

    /**
     * 删除广告展位
     *
     * @param param 删除参数
     * @return 删除结果的VO
     */
    @PostMapping("/delete")
    public ResultVO<Void> deleteAdPosition(@RequestBody @Validated DeleteAdPositionParam param) {
        return adPositionManager.deleteAdPosition(param);
    }

    /**
     * 列出广告展位
     *
     * @param param 列出参数
     * @return 广告展位列表的VO
     */
    @GetMapping("/list")
    public ResultVO<List<ListAdPositionResult>> listAdPosition(@Validated ListAdPositionParam param) {
        return adPositionManager.listAdPosition(param);
    }
}
