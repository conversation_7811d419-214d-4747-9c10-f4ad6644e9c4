package fm.lizhi.ocean.wave.management.remote.service.activitycenter;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplatePageBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseCreateActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplateShelfStatus;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityTemplateConfigService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 活动模板配置服务远程处理器
 */
@Component
@Slf4j
public class ActivityTemplateConfigServiceRemote {

    @Autowired
    private ActivityTemplateConfigService activityTemplateConfigService;

    /**
     * 创建活动模板
     *
     * @param req 请求
     * @return 结果
     */
    public Result<ResponseCreateActivityTemplate> createTemplate(RequestCreateActivityTemplate req) {
        log.info("createTemplate req: {}", req);
        Result<ResponseCreateActivityTemplate> result = activityTemplateConfigService.createTemplate(req);
        if (ResultUtils.isSuccess(result)) {
            log.info("createTemplate success, target: {}", result.target());
        } else {
            log.info("createTemplate fail, rCode: {}, message: {}", result.rCode(), result.getMessage());
        }
        return result;
    }

    /**
     * 更新活动模板
     *
     * @param req 请求
     * @return 结果
     */
    public Result<Void> updateTemplate(RequestUpdateActivityTemplate req) {
        log.info("updateTemplate req: {}", req);
        Result<Void> result = activityTemplateConfigService.updateTemplate(req);
        if (ResultUtils.isSuccess(result)) {
            log.info("updateTemplate success");
        } else {
            log.info("updateTemplate fail, rCode: {}, message: {}", result.rCode(), result.getMessage());
        }
        return result;
    }

    /**
     * 删除活动模板
     *
     * @param req 请求
     * @return 结果
     */
    public Result<Void> deleteTemplate(RequestDeleteActivityTemplate req) {
        log.info("deleteTemplate req: {}", req);
        Result<Void> result = activityTemplateConfigService.deleteTemplate(req);
        if (ResultUtils.isSuccess(result)) {
            log.info("deleteTemplate success");
        } else {
            log.info("deleteTemplate fail, rCode: {}, message: {}", result.rCode(), result.getMessage());
        }
        return result;
    }

    /**
     * 更新活动模板上下架状态
     *
     * @param req 请求
     * @return 结果
     */
    public Result<Void> updateTemplateShelfStatus(RequestUpdateActivityTemplateShelfStatus req) {
        log.info("updateTemplateShelfStatus req: {}", req);
        Result<Void> result = activityTemplateConfigService.updateShelfStatus(req);
        if (ResultUtils.isSuccess(result)) {
            log.info("updateTemplateShelfStatus success");
        } else {
            log.info("updateTemplateShelfStatus fail, rCode: {}, message: {}", result.rCode(), result.getMessage());
        }
        return result;
    }

    /**
     * 获取活动模板上下架状态
     *
     * @param templateId 活动模板id
     * @return 结果
     */
    public Result<ResponseGetActivityTemplateShelfStatus> getTemplateShelfStatus(long templateId) {
        log.info("getTemplateShelfStatus templateId: {}", templateId);
        Result<ResponseGetActivityTemplateShelfStatus> result = activityTemplateConfigService.getShelfStatus(templateId);
        if (ResultUtils.isSuccess(result)) {
            log.info("getTemplateShelfStatus success");
        } else {
            log.info("getTemplateShelfStatus fail, rCode: {}, message: {}", result.rCode(), result.getMessage());
        }
        return result;
    }

    /**
     * 分页查询活动模板
     *
     * @param req 请求
     * @return 结果
     */
    public Result<PageBean<ActivityTemplatePageBean>> pageTemplate(RequestPageActivityTemplate req) {
        log.info("pageTemplate req: {}", req);
        Result<PageBean<ActivityTemplatePageBean>> result = activityTemplateConfigService.pageTemplate(req);
        if (ResultUtils.isSuccess(result)) {
            log.info("pageTemplate success, total: {}", result.target().getTotal());
        } else {
            log.info("pageTemplate fail, rCode: {}, message: {}", result.rCode(), result.getMessage());
        }
        return result;
    }

    /**
     * 获取活动模板
     *
     * @param templateId 活动模板id
     * @return 结果
     */
    public Result<ResponseGetActivityTemplate> getTemplate(long templateId) {
        log.info("getTemplate templateId: {}", templateId);
        Result<ResponseGetActivityTemplate> result = activityTemplateConfigService.getTemplate(templateId);
        if (ResultUtils.isSuccess(result)) {
            log.info("getTemplate success");
        } else {
            log.info("getTemplate fail, rCode: {}, message: {}", result.rCode(), result.getMessage());
        }
        return result;
    }
}
