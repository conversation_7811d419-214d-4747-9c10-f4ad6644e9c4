package fm.lizhi.ocean.wave.management.common.validation;

import lombok.Data;

import java.beans.Transient;

/**
 * Bean校验结果
 */
@Data
public class ValidateResult {

    /**
     * 是否校验通过
     */
    private boolean valid;

    /**
     * 错误类型
     */
    private ErrorType errorType;

    /**
     * 校验不通过信息
     */
    private String message;

    /**
     * 校验不通过的路径, 可能是字段名, 也可能是对象名
     */
    private String path;

    /**
     * 格式化后的错误信息
     */
    private String formattedMessage;

    /**
     * 校验通过的结果
     *
     * @return 校验通过的结果
     */
    public static ValidateResult valid() {
        ValidateResult result = new ValidateResult();
        result.setValid(true);
        return result;
    }

    /**
     * 对象校验不通过的结果
     *
     * @param path    错误路径
     * @param message 错误信息
     * @return 对象校验不通过的结果
     */
    public static ValidateResult objectError(String path, String message) {
        ValidateResult result = new ValidateResult();
        result.setValid(false);
        result.setErrorType(ErrorType.OBJECT);
        result.setPath(path);
        result.setMessage(message);
        result.setFormattedMessage("Object " + path + ": " + message);
        return result;
    }

    /**
     * 字段校验不通过的结果
     *
     * @param path    错误路径
     * @param message 错误信息
     * @return 字段校验不通过的结果
     */
    public static ValidateResult fieldError(String path, String message) {
        ValidateResult result = new ValidateResult();
        result.setValid(false);
        result.setErrorType(ErrorType.FIELD);
        result.setPath(path);
        result.setMessage(message);
        result.setFormattedMessage("Field " + path + ": " + message);
        return result;
    }

    /**
     * 通用错误, 没有特定的错误路径
     *
     * @param message 错误信息
     * @return 通用错误
     */
    public static ValidateResult generalError(String message) {
        ValidateResult result = new ValidateResult();
        result.setValid(false);
        result.setErrorType(ErrorType.GENERAL);
        result.setMessage(message);
        result.setFormattedMessage(message);
        return result;
    }

    /**
     * 是否校验不通过
     *
     * @return 是否校验不通过
     */
    @Transient
    public boolean isInvalid() {
        return !valid;
    }
}
