package fm.lizhi.ocean.wave.management.model.param.anchor.singer;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 点唱厅管理-分页查询
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PageHallApplyParam {

    /**
     * 厅ID
     */
    private Long njId;

    /**
     * 厅主波段号
     */
    private String njBand;

    /**
     * 审核状态
     * @see SingerHallApplyStatusEnum
     */
    private Integer auditStatus;

    /**
     * 开始时间戳
     */
    private Long startTime;

    /**
     * 结束时间戳
     */
    private Long endTime;

    /**
     * 排序指标
     */
    private String orderMetrics;

    /**
     * 排序类型
     */
    private String orderType;

    /**
     * 页码
     */
    private Integer pageNo = 1;
    /**
     * 每页数量
     */
    private Integer pageSize = 20;
}
