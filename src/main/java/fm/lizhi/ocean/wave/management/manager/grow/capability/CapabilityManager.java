package fm.lizhi.ocean.wave.management.manager.grow.capability;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.grow.CapabilityConvert;
import fm.lizhi.ocean.wave.management.model.param.grow.capability.SaveCapabilityParam;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.grow.capability.CapabilityManageVO;
import fm.lizhi.ocean.wave.management.remote.grow.capability.CapabilityRemote;
import fm.lizhi.ocean.wavecenter.api.grow.capability.response.ResponseCapability;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 能力项管理器
 * 负责业务逻辑、参数组装、调用 Remote 层
 */
@Slf4j
@Component
public class CapabilityManager {
    @Autowired
    private CapabilityRemote capabilityRemote;

    /**
     * 保存能力项
     *
     * @param param    能力项参数
     * @param appId    应用ID
     * @param operator 操作人
     * @return 统一返回结构
     */
    public ResultVO<Void> saveCapability(SaveCapabilityParam param, Integer appId, String operator) {
        param.setAppId(appId);
        param.setOperator(operator);
        Result<Void> result = capabilityRemote.saveCapability(param);
        if (result.rCode() != 0) {
            log.warn("saveCapability fail, param={}, rCode={}, msg={}", param, result.rCode(), result.getMessage());
            return ResultVO.failure(result.getMessage() != null ? result.getMessage() : "保存失败");
        }
        return ResultVO.success();
    }

    /**
     * 查询能力项列表
     *
     * @param appId 应用ID
     * @return 能力项列表
     */
    public ResultVO<List<CapabilityManageVO>> manageList(Integer appId) {
        Result<List<ResponseCapability>> result = capabilityRemote.queryCapabilityList(appId);
        if (result.rCode() != 0) {
            log.warn("queryCapabilityList fail, appId={}, rCode={}, msg={}", appId, result.rCode(), result.getMessage());
            return ResultVO.failure("查询失败");
        }
        List<CapabilityManageVO> voList = CapabilityConvert.INSTANCE.toCapabilityManageVOList(result.target());
        return ResultVO.success(voList);
    }
} 