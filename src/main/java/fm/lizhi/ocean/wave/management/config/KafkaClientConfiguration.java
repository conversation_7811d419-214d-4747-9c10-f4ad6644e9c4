package fm.lizhi.ocean.wave.management.config;

import fm.lizhi.common.kafka.ioc.api.KafkaTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * kafka客户端配置
 */
@Configuration
public class KafkaClientConfiguration {

    @Bean("publicKafkaTemplate")
    public KafkaTemplate publicKafkaTemplate() {
        return new KafkaTemplate("public-kafka250-bootstrap-server");
    }
}
