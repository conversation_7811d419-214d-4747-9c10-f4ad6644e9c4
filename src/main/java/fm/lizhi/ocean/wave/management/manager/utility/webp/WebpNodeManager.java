package fm.lizhi.ocean.wave.management.manager.utility.webp;

import com.fasterxml.jackson.databind.JavaType;
import fm.lizhi.ocean.wave.management.config.apollo.UtilityConfig;
import fm.lizhi.ocean.wave.management.model.param.utility.webp.WebpNodeConvertParam;
import fm.lizhi.ocean.wave.management.model.result.utility.webp.WebpNodeConvertResult;
import fm.lizhi.ocean.wave.management.model.result.utility.webp.WebpNodeResult;
import fm.lizhi.ocean.wave.management.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import java.net.SocketTimeoutException;
import java.net.URI;

/**
 * Webp转换node服务管理器
 */
@Slf4j
@Component
public class WebpNodeManager {

    @Autowired
    private UtilityConfig utilityConfig;

    @Autowired
    @Qualifier("webpRestTemplate")
    private RestTemplate restTemplate;

    /**
     * 调用node服务进行webp转换
     *
     * @param param 转换参数
     * @return 转换结果
     */
    public WebpNodeResult<WebpNodeConvertResult> webpConvert(WebpNodeConvertParam param) {
        try {
            RequestEntity<WebpNodeConvertParam> requestEntity = RequestEntity
                    .post(URI.create(utilityConfig.getWebp().getNodeServerUrl()))
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(param);
            log.info("Node webpConvert requestEntity: {}", requestEntity);
            JavaType javaType = JsonUtils.constructParametricType(WebpNodeResult.class, WebpNodeConvertResult.class);
            ParameterizedTypeReference<WebpNodeResult<WebpNodeConvertResult>> responseType = ParameterizedTypeReference.forType(javaType);
            ResponseEntity<WebpNodeResult<WebpNodeConvertResult>> responseEntity = restTemplate.exchange(requestEntity, responseType);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (statusCodeValue != HttpStatus.OK.value()) {
                log.error("Node response status code is {}", statusCodeValue);
                return WebpNodeResult.failure(String.format("Node服务状态码为%d", statusCodeValue));
            }
            WebpNodeResult<WebpNodeConvertResult> nodeResult = responseEntity.getBody();
            if (nodeResult == null) {
                log.error("Node response body is null");
                return WebpNodeResult.failure("转换结果为空");
            }
            if (nodeResult.isFailure()) {
                log.info("Node response failed, rCode: {}, msg: {}", nodeResult.getRCode(), nodeResult.getMsg());
                return WebpNodeResult.failure(StringUtils.defaultString(nodeResult.getMsg()));
            }
            log.info("Node webpConvert success");
            log.debug("Node webpConvert result: {}", nodeResult.getData());
            return nodeResult;
        } catch (ResourceAccessException e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                // 判断是否是socket超时异常, 优化错误信息
                log.debug("Node webpConvert timeout, param: {}", param, e);
                log.warn("Node webpConvert timeout, param: {}", param);
                long timeoutSeconds = utilityConfig.getWebp().getRestTemplate().getReadTimeout().toMillis() / 1000;
                return WebpNodeResult.timeout(String.format("转换超时, 超时配置为%d秒", timeoutSeconds));
            } else {
                // node服务抛出异常也会导致ResourceAccessException, 但node服务暂时没空修改, 因此先不考虑网络波动一律视为失败
                log.error("Node webpConvert error, param: {}", param, e);
                return WebpNodeResult.exception(e);
            }
        } catch (RuntimeException e) {
            log.error("Node webpConvert error, param: {}", param, e);
            return WebpNodeResult.exception(e);
        }
    }
}
