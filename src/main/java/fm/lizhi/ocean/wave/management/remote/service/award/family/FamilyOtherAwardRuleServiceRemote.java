package fm.lizhi.ocean.wave.management.remote.service.award.family;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.award.family.FamilyOtherAwardRuleConvert;
import fm.lizhi.ocean.wave.management.model.param.award.family.ListFamilySpecialRecommendCardParam;
import fm.lizhi.ocean.wave.management.model.param.award.family.UploadFamilySpecialRecommendCardParam;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.ListFamilySpecialRecommendCardNameBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestClearFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestListFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestUploadFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.award.family.service.FamilyOtherAwardRuleService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class FamilyOtherAwardRuleServiceRemote {

    @Autowired
    private FamilyOtherAwardRuleService familyOtherAwardRuleService;

    public Result<Void> uploadFamilySpecialRecommendCardName(UploadFamilySpecialRecommendCardParam param, Integer appId, String operator) {
        RequestUploadFamilySpecialRecommendCardName request = FamilyOtherAwardRuleConvert.I.toRequestUploadFamilySpecialRecommendCardName(param, appId, operator);
        return familyOtherAwardRuleService.uploadFamilySpecialRecommendCardName(request);
    }

    public Result<Void> clearFamilySpecialRecommendCardName(Integer appId, String operator) {
        RequestClearFamilySpecialRecommendCardName request = FamilyOtherAwardRuleConvert.I.toRequestClearFamilySpecialRecommendCardName(appId, operator);
        return familyOtherAwardRuleService.clearFamilySpecialRecommendCardName(request);
    }

    public Result<PageBean<ListFamilySpecialRecommendCardNameBean>> listFamilySpecialRecommendCardName(ListFamilySpecialRecommendCardParam param, Integer appId) {
        RequestListFamilySpecialRecommendCardName request = FamilyOtherAwardRuleConvert.I.toRequestListFamilySpecialRecommendCardName(param, appId);
        return familyOtherAwardRuleService.listFamilySpecialRecommendCardName(request);
    }
}
