package fm.lizhi.ocean.wave.management.model.dto.singer;

import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SingerAuditPassedDTO {

    /**
     * 认证记录ID
     */
    private Long recordId;

    /**
     * 当前审核状态
     */
    private Integer currentAuditStatus;

    /**
     * 通过的歌手类型
     */
    private Integer passSingerType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 通过的曲风
     */
    private String passSongStyle;

    /**
     * 是否是原创歌手
     */
    private boolean originalSinger;

    /**
     * 认证记录
     */
    private SingerVerifyRecord singerVerifyRecord;

    /**
     * 歌手信息
     */
    private SingerInfo currentSingerInfo;

    /**
     * 构建审核通过参数
     *
     * @param param        审核参数
     * @param verifyRecord 认证记录
     * @return 审核通过参数
     */
    public static SingerAuditPassedDTO builder(SingerAuditParamDTO param, SingerVerifyRecord verifyRecord, SingerInfo currentSingerInfo) {
        SingerAuditPassedDTO dto = new SingerAuditPassedDTO();
        dto.setRecordId(verifyRecord.getId());
        dto.setCurrentAuditStatus(verifyRecord.getAuditStatus());
        dto.setPassSingerType(param.getSingerType());
        dto.setOperator(param.getOperator());
        dto.setPassSongStyle(param.getPassSongStyle());
        dto.setOriginalSinger(param.isOriginalSinger());
        dto.setSingerVerifyRecord(verifyRecord);
        dto.setCurrentSingerInfo(currentSingerInfo);
        return dto;
    }

}
