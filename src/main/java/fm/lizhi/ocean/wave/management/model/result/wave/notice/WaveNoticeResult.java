package fm.lizhi.ocean.wave.management.model.result.wave.notice;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * Wave公告Result
 */
@Data
public class WaveNoticeResult {
    /** 公告ID */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /** 公告标题 */
    private String title;
    /** 公告内容 */
    private String content;
    /** 是否启用 */
    private Boolean enable;
} 