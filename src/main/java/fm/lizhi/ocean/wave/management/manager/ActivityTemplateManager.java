package fm.lizhi.ocean.wave.management.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ActivityCommonConverter;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ActivityTemplateConverter;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.*;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.*;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.remote.service.activitycenter.ActivityCommonServiceRemote;
import fm.lizhi.ocean.wave.management.remote.service.activitycenter.ActivityTemplateConfigServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetBaseActivityConfig;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplatePageBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseCreateActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplateShelfStatus;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 活动模板管理器
 */
@Component
public class ActivityTemplateManager {

    @Autowired
    private ActivityTemplateConverter activityTemplateConverter;
    @Autowired
    private ActivityTemplateConfigServiceRemote activityTemplateConfigServiceRemote;

    @Autowired
    private ActivityCommonConverter activityCommonConverter;
    @Autowired
    private ActivityCommonServiceRemote activityCommonServiceRemote;

    /**
     * 创建活动模板
     *
     * @param param    参数
     * @param appId    应用id
     * @param operator 操作人
     * @return 结果
     */
    public ResultVO<CreateActivityTemplateResult> createTemplate(CreateActivityTemplateParam param, Integer appId, String operator) {
        RequestCreateActivityTemplate req = activityTemplateConverter.toRequestCreateActivityTemplate(param, appId, operator);
        Result<ResponseCreateActivityTemplate> result = activityTemplateConfigServiceRemote.createTemplate(req);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result);
        }
        ResponseCreateActivityTemplate target = result.target();
        return ResultVO.success(activityTemplateConverter.toCreateActivityTemplateResult(target));
    }

    /**
     * 更新活动模板
     *
     * @param param    参数
     * @param appId    应用id
     * @param operator 操作人
     * @return 结果
     */
    public ResultVO<Void> updateTemplate(UpdateActivityTemplateParam param, Integer appId, String operator) {
        RequestUpdateActivityTemplate req = activityTemplateConverter.toRequestUpdateActivityTemplate(param, appId, operator);
        Result<Void> result = activityTemplateConfigServiceRemote.updateTemplate(req);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result);
        }
        return ResultVO.success();
    }

    /**
     * 删除活动模板
     *
     * @param param    参数
     * @param operator 操作人
     * @return 结果
     */
    public ResultVO<Void> deleteTemplate(DeleteActivityTemplateParam param, String operator) {
        RequestDeleteActivityTemplate req = new RequestDeleteActivityTemplate();
        req.setId(param.getId());
        req.setOperator(operator);
        Result<Void> result = activityTemplateConfigServiceRemote.deleteTemplate(req);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result);
        }
        return ResultVO.success();
    }

    /**
     * 更新活动模板上下架状态
     *
     * @param param    参数
     * @param operator 操作人
     * @return 结果
     */
    public ResultVO<Void> updateShelfStatus(UpdateActivityTemplateShelfStatusParam param, String operator, Integer appId) {
        RequestUpdateActivityTemplateShelfStatus req = activityTemplateConverter.toRequestUpdateActivityTemplateShelfStatus(param, operator, appId);
        Result<Void> result = activityTemplateConfigServiceRemote.updateTemplateShelfStatus(req);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result);
        }
        return ResultVO.success();
    }

    /**
     * 获取活动模板上下架状态
     *
     * @param id 活动模板id
     * @return 结果
     */
    public ResultVO<GetActivityTemplateShelfStatusResult> getShelfStatus(long id) {
        Result<ResponseGetActivityTemplateShelfStatus> result = activityTemplateConfigServiceRemote.getTemplateShelfStatus(id);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result);
        }
        ResponseGetActivityTemplateShelfStatus target = result.target();
        return ResultVO.success(activityTemplateConverter.toGetActivityTemplateShelfStatusResult(target));
    }

    /**
     * 分页查询活动模板
     *
     * @param param 请求
     * @return 结果
     */
    public ResultVO<PageVO<PageActivityTemplateResult>> pageTemplate(PageActivityTemplateParam param) {
        RequestPageActivityTemplate req = activityTemplateConverter.toRequestPageActivityTemplate(param);
        Result<PageBean<ActivityTemplatePageBean>> result = activityTemplateConfigServiceRemote.pageTemplate(req);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result);
        }
        PageBean<ActivityTemplatePageBean> target = result.target();
        return ResultVO.success(activityTemplateConverter.toPageActivityTemplateResultPageVO(target));
    }

    /**
     * 获取活动模板
     *
     * @param templateId 活动模板id
     * @return 结果
     */
    public ResultVO<GetActivityTemplateResult> getTemplate(long templateId) {
        Result<ResponseGetActivityTemplate> result = activityTemplateConfigServiceRemote.getTemplate(templateId);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result);
        }
        ResponseGetActivityTemplate target = result.target();
        return ResultVO.success(activityTemplateConverter.toGetActivityTemplateResult(target));
    }

    /**
     * 获取基础活动配置
     *
     * @param appId 应用id
     * @return 结果
     */
    public ResultVO<GetBaseActivityConfigResult> getBaseActivityConfig(Integer appId) {
        Result<ResponseGetBaseActivityConfig> result = activityCommonServiceRemote.getBaseActivityConfig(appId);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result);
        }
        ResponseGetBaseActivityConfig target = result.target();
        return ResultVO.success(activityCommonConverter.toGetBaseActivityConfigResult(target));
    }
}
