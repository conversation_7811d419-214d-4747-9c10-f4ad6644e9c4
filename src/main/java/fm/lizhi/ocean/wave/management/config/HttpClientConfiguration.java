package fm.lizhi.ocean.wave.management.config;

import fm.lizhi.ocean.wave.management.common.web.RequestAuthenticationInterceptor;
import fm.lizhi.ocean.wave.management.common.web.RequestRoutingInterceptor;
import fm.lizhi.ocean.wave.management.config.apollo.CommonConfig;
import fm.lizhi.ocean.wave.management.config.apollo.UtilityConfig;
import fm.lizhi.ocean.wave.management.config.apollo.WebpProperties;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * HTTP客户端配置
 */
@Configuration
public class HttpClientConfiguration {

    /**
     * 公共RestTemplate
     *
     * @param commonConfig 公共配置
     * @return 公共RestTemplate
     */
    @Bean("restTemplate")
    public RestTemplate restTemplate(CommonConfig commonConfig) {
        CommonConfig.RestTemplate restTemplateConfig = commonConfig.getRestTemplate();
        Dispatcher dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(restTemplateConfig.getMaxRequests());
        dispatcher.setMaxRequestsPerHost(restTemplateConfig.getMaxRequestsPerHost());
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(restTemplateConfig.getConnectTimeout())
                .readTimeout(restTemplateConfig.getReadTimeout())
                .writeTimeout(restTemplateConfig.getWriteTimeout())
                .dispatcher(dispatcher)
                .build();
        OkHttp3ClientHttpRequestFactory requestFactory = new OkHttp3ClientHttpRequestFactory(okHttpClient);
        return new RestTemplate(requestFactory);
    }

    /**
     * webp转换使用的RestTemplate
     *
     * @param utilityConfig 工具配置
     * @return webp转换使用的RestTemplate
     */
    @Bean("webpRestTemplate")
    public RestTemplate webpRestTemplate(UtilityConfig utilityConfig) {
        WebpProperties.RestTemplate restTemplateConfig = utilityConfig.getWebp().getRestTemplate();
        Dispatcher dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(restTemplateConfig.getMaxRequests());
        dispatcher.setMaxRequestsPerHost(restTemplateConfig.getMaxRequestsPerHost());
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(restTemplateConfig.getConnectTimeout())
                .readTimeout(restTemplateConfig.getReadTimeout())
                .writeTimeout(restTemplateConfig.getWriteTimeout())
                .dispatcher(dispatcher)
                .build();
        OkHttp3ClientHttpRequestFactory requestFactory = new OkHttp3ClientHttpRequestFactory(okHttpClient);
        RestTemplate restTemplate = new RestTemplate(requestFactory);
        List<ClientHttpRequestInterceptor> interceptors = new ArrayList<>();
        interceptors.add(new RequestAuthenticationInterceptor());
        interceptors.add(new RequestRoutingInterceptor());
        restTemplate.setInterceptors(interceptors);
        return restTemplate;
    }
}
