package fm.lizhi.ocean.wave.management.model.param.anchor.singer;

import java.util.List;

import lombok.Data;

@Data
public class SingerVerifyAuditParam {

     /**
     * 歌手认证记录ID
     */
    private List<Long> ids;

    /**
     * 目标审核状态
     */
    private Integer auditStatus;

    /**
     * 不通过原因
     */
    private String rejectedCause;

    /**
     * 歌手类型
     */
    private Integer singerType;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 操作人
     */
    private String operator;
}
