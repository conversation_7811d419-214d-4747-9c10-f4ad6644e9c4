package fm.lizhi.ocean.wave.management.model.param.utility.webp;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import fm.lizhi.ocean.wave.platform.api.platform.webp.constant.WebpConvertQualityOptionEnum;
import fm.lizhi.ocean.wave.platform.api.platform.webp.constant.WebpConvertSourceTypeEnum;
import lombok.Data;

/**
 * webp异步转换请求参数
 */
@Data
public class WebpAsyncConvertParam {

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 业务请求id, 由业务方生成并保证唯一
     */
    private String bizRequestId;

    /**
     * 业务类型, 由业务方定义, 用于分类
     */
    private String bizType;

    /**
     * 源文件类型
     *
     * @see WebpConvertSourceTypeEnum
     */
    private String sourceType;

    /**
     * 源文件路径, 斜杆开头的相对路径
     */
    private String sourcePath;

    /**
     * 转换质量选项
     */
    @JsonSetter(nulls = Nulls.SKIP)
    private Integer qualityOption = WebpConvertQualityOptionEnum.NORMAL.getValue();
}
