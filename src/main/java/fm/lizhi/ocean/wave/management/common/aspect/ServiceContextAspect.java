package fm.lizhi.ocean.wave.management.common.aspect;

import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.context.ServiceContext;
import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 解析出请求参数中的上下文信息
 * <AUTHOR>
 * @date 2024/12/2 19:36
 */
@Aspect
@Slf4j
public class ServiceContextAspect {

    @Around("@within(org.springframework.web.bind.annotation.RestController)" +
            " && execution(public fm.lizhi.ocean.wave.management.model.vo.ResultVO *(..))")
    public Object parseContext(ProceedingJoinPoint joinPoint) throws Throwable {
        ServiceContext context = ContextUtils.getContext();
        BusinessEvnEnum oldBusinessEnv = null;
        if (context != null) {
            oldBusinessEnv = context.getBusinessEvnEnum();
        }
        Integer appId = SessionExtUtils.getAppId();
        try {
            if (appId != null) {
                ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.from(appId));
            }
            return joinPoint.proceed();
        } finally {
            if (oldBusinessEnv != null) {
                ContextUtils.setBusinessEvnEnum(oldBusinessEnv);
            } else {
                ContextUtils.clearContext();
            }
        }
    }

}
