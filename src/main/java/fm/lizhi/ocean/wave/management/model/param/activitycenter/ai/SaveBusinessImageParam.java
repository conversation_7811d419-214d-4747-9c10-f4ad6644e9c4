package fm.lizhi.ocean.wave.management.model.param.activitycenter.ai;

import fm.lizhi.ocean.wave.management.common.annotation.TransferWaveCdnToBusinessCdn;
import fm.lizhi.ocean.wave.management.common.validation.EnumValue;
import fm.lizhi.ocean.wave.management.constants.activitycenter.ai.BusinessImageTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.constants.DecorateLiveModeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.constants.DecorateMaterialTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.constants.DecorateTimeTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.constants.DecorateVisibilityEnum;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import java.beans.Transient;
import java.util.List;
import java.util.Objects;

/**
 * 保存业务素材图片参数
 */
@Data
public class SaveBusinessImageParam {

    /**
     * 图片素材类型
     */
    @NotNull(message = "图片类型不能为空")
    @EnumValue(value = BusinessImageTypeEnum.class, message = "图片类型不合法")
    private Integer imageType;

    /**
     * 房间背景参数, 当图片类型为房间背景时必填
     */
    private RoomBackground roomBackground;

    /**
     * 头像框参数, 当图片类型为头像框时必填
     */
    private AvatarWidget avatarWidget;

    @AssertTrue(message = "图片类型为房间背景时, 房间背景参数不能为空")
    @Transient
    protected boolean isRoomBackgroundPresent() {
        return !Objects.equals(imageType, BusinessImageTypeEnum.ROOM_BACKGROUND.getValue()) || roomBackground != null;
    }

    @AssertTrue(message = "图片类型为头像框时, 头像框参数不能为空")
    @Transient
    protected boolean isAvatarWidgetPresent() {
        return !Objects.equals(imageType, BusinessImageTypeEnum.AVATAR_WIDGET.getValue()) || avatarWidget != null;
    }

    /**
     * 房间背景参数
     */
    @Data
    public static class RoomBackground {

        /**
         * 名称
         */
        private String name;

        /**
         * 静态图或角标
         */
        @TransferWaveCdnToBusinessCdn
        private String thumbUrl;

        /**
         * 素材
         */
        @TransferWaveCdnToBusinessCdn
        private String materialUrl;

        /**
         * 素材类型
         *
         * @see DecorateMaterialTypeEnum
         */
        private String materialType;

        /**
         * 图标
         */
        @TransferWaveCdnToBusinessCdn
        private String iconUrl;

        /**
         * svga素材地址
         */
        @TransferWaveCdnToBusinessCdn
        private String svgaMaterialUrl;

        /**
         * 有效期分钟数, 有效期类型不为永久时必填
         */
        private Integer validMin;

        /**
         * 权重
         */
        private Integer weight;

        /**
         * 可见性
         *
         * @see DecorateVisibilityEnum
         */
        private Integer visibility;

        /**
         * 直播模式
         *
         * @see DecorateLiveModeEnum
         */
        private List<Integer> liveModes;

        /**
         * 背景颜色
         */
        private String backgroundColor;

        /**
         * 背景类型(皮肤类型)
         */
        private Integer backgroundType;

        /**
         * 点唱坐底图地址
         */
        @TransferWaveCdnToBusinessCdn
        private String stageUrl;

        /**
         * 舞台底座svga
         */
        @TransferWaveCdnToBusinessCdn
        private String stageSvgaUrl;

        /**
         * 舞台位默认图
         */
        @TransferWaveCdnToBusinessCdn
        private String stageLocationUrl;

        /**
         * 主持模式声纹
         */
        @TransferWaveCdnToBusinessCdn
        private String hostModeVoicePrintSvgaUrl;

        /**
         * 演唱模式声纹
         */
        @TransferWaveCdnToBusinessCdn
        private String singingModeVoicePrintSvgaUrl;

        /**
         * 舞台光圈
         */
        @TransferWaveCdnToBusinessCdn
        private String stageApertureSvgaUrl;

        /**
         * 时间限制类型
         *
         * @see DecorateTimeTypeEnum
         */
        private Integer timeType;

        /**
         * 价格(金币数)
         */
        private Integer dressUpCoin;

        /**
         * 备注
         */
        private String remark;
    }

    /**
     * 头像框参数
     */
    @Data
    public static class AvatarWidget {

        /**
         * 名称
         */
        private String name;

        /**
         * 静态图或角标
         */
        @TransferWaveCdnToBusinessCdn
        private String thumbUrl;

        /**
         * 素材
         */
        @TransferWaveCdnToBusinessCdn
        private String materialUrl;

        /**
         * 素材类型
         *
         * @see DecorateMaterialTypeEnum
         */
        private String materialType;

        /**
         * svga素材地址
         */
        @TransferWaveCdnToBusinessCdn
        private String svgaMaterialUrl;

        /**
         * PC端动画资源webp文件
         */
        @TransferWaveCdnToBusinessCdn
        private String pcAniUrl;

        /**
         * 有效期分钟数, 有效期类型不为永久时必填
         */
        private Integer validMin;

        /**
         * 可见性
         *
         * @see DecorateVisibilityEnum
         */
        private Integer visibility;

        /**
         * 时间限制类型
         *
         * @see DecorateTimeTypeEnum
         */
        private Integer timeType;

        /**
         * 价格(金币数)
         */
        private Integer dressUpCoin;

        /**
         * 备注
         */
        private String remark;
    }
}
