package fm.lizhi.ocean.wave.management.processor.singer.xm;

import fm.lizhi.ocean.wave.management.model.dto.singer.SingerHallStatusResult;
import fm.lizhi.ocean.wave.management.processor.singer.ISingerHallApplyProcessor;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmSingerHallApplyProcessor implements ISingerHallApplyProcessor {

    @Override
    public SingerHallStatusResult batchGetSingerHallStatusMap(int appId, List<Long> ids) {
        return SingerHallStatusResult.of(false, new HashMap<>(2));
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }
}
