package fm.lizhi.ocean.wave.management.model.vo.resource;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 装扮信息
 * <AUTHOR>
 * @date 2025/3/21 15:30
 */
@Data
@Accessors(chain = true)
public class DecorateInfoVO {

    /**
     * 装扮类型
     */
    private PlatformDecorateTypeEnum decorateTypeEnum;

    /**
     * 装扮ID
     */
    @JsonSerialize (using = ToStringSerializer.class)
    private Long decorateId;

    /**
     * 装扮名称
     */
    private String decorateName;
    
    /**
     * 装扮图片
     */
    private String decorateImage;

    /**
     * 装扮过期时间,分钟
     */
    private Integer decorateExpireTime;

    /**
     * 预览 URL
     */
    private String previewUrl;
}
