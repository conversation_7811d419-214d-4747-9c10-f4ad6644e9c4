package fm.lizhi.ocean.wave.management.datastore.dao.family.offlinezone;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.management.datastore.mapper.family.offlinezone.OfflineZoneLevelConfigExtMapper;
import fm.lizhi.ocean.wave.management.datastore.mapper.family.offlinezone.OfflineZoneLevelRightRelationExtMapper;
import fm.lizhi.ocean.wave.management.model.converter.family.offlinezone.LevelConfigConvert;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.UpdateLevelConfigParam;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelConfig;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelConfigExample;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRightRelation;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneLevelConfigMapper;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneLevelRightRelationMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class LevelConfigDao {


    @Autowired
    private LevelConfigConvert levelConfigConvert;

    @Autowired
    private OfflineZoneLevelConfigMapper offlineZoneLevelConfigMapper;
    @Autowired
    private OfflineZoneLevelConfigExtMapper offlineZoneLevelConfigExtMapper;

    @Autowired
    private OfflineZoneLevelRightRelationMapper offlineZoneLevelRightRelationMapper;
    @Autowired
    private OfflineZoneLevelRightRelationExtMapper offlineZoneLevelRightRelationExtMapper;

    /**
     * 根据ID获取等级配置
     *
     * @param id 等级配置ID
     * @return 等级配置实体
     */
    public OfflineZoneLevelConfig getLevelConfigById(Long id) {
        OfflineZoneLevelConfig param = new OfflineZoneLevelConfig();
        param.setId(id);


        return offlineZoneLevelConfigMapper.selectByPrimaryKey(param);
    }


    /**
     * 根据ID列表获取等级配置
     *
     * @param levelIds 等级配置ID列表
     * @return 等级配置实体列表
     */
    public List<OfflineZoneLevelConfig> getLevelConfigByIds(Set<Long> levelIds) {
        if (CollectionUtils.isEmpty(levelIds)){
            return new ArrayList<>();
        }

        OfflineZoneLevelConfigExample example = new OfflineZoneLevelConfigExample();
        example.createCriteria().andIdIn(new ArrayList<>(levelIds));
        return offlineZoneLevelConfigMapper.selectByExample(example);
    }

    /**
     * 更新等级配置
     *
     * @param param                      更新参数
     * @param shouldUpdateRightRelations 是否需要更新等级权益关系, 在外层提前判断好传入
     */
    @Transactional
    public void updateLevelConfig(UpdateLevelConfigParam param, boolean shouldUpdateRightRelations) {
        // 更新等级配置
        OfflineZoneLevelConfig updateLevelEntity = levelConfigConvert.toUpdateLevelEntity(param);
        int updateLevelRows = offlineZoneLevelConfigMapper.updateByPrimaryKey(updateLevelEntity);
        log.info("update levelConfig, rows={}, entity={}", updateLevelRows, updateLevelEntity);
        // 更新等级权益关系
        if (shouldUpdateRightRelations) {
            // 更新等级权益关系是低频操作, 直接先删除再批量插入
            Long levelId = param.getId();
            int deleteRelationRows = offlineZoneLevelRightRelationExtMapper.deleteByLevelId(levelId);
            List<OfflineZoneLevelRightRelation> insertRelationEntities = levelConfigConvert.toCreateLevelRightRelationEntities(param);
            int insertRelationRows = offlineZoneLevelRightRelationMapper.batchInsert(insertRelationEntities);
            log.info("update levelConfig right relation, deleteRows={}, insertRows={}, entities={}",
                    deleteRelationRows, insertRelationRows, insertRelationEntities);
        } else {
            log.info("no need to update levelConfig right relation");
        }
    }

    /**
     * 根据等级ID获取关联的等级权益列表, 按index升序
     *
     * @param levelId 等级ID
     * @return 关联的等级权益列表
     */
    public List<OfflineZoneLevelRightRelation> getLevelRightRelationsByLevelId(long levelId) {
        OfflineZoneLevelRightRelation selectMany = new OfflineZoneLevelRightRelation();
        selectMany.setLevelId(levelId);
        List<OfflineZoneLevelRightRelation> list = offlineZoneLevelRightRelationMapper.selectMany(selectMany);
        List<OfflineZoneLevelRightRelation> sortedList = new ArrayList<>(list);
        sortedList.sort(Comparator.comparing(OfflineZoneLevelRightRelation::getIndex));
        return sortedList;
    }

    private List<OfflineZoneLevelRightRelation> getLevelRightRelationsByLevelIds(List<Long> levelIds) {
        if (CollectionUtils.isEmpty(levelIds)) {
            return Collections.emptyList();
        }
        return offlineZoneLevelRightRelationExtMapper.selectByLevelIds(levelIds);
    }

    /**
     * 获取当前环境下的所有等级配置, 不包含已删除的, 按levelOrder升序排列
     *
     * @return 等级配置列表
     */
    public List<OfflineZoneLevelConfig> listLevelConfig() {
        Integer appId = SessionExtUtils.getAppId();
        String deployEnv = ConfigUtils.getEnvRequired().name();
        List<OfflineZoneLevelConfig> levelConfigs = offlineZoneLevelConfigExtMapper.listLevelConfig(appId, deployEnv);
        log.debug("listLevelConfig, appId={}, deployEnv={}, levelConfigs={}", appId, deployEnv, levelConfigs);
        return levelConfigs;
    }

    /**
     * 根据等级ID列表获取等级ID到关联的等级权益列表的映射, 其中权益列表已经按index升序排列
     *
     * @param levelIds 等级ID列表
     * @return 等级ID到关联的等级权益列表的映射
     */
    public ListValuedMap<Long, OfflineZoneLevelRightRelation> getLevelRightRelationsMapByLevelIds(List<Long> levelIds) {
        ArrayListValuedHashMap<Long, OfflineZoneLevelRightRelation> levelRightRelationsMap = new ArrayListValuedHashMap<>();
        List<OfflineZoneLevelRightRelation> relations = getLevelRightRelationsByLevelIds(levelIds);
        for (OfflineZoneLevelRightRelation relation : relations) {
            levelRightRelationsMap.put(relation.getLevelId(), relation);
        }
        log.debug("getLevelRightRelationsMapByLevelIds, levelIds={}, levelRightRelationsMap={}", levelIds, levelRightRelationsMap);
        return levelRightRelationsMap;
    }
}