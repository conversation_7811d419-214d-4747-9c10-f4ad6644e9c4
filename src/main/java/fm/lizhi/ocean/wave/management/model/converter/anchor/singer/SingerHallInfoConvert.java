package fm.lizhi.ocean.wave.management.model.converter.anchor.singer;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wave.management.model.param.anchor.singer.SingerHallInfoParam;
import fm.lizhi.ocean.wave.management.model.result.anchor.singer.SingerHallInfoResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerHallExcelInfoVO;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerRoomDetails;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerRoomDetails;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface SingerHallInfoConvert {

    SingerHallInfoConvert INSTANCE = Mappers.getMapper(SingerHallInfoConvert.class);

    /**
     * 参数转换为请求对象
     */
    RequestSingerRoomDetails param2Request(SingerHallInfoParam param, Integer appId);

    @Mapping(source = "njInfo", target = "njInfo")
    SingerHallInfoResult response2Result(ResponseSingerRoomDetails response);

    PageVO<SingerHallInfoResult> pageBean2PageVO(PageBean<ResponseSingerRoomDetails> pageBean);

    @Mappings({
        @Mapping(source = "familyInfo.familyName", target = "familyName"),
        @Mapping(source = "njInfo.id", target = "singerId"),
        @Mapping(source = "njInfo.name", target = "name"),
        @Mapping(source = "njInfo.band", target = "band"),
    })
    SingerHallExcelInfoVO singerHallInfoResult2Excel(SingerHallInfoResult singerHallInfoResult);

    List<SingerHallExcelInfoVO> singerHallInfosToResult2Excels(List<SingerHallInfoResult> SingerHallInfoResults);

}
