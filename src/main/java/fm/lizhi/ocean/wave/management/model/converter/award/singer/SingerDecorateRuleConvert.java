package fm.lizhi.ocean.wave.management.model.converter.award.singer;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerDecorateConditionDTO;
import fm.lizhi.ocean.wave.management.model.param.award.singer.PageSingerDecorateFlowParam;
import fm.lizhi.ocean.wave.management.model.param.award.singer.PageSingerDecorateRuleParam;
import fm.lizhi.ocean.wave.management.model.param.award.singer.SaveSingerDecorateRuleParam;
import fm.lizhi.ocean.wave.management.model.param.award.singer.UpdateSingerDecorateRuleParam;
import fm.lizhi.ocean.wave.management.model.result.award.singer.SingerDecorateFlowResult;
import fm.lizhi.ocean.wave.management.model.result.award.singer.SingerDecorateRuleResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerDecorateFlowExcelVO;
import fm.lizhi.ocean.wave.management.model.vo.award.singer.SingerDecorateConditionVO;
import fm.lizhi.ocean.wave.management.model.vo.award.singer.SingerDecorateRuleVO;
import fm.lizhi.ocean.wave.management.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestPageSingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestPageSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestSaveSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestUpdateSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.response.ResponseSingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;

import java.util.Date;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Objects;

import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateCondition;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateRule;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateConditionTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * 歌手装扮规则配置转换器
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
    imports = {
        PlatformDecorateTypeEnum.class,
        SimpleDateFormat.class,
            ConfigUtils.class,
            Date.class
    })
public interface SingerDecorateRuleConvert {

    SingerDecorateRuleConvert INSTANCE = Mappers.getMapper(SingerDecorateRuleConvert.class);

    /**
     * 转换分页请求
     * @param param 分页参数
     * @param appId 应用ID
     * @return 分页请求
     */
    RequestPageSingerDecorateRule convertPageRequest(PageSingerDecorateRuleParam param, Integer appId);

    /**
     * 转换保存请求
     * @param param 保存参数
     * @param appId 应用ID
     * @param operator 操作人
     * @return 保存请求
     */
    RequestSaveSingerDecorateRule convertSaveRequest(SaveSingerDecorateRuleParam param, Integer appId, String operator);

    /**
     * 转换更新请求
     * @param param 更新参数
     * @param appId 应用ID
     * @param operator 操作人
     * @return 更新请求
     */
    RequestUpdateSingerDecorateRule convertUpdateRequest(UpdateSingerDecorateRuleParam param, Integer appId, String operator);


    RequestPageSingerDecorateFlow convertRequestPageSingerDecorateFlow(PageSingerDecorateFlowParam param, Integer appId);

    PageVO<SingerDecorateFlowResult> convertSingerDecorateFlowResultList(PageBean<ResponseSingerDecorateFlow> target);

    @Mappings({
        @Mapping(source = "flow.id", target = "id"),
        @Mapping(source = "flow.userId", target = "userId"),
        @Mapping(target = "decorateType", expression = "java(PlatformDecorateTypeEnum.getByType(result.getFlow().getDecorateType()).getName())"),
        @Mapping(source = "flow.decorateId", target = "decorateId"),
        @Mapping(target = "operateType", expression = "java(result.getFlow().getOperateType() == 1 ? \"发放\" : \"回收\")"),
        @Mapping(source = "flow.operator", target = "operator"),
        @Mapping(target = "operateTime", expression = "java(result.getFlow().getOperateTime() != null ? new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\").format(new Date(result.getFlow().getOperateTime())) : \"\")"),
        @Mapping(source = "flow.reason", target = "reason"),
        @Mapping(source = "decorateInfo.decorateName", target = "decorateName"),
        @Mapping(source = "decorateInfo.decorateImage", target = "previewUrl"),
        @Mapping(source = "user.name", target = "name"),
    })
    SingerDecorateFlowExcelVO convertSingerDecorateFlowExcelVO(SingerDecorateFlowResult result);
    
    List<SingerDecorateFlowExcelVO> convertSingerDecorateFlowExcelVOList(List<SingerDecorateFlowResult> list);

    @Mapping(target = "decorateInfo", source = "decorateInfo")
    @Mapping(target = "rule", expression = "java(toSingerDecorateRuleVO(rule, conditions))")
    SingerDecorateRuleResult buildResponseSingerDecorateRule(SingerDecorateRule rule, DecorateInfoBean decorateInfo, List<SingerDecorateCondition> conditions);

    @Mapping(target = "conditionList", expression = "java(toSingerDecorateConditionVO(conditions))")
    @Mapping(target = "createTime", expression = "java(rule.getCreateTime().getTime())")
    @Mapping(target = "modifyTime", expression = "java(rule.getModifyTime().getTime())")
    SingerDecorateRuleVO toSingerDecorateRuleVO(SingerDecorateRule rule, List<SingerDecorateCondition> conditions);


    @Mapping(target = "songStyleList", expression = "java(deserializeSongStyleList(condition))")
    @Mapping(target = "originalSinger", expression = "java(deserializeOriginalSinger(condition))")
    SingerDecorateConditionVO toSingerDecorateConditionVO(SingerDecorateCondition condition);
    List<SingerDecorateConditionVO> toSingerDecorateConditionVO(List<SingerDecorateCondition> condition);


    default List<String> deserializeSongStyleList(SingerDecorateCondition condition) {
        if(Objects.equals(condition.getConditionType(), SingerDecorateConditionTypeEnum.MUSIC_STYLE.getValue())) {
            return JsonUtils.readValueAsList(condition.getConditionJson(), String.class);
        }
        //显式给个null
        return null;
    }

    default Boolean deserializeOriginalSinger(SingerDecorateCondition condition) {
        if(Objects.equals(condition.getConditionType(), SingerDecorateConditionTypeEnum.ORIGINAL_SINGER.getValue())) {
            return Boolean.parseBoolean(condition.getConditionJson());
        }
        //显式给个null
        return null;
    }
    default String serializeSongStyleList(Integer conditionType, List<String> songStyleList, Boolean originalSinger) {
        if(Objects.equals(SingerDecorateConditionTypeEnum.MUSIC_STYLE.getValue(), conditionType)) {
            return JsonUtils.toJsonString(songStyleList);
        } else if(Objects.equals(SingerDecorateConditionTypeEnum.ORIGINAL_SINGER.getValue(), conditionType)) {
            return originalSinger.toString();
        }
        return StringUtils.EMPTY;
    }



    @Mapping(target = "deleted", constant = "false")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    SingerDecorateRule toInsertSingerDecorateRule(SaveSingerDecorateRuleParam param, Integer appId, String operator, String combineConditionIndex);

    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "decorateRuleId", ignore = true)
    @Mapping(target = "conditionJson", expression = "java(serializeSongStyleList(condition.getConditionType(), condition.getSongStyleList(), condition.getOriginalSinger()))")
    @Mapping(target = "appId", expression = "java(appId)")
    SingerDecorateCondition toSingerDecorateCondition(SaveSingerDecorateRuleParam.SingerDecorateConditionParam condition, @Context Integer appId);

    List<SingerDecorateCondition> toSingerDecorateConditions(List<SaveSingerDecorateRuleParam.SingerDecorateConditionParam> conditions, @Context Integer appId);


    @Mapping(target = "modifyTime", expression = "java(new Date())")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "appId", ignore = true)
    SingerDecorateRule toUpdateSingerDecorateRule(UpdateSingerDecorateRuleParam param, String operator, String combineConditionIndex);


    @Mapping(target = "modifyTime", expression = "java(new Date())")
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "conditionJson", expression = "java(serializeSongStyleList(condition.getConditionType(), condition.getSongStyleList(), condition.getOriginalSinger()))")
    SingerDecorateCondition toUpdateSingerDecorateCondition(UpdateSingerDecorateRuleParam.SingerDecorateConditionParam condition);
    List<SingerDecorateCondition> toUpdateSingerDecorateConditions(List<UpdateSingerDecorateRuleParam.SingerDecorateConditionParam> conditions);

    @Mapping(target = "id", ignore = true)
    SingerDecorateConditionDTO toSingerDecorateConditionDTO(SaveSingerDecorateRuleParam.SingerDecorateConditionParam param);
    List<SingerDecorateConditionDTO> toSingerDecorateConditionDTOs(List<SaveSingerDecorateRuleParam.SingerDecorateConditionParam> conditions);

    SingerDecorateConditionDTO toUpdateSingerDecorateConditionDTO(UpdateSingerDecorateRuleParam.SingerDecorateConditionParam param);
    List<SingerDecorateConditionDTO> toUpdateSingerDecorateConditionDTOs(List<UpdateSingerDecorateRuleParam.SingerDecorateConditionParam> conditions);
}