package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import lombok.Data;

import java.util.List;

@Data
public class ActivityNoticeResult {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private Integer appId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 公告内容
     */
    private String content;

    /**
     * 品类
     */
    private List<Integer> categoryList;
}
