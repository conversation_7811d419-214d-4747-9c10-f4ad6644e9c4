package fm.lizhi.ocean.wave.management.model.param.family.offlinezone;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 保存等级权益参数, 作为新增和更新的基类
 */
@Data
public abstract class SaveLevelRightParam {

    /**
     * 等级权益名称
     */
    @NotEmpty(message = "等级权益名称不能为空")
    @Size(max = 50, message = "等级权益名称不能超过{max}个字")
    private String name;

    /**
     * 等级权益图片
     */
    @JsonSetter(nulls = Nulls.SKIP)
    private String image = StringUtils.EMPTY;

    /**
     * 等级权益介绍列表
     */
    @NotEmpty(message = "等级权益介绍至少1条")
    @Valid
    private List<Introduction> introductions;

    /**
     * 等级权益介绍
     */
    @Data
    public static class Introduction {

        /**
         * 介绍标题
         */
        @NotEmpty(message = "介绍标题不能为空")
        @Size(max = 30, message = "介绍标题不能超过{max}个字")
        private String title;

        /**
         * 介绍内容
         */
        @NotEmpty(message = "介绍内容不能为空")
        @Size(max = 500, message = "介绍内容不能超过{max}个字")
        private String content;
    }
}
