package fm.lizhi.ocean.wave.management.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import fm.lizhi.commons.service.client.pojo.Result;
import lombok.Data;

import java.beans.Transient;

/**
 * 统一泛型结果VO
 *
 * @param <T> 数据类型
 */
@Data
public class ResultVO<T> {

    /**
     * 成功状态码, 固定为0
     */
    public static final int SUCCESS = 0;
    /**
     * 失败状态码, 默认为1, 业务代码可以自定义失败状态码
     */
    public static final int FAILURE = 1;

    /**
     * 返回码
     */
    @JsonProperty("rCode")
    private int rCode;

    /**
     * 提示消息, 通常用于错误信息
     */
    private String msg;

    /**
     * 数据
     */
    private T data;

    /**
     * 不带数据的成功返回, 统一使用Void泛型
     *
     * @return 不带数据的成功返回
     */
    public static ResultVO<Void> success() {
        ResultVO<Void> vo = new ResultVO<>();
        vo.setRCode(SUCCESS);
        return vo;
    }

    /**
     * 带数据的成功返回
     *
     * @param data 数据
     * @param <T>  数据泛型
     * @return 带数据的成功返回
     */
    public static <T> ResultVO<T> success(T data) {
        ResultVO<T> vo = new ResultVO<>();
        vo.setRCode(SUCCESS);
        vo.setData(data);
        return vo;
    }

    /**
     * 使用默认失败状态码的失败返回
     *
     * @param msg 失败消息
     * @param <T> 数据泛型
     * @return 失败返回
     */
    public static <T> ResultVO<T> failure(String msg) {
        ResultVO<T> vo = new ResultVO<>();
        vo.setRCode(FAILURE);
        vo.setMsg(msg);
        return vo;
    }

    /**
     * 使用自定义失败状态码的失败返回
     *
     * @param rCode 失败状态码
     * @param msg   失败消息
     * @param <T>   数据泛型
     * @return 失败返回
     */
    public static <T> ResultVO<T> failure(int rCode, String msg) {
        ResultVO<T> vo = new ResultVO<>();
        vo.setRCode(rCode);
        vo.setMsg(msg);
        return vo;
    }

    /**
     * 使用其他返回结果的失败返回, 该方法主要是用于泛型转换
     *
     * @param resultVO 其他返回结果
     * @param <T>      源头的数据泛型
     * @param <R>      转换后的数据泛型
     * @return 失败返回
     */
    public static <T, R> ResultVO<R> failure(ResultVO<T> resultVO) {
        ResultVO<R> vo = new ResultVO<>();
        vo.setRCode(resultVO.getRCode());
        vo.setMsg(resultVO.getMsg());
        return vo;
    }

    /**
     * 使用其他RPC结果的失败返回, 该方法主要是用于泛型转换
     *
     * @param result 其他返回结果
     * @param <T>    源头的数据泛型
     * @param <R>    转换后的数据泛型
     * @return 失败返回
     */
    public static <T, R> ResultVO<R> failure(Result<T> result) {
        ResultVO<R> vo = new ResultVO<>();
        vo.setRCode(result.rCode());
        vo.setMsg(result.getMessage());
        return vo;
    }

    /**
     * 判断是否成功
     *
     * @return 是否成功
     */
    @Transient
    public boolean isSuccess() {
        return rCode == SUCCESS;
    }

    /**
     * 判断是否失败
     *
     * @return 是否失败
     */
    @Transient
    public boolean isFailure() {
        return !isSuccess();
    }
}
