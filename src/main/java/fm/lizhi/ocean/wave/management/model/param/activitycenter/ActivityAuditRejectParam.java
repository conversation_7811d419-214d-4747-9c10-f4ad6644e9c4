package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ActivityAuditRejectParam {

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 审核不通过原因
     */
    private String reason;

    /**
     * 应用ID2
     */
    private Integer appId;

    /**
     * 版本
     */
    private Integer version;

}
