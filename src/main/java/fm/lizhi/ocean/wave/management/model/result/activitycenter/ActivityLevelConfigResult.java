package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class ActivityLevelConfigResult {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 等级名称
     */
    private String level;

    /**
     * 操作者
     */
    private String operator;

    /**
     * 修改时间
     */
    private Long modifyTime;


}