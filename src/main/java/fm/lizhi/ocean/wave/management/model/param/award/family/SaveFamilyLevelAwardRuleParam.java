package fm.lizhi.ocean.wave.management.model.param.award.family;

import fm.lizhi.ocean.wavecenter.api.award.family.bean.SaveFamilyLevelAwardItemBean;
import lombok.Data;

import java.util.List;

/**
 * 保存公会等级奖励规则参数
 */
@Data
public class SaveFamilyLevelAwardRuleParam {

    /**
     * 公会等级id
     */
    private Long levelId;

    /**
     * 公会等级奖励条目
     */
    private List<SaveFamilyLevelAwardItemBean> items;
}
