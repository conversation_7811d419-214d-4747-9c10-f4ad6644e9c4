package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceDeployTypeConstants;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityResource;
import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * 修改活动资源
 * <AUTHOR>
 */
@Data
public class UpdateActivityResourceParam {

    private Long id;

    /**
     * 资源名称
     */
    @Size(max = 10, message = "等级名称长度必须小于10个字")
    private String name;

    /**
     * 资源介绍
     */
    @Size(max = 100, message = "等级名称长度必须小于100个字")
    private String introduction;

    /**
     * 资源图片 URL
     */
    private String imageUrl;

    /**
     * 关联等级 ID 列表
     */
    private List<Long> relationLevelIds;

    /**
     * 资源配置类型，1：自动，2：手动
     */
    private Integer deployType;

    /**
     * 是否可用，0：禁用，1：可用
     */
    private Integer status;

    /**
     * 自动资源的code
     */
    private String resourceCode;

    /**
     * 是否必选
     */
    private Boolean required;


}
