package fm.lizhi.ocean.wave.management.model.result.family.offlinezone;

import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wave.management.model.vo.family.offlinezone.AgreementFileVO;

import lombok.Data;

/**
 * 跳槽保护协议历史记录结果
 * <AUTHOR>
 */
@Data
public class ProtectionHistoryResult {

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 跳槽保护表ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long protectedId;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 公会ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyId;

    /**
     * 厅ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long njId;

    /**
     * 主播ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long playerId;

    /**
     * 上传用户
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long uploadUserId;

    /**
     * 协议生效开始时间
     */
    private Long agreementStartTime;

    /**
     * 协议生效结束时间
     */
    private Long agreementEndTime;

    /**
     * 协议更新时间
     */
    private Long agreementUpdateTime;

    /**
     * 是否盖章签字：0-否，1-是
     */
    private Boolean stampSign;

    /**
     * 环境：TEST/PRE/PRO
     */
    private String deployEnv;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long modifyTime;


    /**
     * 协议文件列表
     */
    private List<AgreementFileVO> agreementFileList;

}