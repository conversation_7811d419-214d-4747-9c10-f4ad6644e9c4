package fm.lizhi.ocean.wave.management.datastore.dao.family.offlinezone;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wave.management.datastore.mapper.family.offlinezone.OfflineZoneLearningClassExtMapper;
import fm.lizhi.ocean.wave.management.datastore.mapper.family.offlinezone.OfflineZoneLearningClassWhiteListExtMapper;
import fm.lizhi.ocean.wave.management.model.converter.family.offlinezone.LearningClassConvert;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.AddLearningClassParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.DeleteLearningClassParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.ListLearningClassParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.UpdateLearningClassParam;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLearningClass;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLearningClassWhiteList;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneLearningClassMapper;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneLearningClassWhiteListMapper;
import fm.lizhi.sso.client.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 线下专区学习课堂 Dao
 */
@Repository
@Slf4j
public class LearningClassDao {

    @Autowired
    private LearningClassConvert learningClassConvert;

    @Autowired
    private OfflineZoneLearningClassMapper offlineZoneLearningClassMapper;
    @Autowired
    private OfflineZoneLearningClassExtMapper offlineZoneLearningClassExtMapper;

    @Autowired
    private OfflineZoneLearningClassWhiteListMapper offlineZoneLearningClassWhiteListMapper;
    @Autowired
    private OfflineZoneLearningClassWhiteListExtMapper offlineZoneLearningClassWhiteListExtMapper;

    /**
     * 新增学习课堂资料
     *
     * @param param 新增参数
     * @return 新增结果的ID
     */
    @Transactional
    public Long addLearningClass(AddLearningClassParam param) {
        // 插入学习课堂资料
        OfflineZoneLearningClass createClassEntity = learningClassConvert.toCreateClassEntity(param);
        offlineZoneLearningClassMapper.insert(createClassEntity);
        log.info("insert OfflineZoneLearningClass, entity={}", createClassEntity);
        // 插入资料关联的白名单列表
        Long learningId = createClassEntity.getId();
        List<OfflineZoneLearningClassWhiteList> createWhiteListEntities = learningClassConvert.toCreateWhiteListEntities(param, learningId);
        if (CollectionUtils.isNotEmpty(createWhiteListEntities)) {
            int insertWhiteListRows = offlineZoneLearningClassWhiteListMapper.batchInsert(createWhiteListEntities);
            log.info("batch insert OfflineZoneLearningClassWhiteList, rows={}, entities={}", insertWhiteListRows, createWhiteListEntities);
        } else {
            log.info("no need to insert OfflineZoneLearningClassWhiteList");
        }
        return learningId;
    }

    /**
     * 根据ID获取学习课堂资料
     *
     * @param id 学习课堂ID
     * @return 学习课堂实体
     */
    public OfflineZoneLearningClass getLearningClass(long id) {
        OfflineZoneLearningClass getById = new OfflineZoneLearningClass();
        getById.setId(id);
        return offlineZoneLearningClassMapper.selectByPrimaryKey(getById);
    }

    private List<OfflineZoneLearningClassWhiteList> getWhiteListsByLearningId(long learningId) {
        OfflineZoneLearningClassWhiteList selectMany = new OfflineZoneLearningClassWhiteList();
        selectMany.setLearningId(learningId);
        return offlineZoneLearningClassWhiteListMapper.selectMany(selectMany);
    }

    private List<OfflineZoneLearningClassWhiteList> getWhiteListsByLearningIds(List<Long> learningIds) {
        if (CollectionUtils.isEmpty(learningIds)) {
            return Collections.emptyList();
        }
        return offlineZoneLearningClassWhiteListExtMapper.selectByLearningIds(learningIds);
    }

    /**
     * 根据学习课堂ID获取白名单ID列表
     *
     * @param learningId 学习课堂ID
     * @return 白名单ID列表
     */
    public List<Long> getWhiteIdsByLearningId(long learningId) {
        List<OfflineZoneLearningClassWhiteList> whiteLists = getWhiteListsByLearningId(learningId);
        ArrayList<Long> whiteIds = new ArrayList<>(whiteLists.size());
        for (OfflineZoneLearningClassWhiteList whiteList : whiteLists) {
            whiteIds.add(whiteList.getWhiteId());
        }
        return whiteIds;
    }

    /**
     * 更新学习课堂资料
     *
     * @param param                 更新参数
     * @param shouldUpdateWhiteList 是否需要更新白名单列表, 在外层提前判断好传入
     */
    @Transactional
    public void updateLearningClass(UpdateLearningClassParam param, boolean shouldUpdateWhiteList) {
        // 更新学习课堂资料
        OfflineZoneLearningClass updateClassEntity = learningClassConvert.toUpdateClassEntity(param);
        int updateClassRows = offlineZoneLearningClassMapper.updateByPrimaryKey(updateClassEntity);
        log.info("update OfflineZoneLearningClass, rows={}, entity={}", updateClassRows, updateClassEntity);
        // 更新资料关联的白名单列表
        if (shouldUpdateWhiteList) {
            // 学习课堂白名单列表变化是低频操作, 直接先删除再批量插入
            Long learningId = param.getId();
            int deleteWhiteListRows = offlineZoneLearningClassWhiteListExtMapper.deleteByLearningId(learningId);
            List<OfflineZoneLearningClassWhiteList> insertWhiteListEntities = learningClassConvert.toCreateWhiteListEntities(param, learningId);
            int insertWhiteListRows = offlineZoneLearningClassWhiteListMapper.batchInsert(insertWhiteListEntities);
            log.info("update white list for learningId={}, deleteRows={}, insertRows={}, insertEntities={}",
                    learningId, deleteWhiteListRows, insertWhiteListRows, insertWhiteListEntities);
        } else {
            log.info("no need to update white list for learningId={}", param.getId());
        }
    }

    /**
     * 删除学习课堂资料
     *
     * @param param 删除参数
     */
    @Transactional
    public void deleteLearningClass(DeleteLearningClassParam param) {
        Long id = param.getId();
        String operator = SessionUtils.getAccount();
        int deleteClassRows = offlineZoneLearningClassExtMapper.deleteById(id, operator);
        log.info("delete OfflineZoneLearningClass, rows={}, id={}, operator={}", deleteClassRows, id, operator);
    }

    /**
     * 分页查询学习课堂资料列表, 不包含已删除的
     *
     * @param param 查询参数
     * @return 分页结果列表
     */
    public PageList<OfflineZoneLearningClass> pageListLearningClass(ListLearningClassParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String deployEnv = ConfigUtils.getEnvRequired().name();
        Integer pageNumber = param.getPageNumber();
        Integer pageSize = param.getPageSize();
        PageList<OfflineZoneLearningClass> pageList = offlineZoneLearningClassExtMapper.pageList(param, appId, deployEnv, pageNumber, pageSize);
        log.debug("pageList OfflineZoneLearningClass, param={}, pageList={}", param, pageList);
        return pageList;
    }

    /**
     * 获取学习课堂白名单ID列表Map, key: 学习课堂ID, value: 白名单ID列表
     *
     * @param learningIds 学习课堂ID列表
     * @return 学习课堂白名单ID列表Map
     */
    public ListValuedMap<Long, Long> getLearningClassWhiteListIdsMap(List<Long> learningIds) {
        ArrayListValuedHashMap<Long, Long> classWhiteListIdsMap = new ArrayListValuedHashMap<>();
        List<OfflineZoneLearningClassWhiteList> whiteLists = getWhiteListsByLearningIds(learningIds);
        for (OfflineZoneLearningClassWhiteList whiteList : whiteLists) {
            classWhiteListIdsMap.put(whiteList.getLearningId(), whiteList.getWhiteId());
        }
        log.debug("getLearningClassWhiteListIdsMap, learningIds={}, classWhiteListIdsMap={}", learningIds, classWhiteListIdsMap);
        return classWhiteListIdsMap;
    }
}
