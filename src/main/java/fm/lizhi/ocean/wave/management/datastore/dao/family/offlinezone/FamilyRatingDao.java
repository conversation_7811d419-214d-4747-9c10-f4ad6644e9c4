package fm.lizhi.ocean.wave.management.datastore.dao.family.offlinezone;

import cn.hutool.core.collection.CollUtil;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.ListFamilyRatingParam;
import fm.lizhi.ocean.wave.management.util.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataFamilyWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataFamilyWeekExample;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneFamilyRating;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneFamilyRatingExample;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneDataFamilyWeekMapper;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneFamilyRatingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 公会评级数据访问对象
 * <AUTHOR>
 */
@Repository
@Slf4j
public class FamilyRatingDao {

    @Autowired
    private OfflineZoneFamilyRatingMapper offlineZoneFamilyRatingMapper;

    @Autowired
    public OfflineZoneDataFamilyWeekMapper offlineZoneDataFamilyWeekMapper;


    /**
     * 分页查询公会周数据
     *
     * @param param 查询参数
     * @return 分页结果
     */
    public PageList<OfflineZoneDataFamilyWeek> pageFamilyWeekByParam(ListFamilyRatingParam param) {

        OfflineZoneDataFamilyWeekExample example = new OfflineZoneDataFamilyWeekExample();
        OfflineZoneDataFamilyWeekExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(param.getAppId());
        criteria.andStartWeekDateEqualTo(MyDateUtil.getLastWeekStartDay());
        criteria.andEndWeekDateEqualTo(MyDateUtil.getLastWeekEndDay());

        if (param.getFamilyId() != null) {
            criteria.andFamilyIdEqualTo(param.getFamilyId());
        }
        if (param.getFamilyName() != null) {
            criteria.andFamilyNameLike("%" + param.getFamilyName() + "%");
        }
        example.setOrderByClause("basic_cnt desc, modify_time DESC, id DESC");

        return offlineZoneDataFamilyWeekMapper.pageByExample(example, param.getPageNo(), param.getPageSize());
    }



    /**
     * 分页查询公会评级
     *
     * @param param 查询参数
     * @return 分页结果
     */
    public PageList<OfflineZoneFamilyRating> pageRatingByParam(ListFamilyRatingParam param) {
        // 构建查询条件
        OfflineZoneFamilyRatingExample example = new OfflineZoneFamilyRatingExample();
        OfflineZoneFamilyRatingExample.Criteria criteria = example.createCriteria();
        
        criteria.andAppIdEqualTo(param.getAppId());
        criteria.andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        // 公会ID条件
        if (param.getFamilyId() != null) {
            criteria.andFamilyIdEqualTo(param.getFamilyId());
        }
        
        // 评级等级条件
        if (param.getLevelId() != null) {
            criteria.andLevelIdEqualTo(param.getLevelId().longValue());
        }

        // 设置排序
        example.setOrderByClause(" modify_time DESC, id DESC");

        // 分页查询
        return offlineZoneFamilyRatingMapper.pageByExample(example, param.getPageNo(), param.getPageSize());
    }

    /**
     * 批量更新公会评级
     *
     * @param familyIds 公会ID列表
     * @param levelId 等级ID
     * @param operator 操作人
     * @param appId 应用ID
     * @return 影响行数
     */
    @Transactional
    public void batchUpdateFamilyRating(List<Long> familyIds, Long levelId, String operator, Integer appId) {
        if (CollUtil.isEmpty(familyIds)){
            return;
        }

        List<Long> familyIdsCopy = new ArrayList<>(familyIds);

        // 构建更新条件
        OfflineZoneFamilyRatingExample example = new OfflineZoneFamilyRatingExample();
        OfflineZoneFamilyRatingExample.Criteria criteria = example.createCriteria();
        criteria.andFamilyIdIn(familyIdsCopy);
        criteria.andAppIdEqualTo(appId);
        criteria.andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        // 构建更新实体
        OfflineZoneFamilyRating updateEntity = new OfflineZoneFamilyRating();
        updateEntity.setLevelId(levelId);
        updateEntity.setOperator(operator);
        updateEntity.setModifyTime(new Date());

        // 先查询出需要更新的记录，然后逐个更新
        List<OfflineZoneFamilyRating> records = offlineZoneFamilyRatingMapper.selectByExample(example);
        int updateCount = 0;
        for (OfflineZoneFamilyRating record : records) {
            updateEntity.setId(record.getId());
            updateCount += offlineZoneFamilyRatingMapper.updateByPrimaryKey(updateEntity);
            familyIdsCopy.remove(record.getFamilyId());
        }

        if (!familyIdsCopy.isEmpty()) {
            List<OfflineZoneFamilyRating> insertList = new ArrayList<>();
            // 说明有新的公会需要插入
            for (Long familyId : familyIdsCopy) {
                OfflineZoneFamilyRating insertEntity = new OfflineZoneFamilyRating();
                insertEntity.setAppId(appId);
                insertEntity.setDeployEnv(ConfigUtils.getEnvRequired().name());
                insertEntity.setFamilyId(familyId);
                insertEntity.setLevelId(levelId);
                insertEntity.setOperator(operator);
                insertEntity.setCreateTime(new Date());
                insertEntity.setModifyTime(new Date());
                insertList.add(insertEntity);
            }
            updateCount += offlineZoneFamilyRatingMapper.batchInsert(insertList);
        }

        if (updateCount != familyIds.size()) {
            log.error("批量更新公会评级失败，更新行数与公会数不一致，更新行数：{}，公会数：{}", updateCount, familyIds.size());
            throw new RuntimeException("批量更新公会评级失败");
        }
    }

    /**
     * 根据公会ID列表查询公会周数据
     *
     * @param param 查询参数
     * @param familyIds 公会ID列表
     * @return 公会周数据列表
     */
    public List<OfflineZoneDataFamilyWeek> listFamilyWeekByFamilyIds(ListFamilyRatingParam param, List<Long> familyIds) {

        if (familyIds == null || familyIds.isEmpty()) {
            return Collections.emptyList();
        }

        OfflineZoneDataFamilyWeekExample example = new OfflineZoneDataFamilyWeekExample();
        OfflineZoneDataFamilyWeekExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(param.getAppId());
        criteria.andFamilyIdIn(familyIds);
        criteria.andStartWeekDateEqualTo(MyDateUtil.getLastWeekStartDay());
        criteria.andEndWeekDateEqualTo(MyDateUtil.getLastWeekEndDay());

        if (param.getFamilyName() != null) {
            criteria.andFamilyNameLike("%" + param.getFamilyName() + "%");
        }

        example.setOrderByClause("basic_cnt desc, modify_time DESC");
        return offlineZoneDataFamilyWeekMapper.selectByExample(example);
    }


    /**
     * 根据公会ID列表查询公会评级
     *
     * @param param 查询参数
     * @param familyIds 公会ID列表
     * @return 公会评级列表
     */
    public List<OfflineZoneFamilyRating> listRatingByFamilyIds(ListFamilyRatingParam param, List<Long> familyIds) {
        if (familyIds == null || familyIds.isEmpty()) {
            return Collections.emptyList();
        }

        OfflineZoneFamilyRatingExample example = new OfflineZoneFamilyRatingExample();
        OfflineZoneFamilyRatingExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(param.getAppId());
        criteria.andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        criteria.andFamilyIdIn(familyIds);

        if (param.getLevelId() != null) {
            criteria.andLevelIdEqualTo(param.getLevelId().longValue());
        }

        example.setOrderByClause("modify_time DESC");
        return offlineZoneFamilyRatingMapper.selectByExample(example);
    }

    /**
     * 根据公会ID获取公会评级实体
     *
     * @param familyId 公会ID
     * @return 公会评级实体
     */
    public OfflineZoneFamilyRating getFamilyRatingByFamilyId(long familyId) {
        OfflineZoneFamilyRating selectOne = new OfflineZoneFamilyRating();
        selectOne.setFamilyId(familyId);
        selectOne.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return offlineZoneFamilyRatingMapper.selectOne(selectOne);
    }
}