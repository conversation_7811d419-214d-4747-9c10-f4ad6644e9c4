package fm.lizhi.ocean.wave.management.processor.singer;

import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;

/**
 * <AUTHOR>
 */
public interface ISingerVerifyProcessor extends BusinessEnvAwareProcessor {

    /**
     * 回收奖励
     *
     * @param appId      应用ID
     * @param singerId   歌手ID
     * @param singerType 歌手类型
     * @param operator   操作人
     */
    void recoverSingerAward(int appId, long singerId, int singerType, String operator);

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return ISingerVerifyProcessor.class;
    }

}
