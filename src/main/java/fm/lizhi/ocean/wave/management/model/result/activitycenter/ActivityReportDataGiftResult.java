package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Builder;
import lombok.Data;

/**
 *
 * 活动报表-用户送礼表
 *
 * <AUTHOR>
 * @date 2024-10-16 04:43:36
 */
@Data
@Builder
public class ActivityReportDataGiftResult {
    /**
     * ID
     */
    private String id;

    /**
     * 活动ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 提报厅厅主ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long njId;

    /**
     * 家族ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyId;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 主播band号
     */
    private String band;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 送礼钻石数
     */
    private Long allIncome;

    /**
     * 送礼魅力值
     */
    private Long allCharm;

    /**
     * 送礼比数
     */
    private Long giftCnt;

    /**
     * 应用ID
     */
    private Integer appId;
}