package fm.lizhi.ocean.wave.management.processor.singer.xm;

import fm.lizhi.ocean.wave.management.processor.singer.ISingerVerifyProcessor;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmSingerVerifyProcessor implements ISingerVerifyProcessor {

    @Override
    public void recoverSingerAward(int appId, long singerId, int singerType, String operator) {
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }
}
