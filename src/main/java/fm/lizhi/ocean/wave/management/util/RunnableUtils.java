package fm.lizhi.ocean.wave.management.util;

import com.alibaba.ttl.TtlRunnable;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;

/**
 * 多线程的Runnable工具类
 */
public class RunnableUtils {

    /**
     * 包装Runnable, 主要是统一解决灯塔迭代信息跨线程传递以及threadLocal跨线程传递的问题
     *
     * @param runnable 要包装的Runnable
     * @return 包装后的Runnable
     */
    public static Runnable wrap(Runnable runnable) {
        return TtlRunnable.get(RunnableWrapper.of(runnable));
    }
}
