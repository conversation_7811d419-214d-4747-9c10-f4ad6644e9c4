package fm.lizhi.ocean.wave.management.model.converter.resource;

import fm.lizhi.ocean.wave.management.model.vo.resource.*;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.*;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21 16:58
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RecommendCardConvert {

    RecommendCardConvert I = Mappers.getMapper(RecommendCardConvert.class);

    RecommendCardUserStockVO userStockBean2VO(RecommendCardUserStockBean bean);

    List<RecommendCardUserStockVO> userStockBeans2VOs(List<RecommendCardUserStockBean> beans);

    RecommendCardUseRecordVO useRecordBean2VO(RecommendCardUseRecordBean bean);

    List<RecommendCardUseRecordVO> useRecordBeans2VOs(List<RecommendCardUseRecordBean> beans);

    RecommendCardSendRecordVO sendRecordBean2VO(RecommendCardSendRecordBean bean);

    List<RecommendCardSendRecordVO> sendRecordBeans2VOs(List<RecommendCardSendRecordBean> beans);

    SendRecommendCardBean sendCmdVO2Bean(SendRecommendCardVO vo);

    List<SendRecommendCardBean> sendCmdVOs2Beans(List<SendRecommendCardVO> vos);

    BatchSendUserResultVO batchSendUserResultBean2VO(BatchSendUserResultBean bean);

    List<BatchSendUserResultVO> batchSendUserResultBeans2VOs(List<BatchSendUserResultBean> beans);

}
