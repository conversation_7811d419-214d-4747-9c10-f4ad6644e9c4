package fm.lizhi.ocean.wave.management.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;

import java.io.IOException;
import java.io.InputStream;
import java.io.UncheckedIOException;
import java.lang.reflect.Type;
import java.util.List;

/**
 * 默认JSON序列化工具类
 */
public final class JsonUtils {

    /**
     * 默认的ObjectMapper对象
     */
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .registerModule(new ParameterNamesModule())
            .registerModule(new Jdk8Module())
            .registerModule(new JavaTimeModule())
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    private JsonUtils() {
    }

    /**
     * 将对象按默认的规则序列化为JSON字符串
     *
     * @param value 要序列化的对象
     * @return JSON字符串
     */
    public static String toJsonString(Object value) {
        try {
            return OBJECT_MAPPER.writeValueAsString(value);
        } catch (JsonProcessingException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 将JSON字符串按默认的规则反序列化为对象
     *
     * @param content JSON字符串
     * @param clazz   要反序列化的类型
     * @param <T>     反序列化类型泛型
     * @return 反序列化的对象
     */
    public static <T> T readValue(String content, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(content, clazz);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 将JSON输入流按默认的规则反序列化为对象
     *
     * @param src   JSON输入流
     * @param clazz 要反序列化的类型
     * @param <T>   反序列化类型泛型
     * @return 反序列化的对象
     */
    public static <T> T readValue(InputStream src, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(src, clazz);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 将JSON字符串按默认的规则反序列化为对象, 基于java.lang.reflect.Type
     *
     * @param content JSON字符串
     * @param type    Java中的类型描述
     * @param <T>     反序列化的类型泛型
     * @return 反序列化的对象
     */
    public static <T> T readValue(String content, Type type) {
        JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructType(type);
        try {
            return OBJECT_MAPPER.readValue(content, javaType);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 将JSON输入流按默认的规则反序列化为对象, 基于java.lang.reflect.Type
     *
     * @param src  JSON输入流
     * @param type Java中的类型描述
     * @param <T>  反序列化的类型泛型
     * @return 反序列化的对象
     */
    public static <T> T readValue(InputStream src, Type type) {
        JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructType(type);
        try {
            return OBJECT_MAPPER.readValue(src, javaType);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 将JSON字符串按默认的规则序列化转换为指定的类型对象
     *
     * @param content       JSON字符串
     * @param typeReference 要反序列化的类型
     * @param <T>           反序列化类型泛型
     * @return 反序列化的对象
     */
    public static <T> T readValue(String content, TypeReference<T> typeReference) {
        try {
            return OBJECT_MAPPER.readValue(content, typeReference);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 将JSON输入流按默认的规则反序列化为对象
     *
     * @param src           JSON输入流
     * @param typeReference 要反序列化的类型
     * @param <T>           反序列化类型泛型
     * @return 反序列化的对象
     */
    public static <T> T readValue(InputStream src, TypeReference<T> typeReference) {
        try {
            return OBJECT_MAPPER.readValue(src, typeReference);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 将JSON字符串按默认的规则反序列化为List对象
     *
     * @param content JSON字符串
     * @param clazz   要反序列化的类型
     * @param <T>     反序列化类型泛型
     * @return 反序列化的List对象
     */
    public static <T> List<T> readValueAsList(String content, Class<T> clazz) {
        CollectionType collectionType = constructListType(clazz);
        try {
            return OBJECT_MAPPER.readValue(content, collectionType);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 将JSON字符串按默认的规则反序列化为List对象
     *
     * @param src   JSON输入流
     * @param clazz 要反序列化的类型
     * @param <T>   反序列化类型泛型
     * @return 反序列化的List对象
     */
    public static <T> List<T> readValueAsList(InputStream src, Class<T> clazz) {
        CollectionType collectionType = constructListType(clazz);
        try {
            return OBJECT_MAPPER.readValue(src, collectionType);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 将对象按默认的规则序列化转换为指定的类型对象
     *
     * @param fromValue   要转换的来源对象
     * @param toValueType 要转换的目标类型
     * @param <T>         转换类型泛型
     * @return 转换后的对象
     */
    public static <T> T convertValue(Object fromValue, Class<T> toValueType) {
        return OBJECT_MAPPER.convertValue(fromValue, toValueType);
    }

    /**
     * 按默认的规则构造泛型类型
     *
     * @param parametrized     泛型外层类型
     * @param parameterClasses 泛型内层类型
     * @return 泛型类型
     */
    public static JavaType constructParametricType(Class<?> parametrized, Class<?>... parameterClasses) {
        return OBJECT_MAPPER.getTypeFactory().constructParametricType(parametrized, parameterClasses);
    }

    /**
     * 按默认的规则构造List类型
     *
     * @param elementClass 元素类型
     * @return List类型
     */
    public static CollectionType constructListType(Class<?> elementClass) {
        return OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, elementClass);
    }
}
