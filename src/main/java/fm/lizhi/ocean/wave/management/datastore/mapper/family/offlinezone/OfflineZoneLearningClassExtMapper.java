package fm.lizhi.ocean.wave.management.datastore.mapper.family.offlinezone;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.ListLearningClassParam;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLearningClass;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface OfflineZoneLearningClassExtMapper {

    @Delete("UPDATE `offline_zone_learning_class`\n" +
            "SET `deleted` = 1, `operator` = #{operator}, `modify_time` = NOW()\n" +
            "WHERE `id` = #{id} AND `deleted` = 0")
    int deleteById(@Param("id") long id, @Param("operator") String operator);

    @Select("<script>\n" +
            "  SELECT * FROM `offline_zone_learning_class`\n" +
            "  WHERE `app_id` = #{appId}\n" +
            "    AND `deploy_env` = #{deployEnv}\n" +
            "    AND `deleted` = 0\n" +
            "    <if test=\"param.title != null and param.title != ''\">\n" +
            "      AND `title` like CONCAT('%', #{param.title}, '%')\n" +
            "    </if>\n" +
            "    <if test=\"param.type != null\">\n" +
            "      AND `type` = #{param.type}\n" +
            "    </if>\n" +
            "    <if test=\"param.status != null\">\n" +
            "      AND `status` = #{param.status}\n" +
            "    </if>\n" +
            "  ORDER BY `create_time` DESC, `id` DESC\n" +
            "</script>")
    PageList<OfflineZoneLearningClass> pageList(@Param("param") ListLearningClassParam param,
                                                @Param("appId") Integer appId,
                                                @Param("deployEnv") String deployEnv,
                                                @Param(ParamContants.PAGE_NUMBER) int pageNumber,
                                                @Param(ParamContants.PAGE_SIZE) int pageSize);
}
