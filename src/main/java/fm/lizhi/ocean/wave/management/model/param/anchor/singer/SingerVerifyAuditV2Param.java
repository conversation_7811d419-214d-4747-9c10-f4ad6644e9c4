package fm.lizhi.ocean.wave.management.model.param.anchor.singer;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SingerVerifyAuditV2Param {

    /**
     * 歌手认证记录ID
     */
    private List<Long> ids;

    /**
     * 目标审核状态
     */
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    /**
     * 不通过原因
     */
    private String rejectedCause;

    /**
     * 歌手类型
     */
    @NotNull(message = "歌手类型不能为空")
    private Integer singerType;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 是否为原唱歌手
     */
    private Boolean originalSinger;

    /**
     * 通过的歌曲风格
     */
    private String passSongStyle;
}
