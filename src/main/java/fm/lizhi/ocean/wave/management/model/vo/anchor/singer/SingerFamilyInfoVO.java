package fm.lizhi.ocean.wave.management.model.vo.anchor.singer;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SingerFamilyInfoVO {

    /**
     * 家族ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 家族名称
     */
    private String familyName;
}
