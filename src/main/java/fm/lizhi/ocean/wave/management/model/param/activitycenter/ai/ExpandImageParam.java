package fm.lizhi.ocean.wave.management.model.param.activitycenter.ai;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import lombok.Data;

import javax.validation.constraints.*;
import java.beans.Transient;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 调整尺寸参数
 */
@Data
public class ExpandImageParam {

    /**
     * 会话id
     */
    @NotNull(message = "会话id不能为空")
    @Size(max = 64, message = "会话id长度不能超过{max}个字符")
    private String sessionId;

    /**
     * 原图URL
     */
    @NotNull(message = "原图URL不能为空")
    private String originalImageUrl;

    /**
     * 蒙版图URL, 前端需将蒙版图上传至创作者CDN获得URL
     */
    @NotNull(message = "蒙版图URL不能为空")
    private String maskImageUrl;

    /**
     * 宽度
     */
    @NotNull(message = "宽度不能为空")
    @Min(value = 64, message = "宽度不能小于{value}")
    @Max(value = 2048, message = "宽度不能大于{value}")
    @JsonSetter(nulls = Nulls.SKIP)
    private Integer width = 0;

    /**
     * 高度
     */
    @NotNull(message = "高度不能为空")
    @Min(value = 64, message = "高度不能小于{value}")
    @Max(value = 2048, message = "高度不能大于{value}")
    @JsonSetter(nulls = Nulls.SKIP)
    private Integer height = 0;

    /**
     * 校验宽高比
     *
     * @return 是否合法
     */
    @AssertTrue(message = "宽和高的长边除以短边不能大于16")
    @Transient
    protected boolean isWidthHeightRatioValid() {
        if (width == null || height == null) {
            return true;
        }
        BigDecimal longerSide = BigDecimal.valueOf(Math.max(width, height));
        BigDecimal shorterSide = BigDecimal.valueOf(Math.min(width, height));
        return longerSide.divide(shorterSide, 2, RoundingMode.DOWN).compareTo(BigDecimal.valueOf(16)) <= 0;
    }
}
