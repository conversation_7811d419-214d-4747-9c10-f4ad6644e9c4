package fm.lizhi.ocean.wave.management.model.param.award.singer;

import fm.lizhi.ocean.wave.management.common.validation.EnumValue;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.constant.SongStyleRangeType;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateConditionTypeEnum;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString
public class SaveSingerDecorateRuleParam {


    /**
     * 歌手认证等级
     */
    private Integer singerType;

    /**
     * 曲风
     */
    private String songStyle;

    /**
     * 装扮类型
     */
    private Integer decorateType;

    /**
     * 装扮ID
     */
    private Long decorateId;

    /**
     * 是否启用
     */
    private Boolean enabled;


    @NotNull
    @NotEmpty
    private List<SingerDecorateConditionParam> conditionList;


    @Data
    @ToString
    public static class SingerDecorateConditionParam {

        /**
         * 1:曲风，2：原创
         */
        @NotNull
        @EnumValue(value = SingerDecorateConditionTypeEnum.class, message = "条件类型值错误 -> 1:曲风，2：原创")
        private Integer conditionType;

        /**
         * 1:固定曲风，2：任一曲风，3：全能曲风
         */
        @EnumValue(value = SongStyleRangeType.class, message = "曲风范围类型错误 -> 1:固定曲风，2：任一曲风，3：全能曲风")
        private Integer songStyleType;

        /**
         * 是否原创歌手
         */
        private Boolean originalSinger;

        /**
         * 曲风
         */
        @Size(min = 1, message = "曲风不能为空")
        private List<String> songStyleList;
    }
}
