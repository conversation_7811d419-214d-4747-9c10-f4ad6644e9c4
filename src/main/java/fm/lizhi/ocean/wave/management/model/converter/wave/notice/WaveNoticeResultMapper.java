package fm.lizhi.ocean.wave.management.model.converter.wave.notice;

import fm.lizhi.ocean.wave.platform.api.platform.bean.WaveAnnouncementBean;
import fm.lizhi.ocean.wave.platform.api.platform.bean.WaveAnnouncementRelationBean;
import fm.lizhi.ocean.wave.management.model.result.wave.notice.WaveNoticeResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface WaveNoticeResultMapper {
    WaveNoticeResultMapper INSTANCE = Mappers.getMapper(WaveNoticeResultMapper.class);

    @Mapping(source = "enable", target = "enable", qualifiedByName = "intToBool")
    WaveNoticeResult toResult(WaveAnnouncementRelationBean bean);

    List<WaveNoticeResult> toResultList(List<WaveAnnouncementRelationBean> beans);

    @Mapping(source = "enable", target = "enable", qualifiedByName = "intToBool")
    WaveNoticeResult beanToResult(WaveAnnouncementBean bean);

    @Named("intToBool")
    default Boolean intToBool(Integer enable) {
        return enable != null && enable == 1;
    }
} 