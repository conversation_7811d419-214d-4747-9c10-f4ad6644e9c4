package fm.lizhi.ocean.wave.management.processor.singer;

import fm.lizhi.ocean.wave.management.model.dto.singer.SingerHallStatusResult;
import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ISingerHallApplyProcessor extends BusinessEnvAwareProcessor {


    /**
     * 批量查询点唱厅审核状态
     * 
     * @param appId 应用ID
     * @param ids 厅主ID列表
     * @return 结果, key为厅主ID, value: 点唱厅状态
     */
    SingerHallStatusResult batchGetSingerHallStatusMap(int appId, List<Long> ids);

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return ISingerHallApplyProcessor.class;
    }

}
