package fm.lizhi.ocean.wave.management.model.result.system;

import lombok.Data;

import java.util.List;

/**
 * 系统用户信息
 */
@Data
public class UserResult {

    /**
     * 用户id, 不同环境用户id可能不同, 仅作为排查问题时的参考
     */
    private Integer userId;

    /**
     * 账号名, 使用账号名作为用户唯一标识, 多个环境是一致的
     */
    private String account;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 头像url
     */
    private String avatar;

    /**
     * 邮箱, 也可以作为用户唯一标识, 和账号名一样, 多个环境是一致的
     */
    private String email;

    /**
     * 授权应用id列表
     */
    private List<Integer> authorizedAppIds;

    /**
     * 用户 Token
     */
    private String token;
}
