package fm.lizhi.ocean.wave.management.common.aspect;

import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.BridgeMethodResolver;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.util.ClassUtils;
import org.springframework.validation.annotation.Validated;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import javax.validation.executable.ExecutableValidator;
import java.lang.reflect.Method;
import java.util.Set;

/**
 * 校验切面, 对于使用@Validated注解的bean进行参数校验和返回值校验.
 * <p>
 * 如果是普通类方法, 应该在类上面添加@Validated注解, 在参数上添加校验注解, 如下:
 * <pre>{@code
 * @Component
 * @Validated
 * public class MyManager {
 *
 *     public ResultVO<Void> test(@Valid Param param) {
 *         return ResultVO.success(null);
 *     }
 * }
 * }</pre>
 * <p>
 * 如果是面向接口自动注入, 则应该在接口方法参数上加校验注解, 在实现类上添加@Validated注解, 如下:
 * <pre>{@code
 * public interface MyService {
 *
 *     Result<Void> test(@Valid Param param);
 * }
 *
 * @Component
 * @Validated
 * public class MyServiceImpl implements MyService {
 *
 *     @Override
 *     public Result<Void> test(Param param) {
 *         return new Result<>(0, null);
 *     }
 * }
 * }</pre>
 */
@Aspect
public class ValidationAspect {

    /**
     * javax的校验器
     */
    @Autowired
    @Lazy
    private Validator validator;

    /**
     * 校验方法参数和返回值
     *
     * @param joinPoint 切点
     * @return 如果校验不通过, 返回校验不通过的结果, 否则返回原方法的结果
     * @throws Throwable 异常
     */
    @Around("@annotation(org.springframework.validation.annotation.Validated)" +
            " && execution(public fm.lizhi.ocean.wave.management.model.vo.ResultVO *(..))")
    public Object validateAndReturnResultVO(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        Method method = ((MethodSignature) (joinPoint.getSignature())).getMethod();
        Object[] args = joinPoint.getArgs();
        Class<?>[] groups = determineValidationGroups(target, method);
        ExecutableValidator executableValidator = validator.forExecutables();
        Set<ConstraintViolation<Object>> parametersViolations;
        try {
            parametersViolations = executableValidator.validateParameters(target, method, args, groups);
        } catch (IllegalArgumentException e) {
            Method bridgedMethod = BridgeMethodResolver.findBridgedMethod(
                    ClassUtils.getMostSpecificMethod(method, target.getClass()));
            parametersViolations = executableValidator.validateParameters(target, bridgedMethod, args, groups);
        }
        if (CollectionUtils.isNotEmpty(parametersViolations)) {
            return buildInvalidResultVO(parametersViolations);
        }
        Object result = joinPoint.proceed();
        Set<ConstraintViolation<Object>> returnValueViolations = executableValidator.validateReturnValue(
                target, method, result, groups);
        if (CollectionUtils.isNotEmpty(returnValueViolations)) {
            return buildInvalidResultVO(returnValueViolations);
        }
        return result;
    }

    private Class<?>[] determineValidationGroups(Object target, Method method) {
        Validated methodAnnotation = AnnotationUtils.findAnnotation(method, Validated.class);
        if (methodAnnotation != null) {
            return methodAnnotation.value();
        }
        Validated classAnnotation = AnnotationUtils.findAnnotation(target.getClass(), Validated.class);
        if (classAnnotation != null) {
            return classAnnotation.value();
        }
        return new Class<?>[0];
    }

    private ResultVO<Void> buildInvalidResultVO(Set<ConstraintViolation<Object>> violations) {
        ConstraintViolation<Object> violation = violations.iterator().next();
        String message = violation.getPropertyPath() + ": " + violation.getMessage();
        return ResultVO.failure(message);
    }
}
