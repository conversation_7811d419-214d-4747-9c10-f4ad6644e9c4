package fm.lizhi.ocean.wave.management.model.vo.award.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 获取公会等级奖励规则的结果VO
 */
@Data
public class GetFamilyLevelAwardRuleVO {

    /**
     * 规则id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 公会等级id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long levelId;

    /**
     * 公会等级名称
     */
    private String levelName;

    /**
     * 公会等级是否已删除
     */
    private Boolean levelDeleted;

    /**
     * 奖励条目列表
     */
    private List<ItemVO> items;

    /**
     * 创建时间, 毫秒时间戳
     */
    private Long createTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改时间, 毫秒时间戳
     */
    private Long modifyTime;

    /**
     * 修改者
     */
    private String modifier;

    @Data
    public static class ItemVO {

        /**
         * 资源类型
         *
         * @see FamilyAwardResourceTypeEnum
         */
        private Integer resourceType;

        /**
         * 资源数量
         */
        private Integer resourceNumber;

        /**
         * 资源有效期, 默认单位为天
         */
        private Integer resourceValidPeriod;

        /**
         * 资源id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long resourceId;
    }
}
