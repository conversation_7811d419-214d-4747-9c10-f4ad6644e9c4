package fm.lizhi.ocean.wave.management.model.converter.family.offlinezone;

import fm.lizhi.ocean.wave.management.model.converter.CommonConvert;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.SaveLevelConfigParam;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.UpdateLevelConfigParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ListLevelConfigResult;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelConfig;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRightRelation;
import fm.lizhi.sso.client.SessionUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListValuedMap;
import org.mapstruct.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 线下专区等级配置转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
                Date.class,
                SessionUtils.class,
        },
        uses = {
                CommonConvert.class,
        }
)
public interface LevelConfigConvert {

    /**
     * 转换为用于更新的等级配置实体
     *
     * @param param 更新参数
     * @return 等级配置实体
     */
    @Mapping(target = "appId", ignore = true)
    @Mapping(target = "deployEnv", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "operator", expression = "java(SessionUtils.getAccount())")
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    OfflineZoneLevelConfig toUpdateLevelEntity(UpdateLevelConfigParam param);

    /**
     * 转换为新增的等级配置实体
     *
     * @param param 新增参数
     * @return 等级配置实体
     */
    default List<OfflineZoneLevelRightRelation> toCreateLevelRightRelationEntities(UpdateLevelConfigParam param) {
        if (CollectionUtils.isEmpty(param.getRights())) {
            return Collections.emptyList();
        }
        ArrayList<OfflineZoneLevelRightRelation> entities = new ArrayList<>(param.getRights().size());
        for (int index = 0; index < param.getRights().size(); index++) {
            Long levelId = param.getId();
            SaveLevelConfigParam.Right right = param.getRights().get(index);
            OfflineZoneLevelRightRelation entity = toCreateLevelRightRelationEntity(levelId, right, index);
            entities.add(entity);
        }
        return entities;
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    OfflineZoneLevelRightRelation toCreateLevelRightRelationEntity(Long levelId, SaveLevelConfigParam.Right right, Integer index);

    /**
     * 转换成等级配置列表结果
     *
     * @param entities               等级配置实体列表
     * @param levelRightRelationsMap 等级配置权益关联列表Map, key: 等级配置ID, value: 权益关联列表
     * @return 等级配置列表结果
     */
    List<ListLevelConfigResult> toListLevelConfigResults(List<OfflineZoneLevelConfig> entities, @Context ListValuedMap<Long, OfflineZoneLevelRightRelation> levelRightRelationsMap);

    @Mapping(target = "rights", source = "entity.id", qualifiedByName = "getListLevelConfigResultRights")
    ListLevelConfigResult toListLevelConfigResult(OfflineZoneLevelConfig entity, @Context ListValuedMap<Long, OfflineZoneLevelRightRelation> levelRightRelationsMap);

    @Named("getListLevelConfigResultRights")
    default List<ListLevelConfigResult.Right> getListLevelConfigResultRights(Long levelId, @Context ListValuedMap<Long, OfflineZoneLevelRightRelation> levelRightRelationsMap) {
        if (levelId == null || levelRightRelationsMap == null) {
            return Collections.emptyList();
        }
        List<OfflineZoneLevelRightRelation> relations = levelRightRelationsMap.get(levelId);
        return toListLevelConfigResultRights(relations);
    }

    List<ListLevelConfigResult.Right> toListLevelConfigResultRights(List<OfflineZoneLevelRightRelation> entities);

    @Mapping(target = "unlockedLevelId", expression = "java(entity.getUnlockedLevelId() == 0L ? null : entity.getUnlockedLevelId())")
    ListLevelConfigResult.Right toListLevelConfigResultRight(OfflineZoneLevelRightRelation entity);
}
