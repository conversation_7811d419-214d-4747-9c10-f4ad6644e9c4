package fm.lizhi.ocean.wave.management.remote.service.resource;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.BatchSendUserResultBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardSendRecordBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardUseRecordBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardUserStockBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.*;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.service.RecommendCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2025/3/21 16:54
 */
@Component
public class RecommendCardServiceRemote {

    @Autowired
    private RecommendCardService recommendCardService;

    public Result<RecommendCardUserStockBean> getUserStock(RequestGetUserStock request) {
        return recommendCardService.getUserStock(request);
    }

    public Result<PageBean<RecommendCardUseRecordBean>> getUseRecordForManagement(RequestGetUseRecord request){
        return recommendCardService.getUseRecordForManagement(request);
    }

    public Result<PageBean<RecommendCardSendRecordBean>> getSendRecord(RequestGetSendRecord request) {
        return recommendCardService.getSendRecord(request);
    }

    public Result<List<BatchSendUserResultBean>> batchSend(RequestBatchSend request){
        return recommendCardService.batchSend(request);
    }

    public Result<Void> recycle(RequestRecycle request) {
        return recommendCardService.recycle(request);
    }
}
