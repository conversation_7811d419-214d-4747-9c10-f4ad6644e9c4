package fm.lizhi.ocean.wave.management.remote.service.permission;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.platform.api.permission.bean.PlatformRoleListBean;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestGetPlatformRoleDetailList;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestSavePlatformRole;
import fm.lizhi.ocean.wave.platform.api.permission.service.PlatformRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/24 15:35
 */
@Component
@Slf4j
public class PlatformRoleServiceRemote {

    @Autowired
    private PlatformRoleService platformRoleService;

    /**
     * 查询所有平台角色 包括关联的权限
     * @return
     */
    public Result<List<PlatformRoleListBean>> getPlatformRoleDetailList(RequestGetPlatformRoleDetailList request){
        return platformRoleService.getPlatformRoleDetailList(request);
    }

    /**
     * 保存角色
     * @param request
     * @return
     */
    public Result<Void> savePlatformRole(RequestSavePlatformRole request) {
        return platformRoleService.savePlatformRole(request);
    }

}
