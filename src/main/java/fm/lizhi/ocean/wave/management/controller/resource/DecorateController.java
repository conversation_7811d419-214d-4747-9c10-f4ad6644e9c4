package fm.lizhi.ocean.wave.management.controller.resource;

import fm.lizhi.ocean.wave.management.manager.resource.ResourceManager;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.resource.MedalVO;
import fm.lizhi.ocean.wave.management.model.vo.resource.MountVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("resource")
public class DecorateController {

    @Autowired
    private ResourceManager resourceManager;

    @GetMapping("/mount/list")
    public ResultVO<PageVO<MountVO>> getMountList(@RequestParam(required = false) String name, @RequestParam(defaultValue = "1") Integer pageNumber, @RequestParam(defaultValue = "20") Integer pageSize ) {
        return resourceManager.getMountList(name, pageNumber, pageSize, SessionExtUtils.getAppId());
    }

    @GetMapping("/medal/list")
    public ResultVO<PageVO<MedalVO>> getMedalList(@RequestParam(required = false) String name, @RequestParam(defaultValue = "1") Integer pageNumber, @RequestParam(defaultValue = "20") Integer pageSize ) {
        return resourceManager.getMedalList(name, pageNumber, pageSize, SessionExtUtils.getAppId());
    }
}