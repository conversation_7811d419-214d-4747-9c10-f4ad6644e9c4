package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * @description: 角标信息
 * @author: guoyibin
 * @create: 2024/10/23 21:29
 */
@Data
public class RoomMarkInfoResult {

    /**
     * 背景ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 背景名称
     */
    private String name;

    /**
     * 背景预览图
     */
    private String previewUrl;
}
