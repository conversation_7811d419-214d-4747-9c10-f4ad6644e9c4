package fm.lizhi.ocean.wave.management.remote.service.activitycenter;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetBaseActivityConfig;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 活动公共服务远程处理器
 */
@Component
@Slf4j
public class ActivityCommonServiceRemote {

    @Autowired
    private ActivityCommonService activityCommonService;

    /**
     * 获取基础活动配置
     *
     * @param appId 应用id
     * @return 结果
     */
    public Result<ResponseGetBaseActivityConfig> getBaseActivityConfig(int appId) {
        log.info("getBaseActivityConfig appId:{}", appId);
        Result<ResponseGetBaseActivityConfig> result = activityCommonService.getBaseActivityConfig(appId);
        if (ResultUtils.isSuccess(result)) {
            log.info("getBaseActivityConfig success, target:{}", result.target());
        } else {
            log.warn("getBaseActivityConfig fail, rCode:{}, message: {}", result.rCode(), result.getMessage());
        }
        return result;
    }
}
