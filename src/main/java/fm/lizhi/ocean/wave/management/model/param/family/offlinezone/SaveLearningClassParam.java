package fm.lizhi.ocean.wave.management.model.param.family.offlinezone;

import fm.lizhi.ocean.wave.server.common.validator.EnumValue;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneLearningClassStatusEnum;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneLearningClassTypeEnum;
import lombok.Data;
import org.codehaus.plexus.util.StringUtils;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.beans.Transient;
import java.util.List;
import java.util.Objects;

/**
 * 保存学习课堂参数, 作为新增和更新的基类
 */
@Data
public abstract class SaveLearningClassParam {

    /**
     * 标题
     */
    @NotEmpty(message = "标题不能为空")
    @Size(max = 100, message = "标题长度不能超过{max}个字")
    private String title;

    /**
     * 类型
     */
    @NotNull(message = "类型不能为空")
    @EnumValue(OfflineZoneLearningClassTypeEnum.class)
    private Integer type;

    /**
     * 文件链接
     */
    @NotEmpty(message = "文件链接不能为空")
    private String fileUrl;

    /**
     * 文件封面
     */
    private String fileCover;

    /**
     * 权重
     */
    @NotNull(message = "权重不能为空")
    private Integer weight;

    /**
     * 状态
     */
    @NotNull(message = "状态不能为空")
    @EnumValue(OfflineZoneLearningClassStatusEnum.class)
    private Integer status;

    /**
     * 标签
     */
    @Size(max = 100, message = "标签长度不能超过{max}个字")
    private String label;

    /**
     * 标签颜色
     */
    @Size(max = 100, message = "标签颜色长度不能超过{max}个字")
    private String labelColor;

    /**
     * 白名单ID列表
     */
    private List<Long> whiteIds;

    @AssertTrue(message = "类型为视频文件时封面不能为空")
    @Transient
    protected boolean isFileCoverNotEmpty() {
        if (Objects.equals(type, OfflineZoneLearningClassTypeEnum.VIDEO_FILE.getValue())) {
            return StringUtils.isNotBlank(fileCover);
        }
        return true;
    }
}
