package fm.lizhi.ocean.wave.management.model.result.activitycenter;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 基础枚举配置
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class ActivityAutoConfigResourceResult extends ActivityBaseConfig {

    /**
     * 资源编码
     */
    private String resourceCode;

    /**
     * 配置类型
     */
    private int deployType;

    /**
     * 是否支持上传图片
     */
    private boolean supportUpload;

}
