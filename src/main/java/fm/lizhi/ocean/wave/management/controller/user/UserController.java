package fm.lizhi.ocean.wave.management.controller.user;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.management.manager.UserManager;
import fm.lizhi.ocean.wave.management.model.param.user.GetUserParam;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.user.SearchUserVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/18 14:09
 */
@RestController
@RequestMapping("user")
public class UserController {

    @Autowired
    private UserManager userManager;

    /**
     * 查询用户
     * @param param
     * @return
     */
    @GetMapping("getUser")
    public ResultVO<List<SearchUserVO>> getUser(GetUserParam param){
        Integer appId = SessionExtUtils.getAppId();
        if (appId == null) {
            return ResultVO.failure("appId is null");
        }
        SearchUserVO searchUserVO = userManager.getUser(appId, param);
        if (searchUserVO == null) {
            return ResultVO.success(Collections.emptyList());
        }
        return ResultVO.success(Lists.newArrayList(searchUserVO));
    }

}
