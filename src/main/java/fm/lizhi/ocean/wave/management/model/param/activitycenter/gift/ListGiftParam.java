package fm.lizhi.ocean.wave.management.model.param.activitycenter.gift;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import fm.lizhi.ocean.wavecenter.api.gift.constants.GiftStatusEnum;
import lombok.Data;

/**
 * 分页查询礼物请求的参数
 */
@Data
public class ListGiftParam {

    /**
     * 分页页码
     */
    @JsonSetter(nulls = Nulls.SKIP)
    private Integer pageNo = 1;

    /**
     * 分页大小
     */
    @JsonSetter(nulls = Nulls.SKIP)
    private Integer pageSize = 50;

    /**
     * 礼物ID
     */
    private Long id;

    /**
     * 礼物名称
     */
    private String name;

    /**
     * 礼物状态
     *
     * @see GiftStatusEnum
     */
    private Integer status;
}
