package fm.lizhi.ocean.wave.management.controller.message;

import fm.lizhi.ocean.wave.management.manager.message.WaveNoticeManager;
import fm.lizhi.ocean.wave.management.model.param.wave.notice.EnableOrDisableNoticeParam;
import fm.lizhi.ocean.wave.management.model.param.wave.notice.SaveWaveNoticeParam;
import fm.lizhi.ocean.wave.management.model.result.wave.notice.WaveNoticeResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/notice")
public class WaveNoticeController {

    @Autowired
    private WaveNoticeManager waveNoticeManager;

    @PostMapping("/saveWaveNotice")
    public ResultVO<Void> saveWaveNotice(@RequestBody @Valid SaveWaveNoticeParam param) {
        Integer appId = SessionExtUtils.getAppId();
        return waveNoticeManager.saveOrUpdateWaveNotice(param, appId);
    }

    @PostMapping("/enableNotice")
    public ResultVO<Void> enableNotice(@RequestBody @Valid EnableOrDisableNoticeParam param) {
        Integer appId = SessionExtUtils.getAppId();
        return waveNoticeManager.enableOrDisableNotice(param, appId);
    }

    @GetMapping("/getWaveNoticeList")
    public ResultVO<PageVO<WaveNoticeResult>> getWaveNoticeList(@RequestParam Integer pageSize,
                                                                @RequestParam Integer pageNo,
                                                                @RequestParam(required = false) Boolean enable) {
        Integer appId = SessionExtUtils.getAppId();
        return waveNoticeManager.getWaveNoticeList(pageSize, pageNo, enable, appId);
    }

    @GetMapping("/getLatestWaveNotice")
    public ResultVO<WaveNoticeResult> getLatestWaveNotice() {
        Integer appId = SessionExtUtils.getAppId();
        return waveNoticeManager.getLatestWaveNotice(appId);
    }
}
