package fm.lizhi.ocean.wave.management.model.param.family;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/14 16:54
 */
@Data
@Accessors(chain = true)
public class SaveLevelConfigParam {

    /**
     * Id 仅编辑的时候需要
     */
    private Long id;

    /**
     * 等级名称
     */
    @NotBlank(message = "等级名称不可为空")
    private String levelName;

    /**
     * 最小流水
     */
    @NotNull(message = "最小流水不可为空")
    private Integer minIncome;

    /**
     * 角标
     */
    private String levelIcon;

    /**
     * 勋章
     */
    @NotBlank(message = "勋章不可为空")
    private String levelMedal;

    /**
     * 奖励图片
     */
    private List<String> awardImgs;

    /**
     * 主题色不可为空
     */
    @NotBlank(message = "主题色不可为空")
    private String themColor;

    /**
     * 背景颜色不可为空
     */
    @NotBlank(message = "背景色不可为空")
    private String backgroundColor;

}
