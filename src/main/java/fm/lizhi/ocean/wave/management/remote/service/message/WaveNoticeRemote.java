package fm.lizhi.ocean.wave.management.remote.service.message;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.ocean.wave.platform.api.platform.service.WaveAnnouncementManagementService;
import fm.lizhi.ocean.wave.platform.api.platform.bean.WaveAnnouncementBean;
import fm.lizhi.ocean.wave.platform.api.platform.request.RequestSaveOrUpdateAnnouncement;
import fm.lizhi.ocean.wave.platform.api.platform.response.ResponseAnnouncementList;
import fm.lizhi.ocean.wave.platform.api.platform.request.RequestEnableAnnouncement;
import fm.lizhi.commons.service.client.pojo.Result;


/**
 * Wave公告相关远程服务
 */
@Component
public class WaveNoticeRemote {

    @Autowired
    private WaveAnnouncementManagementService waveAnnouncementManagementService;

    public Result<WaveAnnouncementBean> saveOrUpdateWaveAnnouncement(RequestSaveOrUpdateAnnouncement param) {
        return waveAnnouncementManagementService.saveOrUpdateWaveAnnouncement(param);
    }

    public Result<Boolean> enableAnnouncement(RequestEnableAnnouncement param) {
        return waveAnnouncementManagementService.enableAnnouncement(param);
    }

    public Result<Boolean> disableAnnouncement(RequestEnableAnnouncement param) {
        return waveAnnouncementManagementService.disableAnnouncement(param);
    }

    public Result<ResponseAnnouncementList> getWaveAnnouncementRelation(Boolean enable, Integer pageSize, Integer pageNo) {
        return waveAnnouncementManagementService.getWaveAnnouncementRelation(enable, pageSize, pageNo);
    }

    public Result<WaveAnnouncementBean> getLatestWaveNotice(Integer appId) {
        return waveAnnouncementManagementService.getLatestWaveNotice(appId);
    }
} 