package fm.lizhi.ocean.wave.management.datastore.dao.singer;

import com.google.common.collect.Lists;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.management.datastore.mapper.SingerInfoExtraMapper;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfoExample;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerWhiteListConfig;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerInfoMapper;
import io.shardingsphere.core.routing.router.masterslave.MasterSlaveRouteOnceVisitedHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class SingerInfoDao {


    @Resource
    private SingerInfoMapper singerInfoMapper;

    @Resource
    private SingerInfoExtraMapper singerInfoExtraMapper;

    @Resource
    private SingerWhiteListDao singerWhiteListDao;

    /**
     * 根据用户ID和应用ID以及歌手类型查询歌手信息
     *
     * @param appId      应用ID
     * @param userId     用户ID
     * @param singerType 歌手类型
     * @param fromMaster 是否从主库查询
     * @return 歌手信息列表
     */
    public SingerInfo getSingerInfo(int appId, Long userId, Integer singerType, boolean fromMaster) {
        if (fromMaster) {
            //强行走主库
            MasterSlaveRouteOnceVisitedHolder.routeMaster();
        }
        SingerInfo entity = new SingerInfo();
        entity.setUserId(userId);
        entity.setAppId(appId);
        entity.setSingerType(singerType);
        entity.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return singerInfoMapper.selectOne(entity);
    }

    /**
     * 删除用户指定歌手类型的数据
     *
     * @param appId          appId
     * @param userId         用户ID
     * @param singerTypeList 歌手类型列表
     * @return 结果
     */
    public boolean deleteSingerInfo(int appId, long userId, List<Integer> singerTypeList) {
        SingerInfoExample example = new SingerInfoExample();
        SingerInfoExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(userId)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andSingerTypeIn(singerTypeList);
        return singerInfoMapper.deleteByExample(example) > 0;
    }

    /**
     * 修改歌手状态，不存在就插入
     *
     * @param currentStatus 当前状态
     * @param singerInfo    歌手信息
     * @return 是否修改成功
     */
    public boolean updateSingerStatusOrInsert(Integer currentStatus, SingerInfo singerInfo) {
        if (singerInfo == null) {
            //部分场景不需要操作歌手库
            return true;
        }

        // 当前状态为空，则说明没有歌手库信息，直接插入
        if (currentStatus == null) {
            return singerInfoMapper.insert(singerInfo) > 0;
        }
        SingerInfo info = new SingerInfo();
        info.setId(singerInfo.getId());
        info.setSingerStatus(singerInfo.getSingerStatus());
        info.setOperator(singerInfo.getOperator());
        info.setOriginalSinger(singerInfo.getOriginalSinger());
        info.setSongStyle(singerInfo.getSongStyle());
        info.setSingerVerifyId(singerInfo.getSingerVerifyId());
        info.setContactNumber(singerInfo.getContactNumber() == null ? "" : singerInfo.getContactNumber());
        if (singerInfo.getEliminationTime() != null) {
            info.setEliminationTime(singerInfo.getEliminationTime());
        }
        if (singerInfo.getAuditTime() != null) {
            info.setAuditTime(singerInfo.getAuditTime());
        }

        // 如果当前要修改的状态不等于淘汰，清空淘汰时间和淘汰原因
        if (singerInfo.getSingerStatus() != SingerStatusEnum.ELIMINATED.getStatus()) {
            info.setEliminationTime(null);
            info.setEliminationReason("");
        }

        if (singerInfo.getNjId() != null && singerInfo.getNjId() > 0) {
            info.setNjId(singerInfo.getNjId());
        }

        if (singerInfo.getFamilyId() != null && singerInfo.getFamilyId() > 0) {
            info.setFamilyId(singerInfo.getFamilyId());
        }
        // 否则，修改歌手状态
        return singerInfoExtraMapper.updateSingerStatus(info, currentStatus) > 0;
    }

    /**
     * 根据歌手ID和歌手类型，修改家族ID和厅主ID
     *
     * @param appId      应用ID
     * @param singerId   歌手ID
     * @param singerType 歌手类型
     * @param familyId   家族ID
     * @param njId       厅主ID
     * @return 是否修改成功
     */
    public boolean updateSingerFamilyAndNjId(Integer appId, Long singerId, Integer singerType, Long familyId, Long njId) {
        SingerInfo info = new SingerInfo();
        info.setFamilyId(familyId);
        info.setNjId(njId);
        SingerInfoExample example = new SingerInfoExample();
        SingerInfoExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(singerId)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andSingerTypeEqualTo(singerType);
        return singerInfoMapper.updateByExample(info, example) > 0;
    }

    /**
     * 根据ID列表获取
     *
     * @param appId 应用ID
     * @param ids   歌手ID列表
     * @return 歌手信息列表
     */
    public List<SingerInfo> getSingerInfoByIds(int appId, List<Long> ids, List<SingerStatusEnum> singerStatus) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        SingerInfoExample example = new SingerInfoExample();
        SingerInfoExample.Criteria criteria = example.createCriteria()
                .andIdIn(ids)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        if (CollectionUtils.isNotEmpty(singerStatus)) {
            criteria.andSingerStatusIn(singerStatus.stream().map(SingerStatusEnum::getStatus).collect(Collectors.toList()));
        }

        return singerInfoMapper.selectByExample(example);
    }

    /**
     * 根据userIds列表获取
     *
     * @param appId   应用ID
     * @param userIds 用户ID列表
     * @return 歌手信息列表
     */
    public List<SingerInfo> getSingerInfoByUserIds(int appId, List<Long> userIds, List<SingerStatusEnum> singerStatus, SingerTypeEnum singerType, boolean fromMaster) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        if (fromMaster) {
            //强行走主库
            MasterSlaveRouteOnceVisitedHolder.routeMaster();
        }
        SingerInfoExample example = new SingerInfoExample();
        SingerInfoExample.Criteria criteria = example.createCriteria()
                .andUserIdIn(userIds)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        if (CollectionUtils.isNotEmpty(singerStatus)) {
            criteria.andSingerStatusIn(singerStatus.stream().map(SingerStatusEnum::getStatus).collect(Collectors.toList()));
        }

        if (singerType != null) {
            criteria.andSingerTypeEqualTo(singerType.getType());
        }

        return singerInfoMapper.selectByExample(example);
    }

    /**
     * 根据userIds列表获取
     *
     * @param appId   应用ID
     * @param userIds 用户ID列表
     * @return 歌手信息列表
     */
    public List<SingerInfo> getSingerInfoNotInSingerType(int appId, List<Long> userIds, List<SingerStatusEnum> singerStatus, SingerTypeEnum singerType) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        SingerInfoExample example = new SingerInfoExample();
        SingerInfoExample.Criteria criteria = example.createCriteria()
                .andUserIdIn(userIds)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        if (CollectionUtils.isNotEmpty(singerStatus)) {
            criteria.andSingerStatusIn(singerStatus.stream().map(SingerStatusEnum::getStatus).collect(Collectors.toList()));
        }

        if (singerType != null) {
            criteria.andSingerTypeNotEqualTo(singerType.getType());
        }

        return singerInfoMapper.selectByExample(example);
    }

    /**
     * 查询歌手信息
     *
     * @param appId            应用ID
     * @param singerIds        歌手ID列表
     * @param singerTypeList   歌手类型列表
     * @param singerStatusList 歌手状态列表
     * @return 歌手信息列表
     */
    public List<SingerInfo> getSingerInfoBySingerId(Integer appId, List<Long> singerIds, List<Integer> singerTypeList, List<Integer> singerStatusList) {
        //根据歌手ID、类型以及状态查询出歌手信息
        SingerInfoExample example = new SingerInfoExample();
        SingerInfoExample.Criteria criteria = example.createCriteria()
                .andUserIdIn(singerIds)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        if (CollectionUtils.isNotEmpty(singerTypeList)) {
            criteria.andSingerTypeIn(singerTypeList);
        }
        if (CollectionUtils.isNotEmpty(singerStatusList)) {
            criteria.andSingerStatusIn(singerStatusList);
        }
        return singerInfoMapper.selectByExample(example);
    }

    /**
     * 查询不在白名单的歌手
     *
     * @param singerIds        歌手ID列表
     * @param appId            应用ID
     * @param singerTypeList   歌手类型
     * @param singerStatusList 歌手状态
     * @return 不在白名单的歌手信息列表
     */
    public List<SingerInfo> getSingerListNotInWhiteList(Integer appId, List<Long> singerIds, List<Integer> singerTypeList, List<Integer> singerStatusList) {
        //根据歌手ID、类型以及状态查询出歌手信息
        List<SingerInfo> singerInfoList = getSingerInfoBySingerId(appId, singerIds, singerTypeList, singerStatusList);
        //根据歌手ID查询出白名单信息
        List<SingerWhiteListConfig> singerWhiteListConfigList = singerWhiteListDao.getSingerWhiteListConfigBySingerIds(appId, singerIds);
        if (CollectionUtils.isEmpty(singerWhiteListConfigList)) {
            return singerInfoList;
        }
        Map<String, SingerWhiteListConfig> singerWhiteListConfigMap = singerWhiteListConfigList.stream()
                .collect(Collectors.toMap(whiteList -> whiteList.getSingerId() + "_" + whiteList.getSingerType(), Function.identity()));
        //过滤出歌手ID和类型都不在白名单中的歌手信息
        return singerInfoList.stream()
                .filter(singerInfo -> !singerWhiteListConfigMap.containsKey(singerInfo.getUserId() + "_" + singerInfo.getSingerType()))
                .collect(Collectors.toList());
    }

    /**
     * 查询歌手列表
     *
     * @param appId    应用ID
     * @param singerId 歌手ID
     * @return 歌手列表
     */
    public List<SingerInfo> getSingerInfoBySingerId(Integer appId, Long singerId) {
        SingerInfoExample example = new SingerInfoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andSingerStatusIn(Lists.newArrayList(SingerStatusEnum.EFFECTIVE.getStatus(), SingerStatusEnum.AUTHENTICATING.getStatus()))
                .andUserIdEqualTo(singerId);
        return singerInfoMapper.selectByExample(example);
    }

}
