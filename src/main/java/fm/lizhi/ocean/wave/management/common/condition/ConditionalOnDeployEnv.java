package fm.lizhi.ocean.wave.management.common.condition;

import com.ctrip.framework.apollo.core.enums.Env;
import org.springframework.context.annotation.Conditional;

import java.lang.annotation.*;

/**
 * 部署环境条件判断注解, 如果当前部署环境与注解中的任一值匹配, 则条件成立.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Documented
@Conditional(OnDeployEnvCondition.class)
public @interface ConditionalOnDeployEnv {

    /**
     * 匹配的部署环境, 如果当前部署环境与注解中的任一值匹配, 则条件成立.
     *
     * @return 匹配的部署环境
     */
    Env[] value();
}
