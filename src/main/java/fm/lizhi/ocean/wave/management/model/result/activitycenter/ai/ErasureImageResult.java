package fm.lizhi.ocean.wave.management.model.result.activitycenter.ai;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 部分擦除结果
 */
@Data
public class ErasureImageResult {

    /**
     * ai任务批次id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long serialId;

    /**
     * 图片ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long imageId;

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * 图片路径
     */
    private String imagePath;

    /**
     * 是否成功生成图片
     */
    private boolean success;

    /**
     * 错误提示信息
     */
    private String errorMsg;
}
