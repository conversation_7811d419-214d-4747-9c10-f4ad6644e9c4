package fm.lizhi.ocean.wave.management.model.converter.grow;

import fm.lizhi.ocean.wave.management.model.param.grow.capability.SaveCapabilityParam;
import fm.lizhi.ocean.wavecenter.api.grow.capability.request.RequestSaveCapability;
import fm.lizhi.ocean.wavecenter.api.grow.capability.response.ResponseCapability;
import fm.lizhi.ocean.wave.management.model.vo.grow.capability.CapabilityManageVO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * 能力项参数转换器
 * 负责 SaveCapabilityParam -> RequestSaveCapability 的转换
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface CapabilityConvert {
    CapabilityConvert INSTANCE = Mappers.getMapper(CapabilityConvert.class);

    RequestSaveCapability toRequestSaveCapability(SaveCapabilityParam param);

    @Mapping(target = "modifyUser", source = "operator")
    CapabilityManageVO toCapabilityManageVO(ResponseCapability capability);

    
    List<CapabilityManageVO> toCapabilityManageVOList(List<ResponseCapability> capabilityList);
} 