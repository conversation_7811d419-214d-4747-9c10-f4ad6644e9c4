package fm.lizhi.ocean.wave.management.controller.family.offlinezone;

import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import fm.lizhi.ocean.wave.management.manager.family.offlinezone.ProtectionHistoryManager;
import fm.lizhi.ocean.wave.management.model.param.family.offlinezone.GetProtectionHistoryParam;
import fm.lizhi.ocean.wave.management.model.result.family.offlinezone.ProtectionHistoryResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 跳槽保护协议
 * <AUTHOR>
 */
@RestController
@RequestMapping("/offline/protection")
@Slf4j
public class ProtectionController {

    @Autowired
    private ProtectionHistoryManager protectionHistoryManager;

    /**
     * 查询跳槽保护协议历史记录（不分页）
     *
     * @param param 查询参数
     * @return 查询结果的VO
     */
    @GetMapping("/history")
    public ResultVO<List<ProtectionHistoryResult>> listProtectionHistory(@Validated GetProtectionHistoryParam param) {
        log.info("查询跳槽保护协议历史记录，参数: {}", param);

        Integer appId = SessionExtUtils.getAppId();
        param.setAppId(appId);

        try {

            List<ProtectionHistoryResult> resultList = protectionHistoryManager.listProtectionHistory(param);
            return ResultVO.success(resultList);
        } catch (Exception e) {
            log.error("查询跳槽保护协议历史记录失败", e);
            return ResultVO.failure("查询失败");
        }
    }

}