package fm.lizhi.ocean.wave.management.remote.service.activitycenter;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ActivityToolsConfigConvert;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityToolParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityToolParam;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityToolsInfoBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityTools;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityTools;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityToolsConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityToolsConfigServiceRemote {

    @Autowired
    private ActivityToolsConfigService activityToolsConfigService;



    /**
     * 保存工具
     */
    public Result<Boolean> save(SaveActivityToolParam param, Integer appId, String operator) {
        RequestSaveActivityTools request = ActivityToolsConfigConvert.I.buildRequestSaveActivityTools(param, appId, operator);

        Result<Boolean> result = activityToolsConfigService.saveTools(request);
        if (ResultUtils.isFailure(result) || !result.target()){
            log.warn("save activity tools failed, result:{}", result);
        }
        return result;
    }

    /**
     * 更新工具
     */
    public Result<Boolean> update(UpdateActivityToolParam param, Integer appId, String operator) {
        RequestUpdateActivityTools request = ActivityToolsConfigConvert.I.buildRequestUpdateActivityTools(param, appId, operator);

        Result<Boolean> result = activityToolsConfigService.updateTools(request);
        if (ResultUtils.isFailure(result) || !result.target()){
            log.warn("update activity tools failed, result:{}", result);
        }
        return result;
    }

    /**
     * 查询工具列表
     */
    public Result<List<ActivityToolsInfoBean>> list(Integer appId) {
        Result<List<ActivityToolsInfoBean>> result = activityToolsConfigService.listByAppId(appId);
        if (ResultUtils.isFailure(result)){
            log.warn("list activity tools failed, result:{}", result);
        }
        return result;
    }
}
