package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * @description: 活动厅主信息
 * @author: guoyibin
 * @create: 2024/10/23 21:20
 */
@Data
public class ActivityNjInfoResult {

    /**
     * 活动厅主ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 活动厅主名
     */
    private String name;

    /**
     * 活动厅主波段号
     */
    private String band;
}
