package fm.lizhi.ocean.wave.management.model.result.award.singer;

import fm.lizhi.ocean.wave.management.model.vo.award.singer.SingerDecorateRuleVO;
import fm.lizhi.ocean.wave.management.model.vo.resource.DecorateInfoVO;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 歌手装扮规则配置响应体
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SingerDecorateRuleResult {


    /**
     * 装扮规则
     */
    private SingerDecorateRuleVO rule;

    /**
     * 装扮信息
     */
    private DecorateInfoVO decorateInfo;


}
