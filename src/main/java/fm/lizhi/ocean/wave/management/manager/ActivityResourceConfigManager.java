package fm.lizhi.ocean.wave.management.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ActivityResourceConfigConvert;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.PageActivityResourcesParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityResourceParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityResourceParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.GetActivityResourceResult;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.PageActivityResourceResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.remote.service.activitycenter.ActivityResourceConfigServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestPageActivityResources;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityResource;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityResourceConfigManager {

    @Autowired
    private ActivityResourceConfigServiceRemote activityResourceConfigServiceRemote;


    public ResultVO<Void> save(SaveActivityResourceParam param, Integer appId, String operator) {

        RequestSaveActivityResource request = ActivityResourceConfigConvert.I.toRequestSaveActivityResource(param, appId, operator);
        Result<Void> result = activityResourceConfigServiceRemote.save(request);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }

        return ResultVO.success();
    }


    public ResultVO<Void> update(UpdateActivityResourceParam param, Integer appId, String operator) {
        RequestUpdateActivityResource request = ActivityResourceConfigConvert.I.toRequestUpdateActivityResource(param, appId, operator);
        Result<Void> result = activityResourceConfigServiceRemote.update(request);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success();
    }

    public ResultVO<Void> delete(Long id, Integer appId, String operator) {
        Result<Void> result = activityResourceConfigServiceRemote.delete(id, appId, operator);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }
        return ResultVO.success();
    }

    public ResultVO<PageVO<PageActivityResourceResult>> list(Integer appId, PageActivityResourcesParam param) {
        RequestPageActivityResources request = ActivityResourceConfigConvert.I.toRequestPageActivityResources(appId, param);
        Result<PageBean<ResponseActivityResource>> result = activityResourceConfigServiceRemote.list(request);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }

        //西米特殊处理，将官频位的资源显示的名称进行转换（不使用mapstruct处理，防止逻辑无法清晰化）
        resetOfficeSeatResourceShowName(result.target().getList(), appId);
        return ResultVO.success(ActivityResourceConfigConvert.I.toPageActivityResourceResultPageVO(result.target()));
    }

    public ResultVO<List<GetActivityResourceResult>> listByLevelId(Long levelId, Integer appId) {
        Result<List<ResponseActivityResource>> result = activityResourceConfigServiceRemote.listActivityResourceByLevelId(appId, levelId);
        if (ResultUtils.isFailure(result)){
            return ResultVO.failure(result);
        }

        //西米特殊处理，将官频位的资源显示的名称进行转换（不使用mapstruct处理，防止逻辑无法清晰化）
        resetOfficeSeatResourceShowName(result.target(), appId);
        return ResultVO.success(ActivityResourceConfigConvert.I.toGetActivityResourceResult(result.target()));
    }

    public void resetOfficeSeatResourceShowName(List<ResponseActivityResource> list, Integer appId) {
        if (BusinessEvnEnum.XIMI.getAppId() != appId) {
            return;
        }

        list.stream().filter(item -> Objects.equals(item.getResourceCode(), AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode()))
                .forEach(item -> {
                    item.setName("官频位（含节目单）");
                });
    }
}
