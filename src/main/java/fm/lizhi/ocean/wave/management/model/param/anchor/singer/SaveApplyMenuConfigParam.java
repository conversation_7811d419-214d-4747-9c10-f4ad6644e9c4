package fm.lizhi.ocean.wave.management.model.param.anchor.singer;

import fm.lizhi.ocean.wave.management.common.validation.EnumValue;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.model.MenuVisibleRuleConfigDTO;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.constant.SingerVisibleConditionEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 保存申请菜单配置参数
 */
@Data
public class SaveApplyMenuConfigParam {
    
    /**
     * 歌手类型
     */
    private Integer singerType;
    
    /**
     * 是否启用
     */
    @NotNull(message = "总开关不能为空")
    private Boolean enabled;
    
    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    private Long startTime;
    
    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    private Long endTime;

    /**
     * 菜单配置
     */
    private List<@Valid MenuVisibleRuleConfig> bizRuleConfigs;


    /**
     * 菜单可见性规则配置
     */
    @Data
    @Accessors(chain = true)
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MenuVisibleRuleConfig {
        /**
         * 规则编码
         */
        @NotNull(message = "请选择规则编码")
        @NotBlank(message = "请选择规则编码")
        @EnumValue(SingerVisibleConditionEnum.class)
        private String ruleCode;

        /**
         * 是否启用
         */
        @NotNull(message = "bizRuleConfigs.enabled不能为空")
        private Boolean enabled;

        /**
         * 显示数量
         */
        private Integer count;
    }
}
