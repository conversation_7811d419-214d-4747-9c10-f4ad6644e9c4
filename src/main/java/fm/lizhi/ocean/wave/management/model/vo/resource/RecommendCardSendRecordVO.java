package fm.lizhi.ocean.wave.management.model.vo.resource;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/17 19:44
 */
@Data
public class RecommendCardSendRecordVO {

    /**
     * 发放记录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    private String userBand;

    private String userName;

    private String userRole;

    private String familyName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;

    private Integer sendNum;

    private String reason;

    /**
     * 发放方式
     */
    private String sendType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;

    /**
     * 状态
     */
    private Integer status;

    private String operator;

}
