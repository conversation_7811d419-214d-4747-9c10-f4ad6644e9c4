package fm.lizhi.ocean.wave.management.model.result.anchor.singer;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SingerHallSummaryInfoResult {
    
    private Integer allIncomeSingerCnt;

    private Integer allSingerCnt;


    /**
     * 高级歌手数量
     */
    private int seniorSingerCnt;

    /**
     * 有收入的高级歌手数量
     */
    private int allIncomeSeniorSingerCnt;

    /**
     * 原创高级歌手数量
     */
    private int originalSeniorSinger;

    /**
     * 有收入的原创高级歌手数量
     */
    private int allIncomeOriginalSeniorSingerCnt;
}
