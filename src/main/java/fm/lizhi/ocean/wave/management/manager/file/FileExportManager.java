package fm.lizhi.ocean.wave.management.manager.file;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.file.FileExportVO;
import fm.lizhi.ocean.wave.management.model.vo.file.MsgTipExcelVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/26 16:18
 */
@Slf4j
@Component
public class FileExportManager {

    /**
     * xlsx文件mime类型
     */
    public static final String XLSX_MIME = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

    /**
     * 空页查询次数限制
     */
    public static final int EMPTY_PAGE_COUNT_LIMIT = 10;

    /**
     * 导出提示信息
     * 用于处理异常情况，在导出的文件中说明异常
     * @param response
     * @param msg
     */
    public void msgTip(HttpServletResponse response, String msg) throws IOException{
        // 设置响应头
        processHttpResponse("导出错误", response);

        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream(), MsgTipExcelVO.class)
                .excelType(ExcelTypeEnum.XLSX)
                .build();
        WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();

        List<MsgTipExcelVO> list = Lists.newArrayList(new MsgTipExcelVO().setErrorMsg(msg));

        excelWriter.write(list, writeSheet);
        excelWriter.finish();
    }

    /**
     * 导出excel文件到http响应
     * @param head
     * @param fileName 不需要文件类型后缀
     * @param response
     * @param dataQuery
     * @param <T>
     * @throws IOException
     */
    public <T> void exportToHttpResponse(FileExportVO<T> fileExportVO, HttpServletResponse response, DataQuery<T> dataQuery) throws IOException {
        // 设置响应头
        processHttpResponse(fileExportVO.getFileName(), response);

        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream(), fileExportVO.getHead())
                .excelType(ExcelTypeEnum.XLSX)
                .build();
        WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();

        // 先写入一个空列表，确保表头被写入
        excelWriter.write(new ArrayList<>(), writeSheet);

        boolean lastPage = false;
        int emptyPageCount = 0;

        while (!lastPage) {
            // 查数据
            PageVO<T> tPageVO = dataQuery.pageList(fileExportVO.getPageNo(), fileExportVO.getPageSize());
            int total = tPageVO.getTotal();
            List<T> list = tPageVO.getList();
            log.info("exportToHttpResponse pageList. pageNo={},pageSize={},total={}", fileExportVO.getPageNo(), fileExportVO.getPageSize(), total);

            if (list.isEmpty() && total == 0) {
                emptyPageCount++;
                if (emptyPageCount > EMPTY_PAGE_COUNT_LIMIT) {
                    log.warn("exportToHttpResponse fileName={},pageNo={},pageSize={}, total={}, emptyPageCount={}", fileExportVO.getFileName(), fileExportVO.getPageNo(), fileExportVO.getPageSize(), total, emptyPageCount);
                    break;
                }
                continue;
            }

            // 写入数据
            excelWriter.write(list, writeSheet);

            // 翻页,如果只是查询当前页，则不需要翻页
            if (!fileExportVO.isQueryAll() || fileExportVO.getPageNo() * fileExportVO.getPageSize() >= total) {
                log.info("exportToHttpResponse fileName={},pageNo={},pageSize={}, total={}", fileExportVO.getFileName(), fileExportVO.getPageNo(), fileExportVO.getPageSize(), total);
                lastPage = true;
            }
            fileExportVO.setPageNo(fileExportVO.getPageNo() + 1);
        }

        // 关闭资源
        excelWriter.finish();
    }

    private void processHttpResponse(String fileName, HttpServletResponse response) throws UnsupportedEncodingException {
        response.setContentType(XLSX_MIME);
        response.setCharacterEncoding("utf-8");
        String exportedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + exportedFileName + ExcelTypeEnum.XLSX.getValue());
    }

    /**
     * 数据查询接口
     * @param <T>
     */
    @FunctionalInterface
    public interface DataQuery<T>{
        PageVO<T> pageList(int pageNo, int pageSize);
    }

}
