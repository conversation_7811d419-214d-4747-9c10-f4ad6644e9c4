package fm.lizhi.ocean.wave.management.remote.service.activitycenter;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityRecommendCardConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRecommendCard;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRecommendCard;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityRecommendCardConfigService;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityRecommendCardConfigServiceRemote {

    @Autowired
    private ActivityRecommendCardConfigService activityRecommendCardConfigService;


    public Result<Void> save(RequestSaveActivityRecommendCard param){
        Result<Void> result = activityRecommendCardConfigService.saveRecommendCard(param);
        if (ResultUtils.isFailure(result)){
            log.warn("save activity recommend card config failed. appId:{}, operator:{}, count:{}, rCode:{}",
                    param.getAppId(), param.getOperator(), param.getCount(), result.rCode());
        }

        return result;
    }

    public Result<Void> update(RequestUpdateActivityRecommendCard param){
        Result<Void> result = activityRecommendCardConfigService.updateRecommendCard(param);
        if (ResultUtils.isFailure(result)){
            log.warn("update activity recommend card config failed. id:{}, appId:{}, operator:{}, count:{}, rCode:{}",
                    param.getId(), param.getAppId(), param.getOperator(), param.getCount(), result.rCode());
        }
        return result;
    }

    public Result<Void> delete(Long id, Integer appId, String operator){
        Result<Void> result = activityRecommendCardConfigService.deleteRecommendCard(id, appId, operator);
        if (ResultUtils.isFailure(result)){
            log.warn("delete activity recommend card config failed. id:{}, appId:{}, operator:{}", id, appId, operator);
        }

        return result;
    }

    public Result<List<ActivityRecommendCardConfigBean>> list(Integer appId){
        Result<List<ActivityRecommendCardConfigBean>> result = activityRecommendCardConfigService.listByAppId(appId);
        if (ResultUtils.isFailure(result)){
            log.warn("list activity recommend card config failed. appId:{}", appId);
        }
        return result;

    }



}
