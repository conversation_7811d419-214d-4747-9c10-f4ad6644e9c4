package fm.lizhi.ocean.wave.management.remote.service.activitycenter;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestGetOfficialSeatTimeBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ResponseGetOfficialSeatTimeBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityOfficialSeatTimeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 活动官频位时间服务远程调用
 */
@Component
@Slf4j
public class ActivityOfficialSeatTimeServiceRemote {

    @Autowired
    private ActivityOfficialSeatTimeService activityOfficialSeatTimeService;

    /**
     * 获取官频位时间列表
     *
     * @param request 请求
     * @return 结果
     */
    public Result<ResponseGetOfficialSeatTimeBean> getOfficialSeatTimeList(RequestGetOfficialSeatTimeBean request) {
        log.info("getOfficialSeatTimeList request: {}", request);
        Result<ResponseGetOfficialSeatTimeBean> result = activityOfficialSeatTimeService.getOfficialSeatTimeList(request);
        if (ResultUtils.isSuccess(result)) {
            log.info("getOfficialSeatTimeList success, timeList size: {}", CollectionUtils.size(result.target().getTimeList()));
        } else {
            log.info("getOfficialSeatTimeList failed. rCode: {}, message: {}", result.rCode(), result.getMessage());
        }
        return result;
    }
}
