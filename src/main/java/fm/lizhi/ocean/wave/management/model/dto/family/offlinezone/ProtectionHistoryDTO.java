package fm.lizhi.ocean.wave.management.model.dto.family.offlinezone;

import java.util.Date;

import lombok.Data;

/**
 * 跳槽保护协议历史记录DTO
 * <AUTHOR>
 */
@Data
public class ProtectionHistoryDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 跳槽保护表ID
     */
    private Long protectedId;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 公会ID
     */
    private Long familyId;

    /**
     * 厅ID
     */
    private Long njId;

    /**
     * 主播ID
     */
    private Long playerId;

    /**
     * 上传用户
     */
    private Long uploadUserId;

    /**
     * 协议生效开始时间
     */
    private Date agreementStartTime;

    /**
     * 协议生效结束时间
     */
    private Date agreementEndTime;

    /**
     * 协议更新时间
     */
    private Date agreementUpdateTime;

    /**
     * 是否盖章签字：0-否，1-是
     */
    private Boolean stampSign;

    /**
     * 环境：TEST/PRE/PRO
     */
    private String deployEnv;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 协议文件JSON
     */
    private String agreementFileJson;

}