package fm.lizhi.ocean.wave.management.manager.utility.webp;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import fm.lizhi.ocean.wave.management.config.apollo.UtilityConfig;
import fm.lizhi.ocean.wave.management.constants.BusinessEvnEnum;
import fm.lizhi.ocean.wave.management.manager.common.FeiShuBotManager;
import fm.lizhi.ocean.wave.management.model.dto.common.FeiShuCardMessageDTO;
import fm.lizhi.ocean.wave.management.model.param.common.FeiShuCardMessageParam;
import fm.lizhi.ocean.wave.management.model.result.common.FeiShuCardMessageResult;
import fm.lizhi.ocean.wave.management.model.result.utility.webp.WebpAsyncConvertResult;
import fm.lizhi.ocean.wave.management.util.RunnableUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.*;

/**
 * webp转换通知管理器.
 */
@Slf4j
@Component
public class WebpNotifyManager {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper().enable(SerializationFeature.INDENT_OUTPUT);

    @Autowired
    private UtilityConfig utilityConfig;

    @Autowired
    private FeiShuBotManager feiShuBotManager;

    private ThreadPoolExecutor threadPoolExecutor;

    @PostConstruct
    public void postConstruct() {
        int corePoolSize = 1;
        int maximumPoolSize = 1;
        long keepAliveTime = 0L;
        BlockingQueue<Runnable> blockingQueue = new ArrayBlockingQueue<>(1000);
        ThreadFactory threadFactory = new BasicThreadFactory.Builder().namingPattern("webp-convert-notify-%d").build();
        ThreadPoolExecutor.DiscardPolicy rejectHandler = new ThreadPoolExecutor.DiscardPolicy();
        threadPoolExecutor = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime, TimeUnit.MILLISECONDS,
                blockingQueue, threadFactory, rejectHandler);
    }

    @PreDestroy
    public void preDestroy() {
        threadPoolExecutor.shutdown();
    }

    /**
     * 发送webp转换失败通知.
     *
     * @param result 转换结果
     */
    public void sendConvertFailure(WebpAsyncConvertResult result) {
        try {
            String feiShuWebhook = utilityConfig.getWebp().getFeiShuWebhook();
            if (StringUtils.isBlank(feiShuWebhook)) {
                log.info("FeiShu webhook is empty, skip sending notification, result={}", result);
                return;
            }
            // 卡片头部
            FeiShuCardMessageDTO.Header header = buildFailureHeader(result.getAppId());
            // 卡片内容
            String json;
            try {
                json = OBJECT_MAPPER.writeValueAsString(result);
            } catch (JsonProcessingException | RuntimeException e) {
                log.info("webp转换结果序列化失败, result={}", result, e);
                return;
            }
            FeiShuCardMessageDTO.Markdown jsonMarkdown = buildJsonMarkdown(json);
            List<FeiShuCardMessageDTO.Element> elements = Collections.singletonList(jsonMarkdown);
            // 卡片
            FeiShuCardMessageDTO.Card card = new FeiShuCardMessageDTO.Card();
            card.setHeader(header);
            card.setElements(elements);
            // 消息
            FeiShuCardMessageDTO message = new FeiShuCardMessageDTO();
            message.setCard(card);
            // 提交线程池执行
            Runnable runnable = RunnableUtils.wrap(() -> sendCardMessage(message));
            threadPoolExecutor.execute(runnable);
        } catch (RuntimeException e) {
            log.info("Send webp convert failure notification failed, result={}", result, e);
        }
    }

    private FeiShuCardMessageDTO.Header buildFailureHeader(Integer appId) {
        BusinessEvnEnum businessEvnEnum = BusinessEvnEnum.from(appId);
        String businessName = businessEvnEnum != null ? businessEvnEnum.getCnName() : "业务";
        FeiShuCardMessageDTO.Title title = new FeiShuCardMessageDTO.Title();
        title.setContent(businessName + "webp转换失败通知");
        FeiShuCardMessageDTO.Header header = new FeiShuCardMessageDTO.Header();
        header.setTitle(title);
        return header;
    }

    private FeiShuCardMessageDTO.Markdown buildJsonMarkdown(String json) {
        FeiShuCardMessageDTO.Markdown markdown = new FeiShuCardMessageDTO.Markdown();
        markdown.setContent("```JSON\n" + json + "\n```");
        markdown.setTextAlign(FeiShuCardMessageDTO.Markdown.TEXT_ALIGN_LEFT);
        return markdown;
    }

    private void sendCardMessage(FeiShuCardMessageDTO message) {
        try {
            String feiShuWebhook = utilityConfig.getWebp().getFeiShuWebhook();
            if (StringUtils.isBlank(feiShuWebhook)) {
                log.info("FeiShu webhook is blank, skip sending message: {}", message);
                return;
            }
            FeiShuCardMessageParam param = new FeiShuCardMessageParam();
            param.setWebhook(feiShuWebhook);
            param.setCardMessage(message);
            log.info("Prepare to send message: {}", message);
            FeiShuCardMessageResult result = feiShuBotManager.sendCardMessage(param);
            if (result.isSuccess()) {
                log.info("Send message success");
            } else {
                log.error("Send message failed, code: {}, msg: {}", result.getCode(), result.getMsg());
            }
        } catch (RuntimeException e) {
            log.error("Send message failed, message: {}", message, e);
        }
    }
}
