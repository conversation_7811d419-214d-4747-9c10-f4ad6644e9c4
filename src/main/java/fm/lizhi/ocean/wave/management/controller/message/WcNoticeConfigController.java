package fm.lizhi.ocean.wave.management.controller.message;

import fm.lizhi.ocean.wave.management.model.param.message.WcNoticeUpdateStatusParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import fm.lizhi.ocean.wave.management.manager.message.WcNoticeConfigManager;
import fm.lizhi.ocean.wave.management.model.param.message.WcNoticeConfigParam;
import fm.lizhi.ocean.wave.management.model.param.message.WcNoticeConfigQueryParam;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.message.WcNoticeConfigResVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.sso.client.SessionUtils;

@RestController
@RequestMapping("/notice")
public class WcNoticeConfigController {

    @Autowired
    private WcNoticeConfigManager wcNoticeConfigManager;

    /**
     * 保存公告配置
     *
     * @param param 公告配置参数
     * @return 保存结果
     */
    @PostMapping("/saveWaveCenterNotice")
    public ResultVO<Void> saveWcNoticeConfig(@RequestBody WcNoticeConfigParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        return wcNoticeConfigManager.saveWcNoticeConfig(param, operator, appId);
    }

    /**
     * 查询公告配置列表
     *
     * @param param 查询参数
     * @return 分页结果
     */
    @GetMapping("/getWaveCenterNoticeList")
    public ResultVO<WcNoticeConfigResVO> getWaveCenterNoticeList(WcNoticeConfigQueryParam param) {
        Integer appId = SessionExtUtils.getAppId();
        return wcNoticeConfigManager.getWaveCenterNoticeList(param, appId);
    }

    /**
     * 删除公告配置
     *
     * @param id 公告配置ID
     * @return 删除结果，ResultVO<Void>
     */
    @PostMapping("/delWaveCenterNotice/{id}")
    public ResultVO<Void> delWaveCenterNotice(@PathVariable("id") Long id) {
        return wcNoticeConfigManager.deleteWaveCenterNotice(id);
    }

    /**
     * 上下架
     *
     * @param param 公告配置ID
     * @return 删除结果，ResultVO<Void>
     */
    @PostMapping("/updateWaveCenterNoticeStatus")
    public ResultVO<Void> updateWaveCenterNoticeStatus(@RequestBody WcNoticeUpdateStatusParam param) {
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        return wcNoticeConfigManager.updateNoticeStatus(param, operator, appId);
    }

}
