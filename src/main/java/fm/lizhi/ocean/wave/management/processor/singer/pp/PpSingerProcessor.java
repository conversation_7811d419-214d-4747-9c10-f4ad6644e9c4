package fm.lizhi.ocean.wave.management.processor.singer.pp;

import com.google.common.collect.Lists;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerInfoDao;
import fm.lizhi.ocean.wave.management.manager.singer.SingerPushManager;
import fm.lizhi.ocean.wave.management.processor.singer.ISingerProcessor;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateOperateReasonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class PpSingerProcessor implements ISingerProcessor {

    @Resource
    private SingerInfoDao singerInfoDao;

    @Autowired
    private SingerPushManager singerPushManager;

    @Autowired
    private GuidGenerator guidGenerator;

    @Override
    public List<Long> getRelatedEliminateSingerRecordIds(List<Long> userIds, int appId, int singerType) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }

        //判断当前要操作的歌手列表类型
        List<Integer> singerStatusList = Lists.newArrayList(SingerStatusEnum.EFFECTIVE.getStatus(), SingerStatusEnum.AUTHENTICATING.getStatus());
        if (singerType == SingerTypeEnum.NEW.getType()) {
            //高级歌手类型列表
            List<Integer> seniorSingerTypeList = Lists.newArrayList(SingerTypeEnum.QUALITY.getType(), SingerTypeEnum.STAR.getType());
            //如果是淘汰认证歌手，PP需要关联淘汰高级歌手(不在白名单中)
            List<SingerInfo> seniorSingerList = singerInfoDao.getSingerListNotInWhiteList(appId, userIds, seniorSingerTypeList, singerStatusList);
            //过滤出高级歌手的记录ID
            List<Long> relatedEliminateIds = seniorSingerList.stream().map(SingerInfo::getId).collect(Collectors.toList());
            log.info("PP getRelatedEliminateSingerIds: userIds={}, 关联淘汰歌手数量={}", userIds, relatedEliminateIds.size());
            return relatedEliminateIds;
        }
        //如果是高级歌手，就不关联淘汰认证歌手了
        return new ArrayList<>();
    }

    @Override
    public void reissueDecorateAfterEliminate(int appId, List<Long> singerIds, int eliminateSingerType, String operator) {
        if (eliminateSingerType == SingerTypeEnum.NEW.getType()) {
            //淘汰认证歌手，就没有补发操作
            return;
        }
        //如果是淘汰高级歌手，需要补发认证歌手的装扮,查询出所有的认证歌手
        List<SingerInfo> newSingerList = singerInfoDao.getSingerInfoByUserIds(appId, singerIds, Lists.newArrayList(SingerStatusEnum.EFFECTIVE), SingerTypeEnum.NEW, true);
        if (CollectionUtils.isEmpty(newSingerList)) {
            log.info("pp.reissueDecorateAfterEliminate is empty, appId:{}, singerIds:{}", appId, singerIds);
            return;
        }
        singerPushManager.sendSingerPassKafkaMessage(appId, newSingerList, operator,
                SingerDecorateOperateReasonConstant.SINGER_TYPE_CHANGE, guidGenerator.genId());
        log.info("pp.reissueDecorateAfterEliminate, singerIdsSize:{}, newSingerListSize:{}", singerIds.size(), newSingerList.size());
    }

    @Override
    public boolean isNeedRemoveSingerAfterRemoveWhiteList(int appId, Long njId) {
        return false;
    }


    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }
}
