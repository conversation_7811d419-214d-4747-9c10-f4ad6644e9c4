package fm.lizhi.ocean.wave.management.model.param.permission;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/20 15:18
 */
@Data
public class SavePlatformRoleParam {

    /**
     * ID 编辑的时候必传
     */
    private Integer id;

    @NotBlank(message = "名称为空")
    private String roleName;

    /**
     * 关联权限的权限code
     */
    private List<String> permissions;

    /**
     * 角色策略
     * 只有新增的时候才需要传
     */
    private JSONObject statementDsl;

}
