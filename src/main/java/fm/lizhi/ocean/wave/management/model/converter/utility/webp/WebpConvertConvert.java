package fm.lizhi.ocean.wave.management.model.converter.utility.webp;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.management.constants.utility.webp.WebpConvertQualityParamEnum;
import fm.lizhi.ocean.wave.management.manager.common.CommonCdnManager;
import fm.lizhi.ocean.wave.management.model.param.utility.webp.WebpAsyncConvertParam;
import fm.lizhi.ocean.wave.management.model.param.utility.webp.WebpConvertParam;
import fm.lizhi.ocean.wave.management.model.param.utility.webp.WebpNodeConvertParam;
import fm.lizhi.ocean.wave.management.model.param.utility.webp.WebpSyncConvertParam;
import fm.lizhi.ocean.wave.management.model.result.utility.webp.CdnUploadResult;
import fm.lizhi.ocean.wave.management.model.result.utility.webp.WebpAsyncConvertResult;
import fm.lizhi.ocean.wave.management.model.result.utility.webp.WebpConvertResult;
import fm.lizhi.ocean.wave.management.model.vo.utility.webp.WebpSyncConvertVO;
import fm.lizhi.ocean.wave.platform.api.common.service.CommonService;
import fm.lizhi.ocean.wave.platform.api.platform.webp.constant.WebpConvertRequestTypeEnum;
import fm.lizhi.ocean.wave.platform.api.platform.webp.constant.WebpConvertStatusEnum;
import fm.lizhi.ocean.wave.platform.api.platform.webp.request.RequestSaveBizRecord;
import fm.lizhi.ocean.wave.platform.api.platform.webp.response.ResponseGetBizRecord;
import fm.lizhi.ocean.wave.platform.api.platform.webp.response.ResponseGetConvertCache;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.UUID;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
                CommonService.class,
                ConfigUtils.class,
                WebpConvertStatusEnum.class,
                WebpConvertRequestTypeEnum.class,
                UUID.class,
        }
)
public abstract class WebpConvertConvert {

    @Autowired
    protected CommonCdnManager commonCdnManager;

    @Mapping(target = "requestType", expression = "java(WebpConvertRequestTypeEnum.KAFKA.getValue())")
    public abstract WebpConvertParam toKafkaRequestConvertParam(WebpAsyncConvertParam asyncConvertParam);

    @Mapping(target = "bizRequestId", expression = "java(UUID.randomUUID().toString().replace(\"-\", \"\"))")
    @Mapping(target = "requestType", expression = "java(WebpConvertRequestTypeEnum.HTTP.getValue())")
    public abstract WebpConvertParam toHttpRequestConvertParam(WebpSyncConvertParam syncConvertParam);

    @Mapping(target = "sourceFormat", source = "sourceType")
    @Mapping(target = "sourceUrl", source = "param", qualifiedByName = "toSourceInnerUrl")
    @Mapping(target = "businessEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "compressLevel", source = "qualityOption", qualifiedByName = "toCompressLevel")
    @Mapping(target = "compressMixed", source = "qualityOption", qualifiedByName = "toCompressMixed")
    public abstract WebpNodeConvertParam toNodeConvertParam(WebpConvertParam param);

    @Named("toSourceInnerUrl")
    protected String toSourceInnerUrl(WebpConvertParam param) {
        return commonCdnManager.addInnerHostOrEmpty(param.getSourcePath(), param.getAppId());
    }

    @Named("toCompressLevel")
    protected Integer toCompressLevel(Integer qualityOption) {
        WebpConvertQualityParamEnum paramEnum = WebpConvertQualityParamEnum.fromValue(qualityOption);
        return paramEnum != null ? paramEnum.getCompressLevel() : null;
    }

    @Named("toCompressMixed")
    protected Boolean toCompressMixed(Integer qualityOption) {
        WebpConvertQualityParamEnum paramEnum = WebpConvertQualityParamEnum.fromValue(qualityOption);
        return paramEnum != null ? paramEnum.getCompressMixed() : null;
    }

    @Mapping(target = "sourceSha", constant = "")
    @Mapping(target = "sourceSize", constant = "0L")
    @Mapping(target = "webpSha", constant = "")
    @Mapping(target = "webpSize", constant = "0L")
    @Mapping(target = "webpPath", constant = "")
    @Mapping(target = "status", expression = "java(WebpConvertStatusEnum.FAILURE.getValue())")
    public abstract WebpConvertResult toTimeoutResult(WebpConvertParam param, Integer errorCode, String errorText, Long costSeconds);

    @Mapping(target = "sourceSha", constant = "")
    @Mapping(target = "sourceSize", constant = "0L")
    @Mapping(target = "webpSha", constant = "")
    @Mapping(target = "webpSize", constant = "0L")
    @Mapping(target = "webpPath", constant = "")
    @Mapping(target = "status", expression = "java(WebpConvertStatusEnum.FAILURE.getValue())")
    public abstract WebpConvertResult toErrorResult(WebpConvertParam param, Integer errorCode, String errorText, Long costSeconds);

    @Mapping(target = "appId", source = "param.appId")
    @Mapping(target = "bizRequestId", source = "param.bizRequestId")
    @Mapping(target = "bizType", source = "param.bizType")
    @Mapping(target = "requestType", source = "param.requestType")
    @Mapping(target = "sourceType", source = "param.sourceType")
    @Mapping(target = "sourcePath", source = "param.sourcePath")
    @Mapping(target = "qualityOption", source = "param.qualityOption")
    public abstract WebpConvertResult toExistingResult(WebpConvertParam param, ResponseGetBizRecord bizRecord);

    @Mapping(target = "appId", source = "param.appId")
    @Mapping(target = "status", expression = "java(WebpConvertStatusEnum.SUCCESS.getValue())")
    @Mapping(target = "errorCode", constant = "0")
    @Mapping(target = "errorText", constant = "")
    public abstract WebpConvertResult toCachedResult(WebpConvertParam param, String sourceSha, Long sourceSize, ResponseGetConvertCache convertCache);

    @Mapping(target = "webpSha", source = "cdnUploadResult.fileSha")
    @Mapping(target = "webpSize", source = "cdnUploadResult.fileSize")
    @Mapping(target = "webpPath", source = "cdnUploadResult.filePath")
    @Mapping(target = "status", expression = "java(WebpConvertStatusEnum.SUCCESS.getValue())")
    @Mapping(target = "errorCode", constant = "0")
    @Mapping(target = "errorText", constant = "")
    public abstract WebpConvertResult toSuccessResult(WebpConvertParam param, String sourceSha, Long sourceSize, CdnUploadResult cdnUploadResult, Long costSeconds);

    public abstract RequestSaveBizRecord toRequestSaveBizRecord(WebpConvertResult convertResult);

    @Mapping(target = "code", source = "errorCode", defaultValue = "0")
    @Mapping(target = "msg", source = "errorText", defaultValue = "")
    public abstract WebpAsyncConvertResult toWebpAsyncConvertResult(WebpConvertResult convertResult);

    @Mapping(target = "code", source = "errorCode", defaultValue = "0")
    @Mapping(target = "msg", source = "errorText", defaultValue = "")
    @Mapping(target = "webpUrl", source = "convertResult", qualifiedByName = "toWebpUrl")
    public abstract WebpSyncConvertVO toWebpSyncConvertVO(WebpConvertResult convertResult);

    @Named("toWebpUrl")
    protected String toWebpUrl(WebpConvertResult convertResult) {
        return commonCdnManager.addHostOrEmpty(convertResult.getWebpPath(), convertResult.getAppId());
    }
}
