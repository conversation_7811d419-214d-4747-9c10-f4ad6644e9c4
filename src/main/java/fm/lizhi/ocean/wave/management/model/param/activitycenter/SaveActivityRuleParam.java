package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SaveActivityRuleParam {


    /**
     * 规则类型，1：提报次数，2：官频位轮播厅数
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRuleEnum
     */
    private Integer ruleType;

    /**
     * 周期. 单厅每周：HALL_WEEK
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRulePeriodEnum
     */
    private String period;

    /**
     * 次数
     */
    private Integer count;

    /**
     * 收入限制
     */
    private Long income;

    /**
     * 用户ID列表
     */
    private String userIds;


}
