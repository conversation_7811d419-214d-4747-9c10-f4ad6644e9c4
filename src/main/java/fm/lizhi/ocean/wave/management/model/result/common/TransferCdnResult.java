package fm.lizhi.ocean.wave.management.model.result.common;

import lombok.Data;

/**
 * 文件转存CDN结果
 */
@Data
public class TransferCdnResult {

    /**
     * 转存后的文件相对路径, 以斜杆开头
     */
    private String path;

    /**
     * 转存后的文件URL, 用于用户端访问
     */
    private String url;

    /**
     * 转存后的文件内网URL, 用于容器内访问
     */
    private String innerUrl;

    /**
     * 转存后的文件外网URL, 用于第三方访问
     */
    private String outerUrl;
}
