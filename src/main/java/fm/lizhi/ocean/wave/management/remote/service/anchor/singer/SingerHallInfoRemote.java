package fm.lizhi.ocean.wave.management.remote.service.anchor.singer;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerHallInfoConvert;
import fm.lizhi.ocean.wave.management.model.param.anchor.singer.SingerHallInfoParam;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestGetAllSingerStatics;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerRoomDetails;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetAllSingerStatics;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerRoomDetails;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerInfoAdminService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class SingerHallInfoRemote {

    @Autowired
    private SingerInfoAdminService singerInfoAdminService;

    public Result<ResponseGetAllSingerStatics> getSingerHallSummaryInfo(Long njId, Integer appId) {
        RequestGetAllSingerStatics request = new RequestGetAllSingerStatics().setNjId(njId).setAppId(appId);
        return singerInfoAdminService.getAllSingerStatics(request);
    }

    public Result<PageBean<ResponseSingerRoomDetails>> getSingerHallInfo(SingerHallInfoParam param, Integer appId) {
        RequestSingerRoomDetails request = SingerHallInfoConvert.INSTANCE.param2Request(param, appId);
        return singerInfoAdminService.singerRoomDetails(request);
    }
}
