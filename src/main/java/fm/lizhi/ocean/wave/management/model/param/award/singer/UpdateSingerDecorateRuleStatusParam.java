package fm.lizhi.ocean.wave.management.model.param.award.singer;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 修改歌手装饰规则状态
 */
@Data
@Accessors(chain = true)
public class UpdateSingerDecorateRuleStatusParam {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 是否启用
     */
    @NotNull(message = "启用参数不能为空")
    private Boolean enabled;

}
