package fm.lizhi.ocean.wave.management.controller.permission;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.permission.PermissionConvert;
import fm.lizhi.ocean.wave.management.model.param.permission.GetPermissionListParam;
import fm.lizhi.ocean.wave.management.model.param.permission.SavePermissionParam;
import fm.lizhi.ocean.wave.management.model.param.permission.SavePlatformRoleParam;
import fm.lizhi.ocean.wave.management.model.result.permission.PermissionListResult;
import fm.lizhi.ocean.wave.management.model.result.permission.PlatformRoleListResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.permission.PermissionVO;
import fm.lizhi.ocean.wave.management.remote.service.permission.PermissionServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wave.platform.api.common.bean.PageResult;
import fm.lizhi.ocean.wave.platform.api.permission.bean.PermissionBean;
import fm.lizhi.ocean.wave.platform.api.permission.bean.PermissionListBean;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestGetPermissionList;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestSavePermission;
import fm.lizhi.ocean.wave.platform.api.permission.service.PermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/20 14:53
 */
@RestController
@RequestMapping("/permission/permission")
public class PermissionController {

    @Autowired
    private PermissionServiceRemote permissionServiceRemote;

    /**
     * 权限列表
     * 提供给策略选择资源使用
     * @return
     */
    @GetMapping("permissionList")
    public ResultVO<List<PermissionVO>> permissionList(){
        Result<List<PermissionBean>> result = permissionServiceRemote.getPermissionListForResource();
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure("服务异常");
        }
        List<PermissionVO> permissionVOS = PermissionConvert.I.permissionBeans2VOs(result.target());
        return ResultVO.success(permissionVOS);
    }

    /**
     * 权限列表
     * @return
     */
    @GetMapping("list")
    public ResultVO<PageVO<PermissionListResult>> list(GetPermissionListParam param){
        Result<PageResult<PermissionListBean>> result = permissionServiceRemote.getPermissionList(new RequestGetPermissionList()
                .setAppId(SessionExtUtils.getAppId())
                .setPageNo(param.getPageNo())
                .setPageSize(param.getPageSize())
                .setPermissionCode(param.getPermissionCode())
        );
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure("服务异常");
        }

        List<PermissionListResult> permissionListResults = PermissionConvert.I.permissionListBeans2PermissionListResults(result.target().getList());
        return ResultVO.success(PageVO.of(result.target().getTotal(), permissionListResults));
    }

    /**
     * 保存权限
     * @return
     */
    @PostMapping("save")
    public ResultVO<Void> save(@RequestBody @Validated SavePermissionParam param){
        Result<Void> result = permissionServiceRemote.savePermission(new RequestSavePermission()
                .setAppId(SessionExtUtils.getAppId())
                .setId(param.getId())
                .setPermissionCode(param.getPermissionCode())
                .setPermissionName(param.getPermissionName())
                .setPermissionType(param.getPermissionType())
                .setRoles(param.getRoles())
        );
        if (result.rCode() == PermissionService.SAVE_PERMISSION_CODE_EXIST) {
            return ResultVO.failure("权限编码已存在");
        }
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure("服务异常");
        }
        return ResultVO.success();
    }

}
