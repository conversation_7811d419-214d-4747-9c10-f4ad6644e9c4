package fm.lizhi.ocean.wave.management.controller.award.singer;

import fm.lizhi.ocean.wave.management.manager.singer.SingerDecorateRuleManager;
import fm.lizhi.ocean.wave.management.manager.file.FileExportManager;
import fm.lizhi.ocean.wave.management.model.converter.award.singer.SingerDecorateRuleConvert;
import fm.lizhi.ocean.wave.management.model.param.award.singer.*;
import fm.lizhi.ocean.wave.management.model.result.award.singer.SingerDecorateFlowResult;
import fm.lizhi.ocean.wave.management.model.result.award.singer.SingerDecorateRuleResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerDecorateFlowExcelVO;
import fm.lizhi.ocean.wave.management.model.vo.file.FileExportVO;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.constant.SongStyleRangeType;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateConditionTypeEnum;
import fm.lizhi.sso.client.SessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@RestController
@RequestMapping("/singerDecorate")
public class SingerDecorateController {

    @Autowired
    private SingerDecorateRuleManager singerDecorateRuleManager;

    @Autowired
    private FileExportManager fileExportManager;

    /**
     * 分页获取歌手装扮规则配置列表
     *
     * @param param 分页参数
     * @return 分页结果
     */
    @GetMapping("/rule/list")
    public ResultVO<PageVO<SingerDecorateRuleResult>> pageSingerDecorateRule(@Valid PageSingerDecorateRuleParam param) {
        if (param == null) {
            return ResultVO.failure("参数不能为空");
        }
        Integer appId = SessionExtUtils.getAppId();
        return singerDecorateRuleManager.pageSingerDecorateRule(param, appId);
    }

    /**
     * 保存歌手装扮规则配置
     *
     * @param param 保存参数
     * @return 保存结果
     */
    @PostMapping("/rule/save")
    public ResultVO<Void> saveSingerDecorateRule(@Valid @RequestBody SaveSingerDecorateRuleParam param) {
        if (param == null) {
            return ResultVO.failure("参数不能为空");
        }
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        Set<Integer> collect = param.getConditionList().stream()
                .map(SaveSingerDecorateRuleParam.SingerDecorateConditionParam::getConditionType)
                .collect(Collectors.toSet());
        if (collect.size() != param.getConditionList().size()) {
            return ResultVO.failure("条件类型重复");
        }
        if(collect.size() != SingerDecorateConditionTypeEnum.values().length) {
            return ResultVO.failure("缺少条件类型");
        }
        return singerDecorateRuleManager.insertSingerDecorateRule(param, appId, operator);
    }

    /**
     * 更新歌手装扮规则配置
     *
     * @param param 更新参数
     * @return 更新结果
     */
    @PostMapping("/rule/update")
    public ResultVO<Void> updateSingerDecorateRule(@Valid @RequestBody UpdateSingerDecorateRuleParam param) {
        if (param == null) {
            return ResultVO.failure("参数不能为空");
        }
        Set<Integer> collect = param.getConditionList().stream()
                .map(UpdateSingerDecorateRuleParam.SingerDecorateConditionParam::getConditionType)
                .collect(Collectors.toSet());
        if (collect.size() != param.getConditionList().size()) {
            return ResultVO.failure("条件类型重复");
        }
        if(collect.size() != SingerDecorateConditionTypeEnum.values().length) {
            return ResultVO.failure("缺少条件类型");
        }
        Integer appId = SessionExtUtils.getAppId();
        String operator = SessionUtils.getAccount();
        return singerDecorateRuleManager.updateSingerDecorateRule(param, appId, operator);
    }

    /**
     * 更新歌手装扮规则配置
     *
     * @param param 更新参数
     * @return 更新结果
     */
    @PostMapping("/rule/updateEnable")
    public ResultVO<Void> updateStatus(@Valid @RequestBody UpdateSingerDecorateRuleStatusParam param) {
        if (param == null) {
            return ResultVO.failure("参数不能为空");
        }
        String operator = SessionUtils.getAccount();
        return singerDecorateRuleManager.updateEnable(param, operator);
    }


    /**
     * 删除歌手装扮规则配置
     *
     * @param param 更新参数
     * @return 更新结果
     */
    @PostMapping("/rule/delete")
    public ResultVO<Void> delete(@Valid @RequestBody DeleteSingerDecorateRuleParam param) {
        if (param == null) {
            return ResultVO.failure("参数不能为空");
        }
        String operator = SessionUtils.getAccount();
        return singerDecorateRuleManager.delete(param.getId(), operator);
    }


    /**
     * 分页查询歌手装扮流水
     *
     * @param param 分页参数
     * @return 分页结果
     */
    @GetMapping("/flow/list")
    public ResultVO<PageVO<SingerDecorateFlowResult>> pageSingerDecorateFlow(@Valid PageSingerDecorateFlowParam param) {
        if (param == null) {
            return ResultVO.failure("参数不能为空");
        }
        Integer appId = SessionExtUtils.getAppId();
        return singerDecorateRuleManager.pageSingerDecorateFlow(param, appId);
    }
    
    @GetMapping("/flow/export")
    public void exportSingerDecorateFlow(@Valid PageSingerDecorateFlowExcelParam param, HttpServletResponse response) {
        if (param == null) {
            throw new RuntimeException("参数不能为空");
        }
        
        Integer appId = SessionExtUtils.getAppId();
        FileExportVO<SingerDecorateFlowExcelVO> fileExportVO = new FileExportVO<>();
        fileExportVO.setFileName("歌手装扮流水");
        fileExportVO.setHead(SingerDecorateFlowExcelVO.class);
        fileExportVO.setQueryAll(param.isQueryAll());
        fileExportVO.setPageNo(param.isQueryAll() ? 1 : param.getPageNo());
        fileExportVO.setPageSize(param.getPageSize());

        try {
            fileExportManager.exportToHttpResponse(fileExportVO, response, (pageNo, pageSize) -> {
                param.setPageNo(pageNo);
                ResultVO<PageVO<SingerDecorateFlowResult>> result = singerDecorateRuleManager.pageSingerDecorateFlow((PageSingerDecorateFlowParam)param, appId);
                if (!result.isSuccess()) {
                    return PageVO.of(0, new ArrayList<>());
                }
                PageVO<SingerDecorateFlowResult> pageVO = result.getData();
                List<SingerDecorateFlowResult> list = pageVO.getList();
                List<SingerDecorateFlowExcelVO> excelVOList = SingerDecorateRuleConvert.INSTANCE.convertSingerDecorateFlowExcelVOList(list);
                return PageVO.of(pageVO.getTotal(), excelVOList);
                
            });
        } catch (IOException e) {
            throw new RuntimeException("导出失败", e);
        }
    }


}