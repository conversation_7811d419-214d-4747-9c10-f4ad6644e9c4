package fm.lizhi.ocean.wave.management.model.converter.grow;

import fm.lizhi.ocean.wave.management.model.vo.family.FamilyLevelConfigVO;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigAwardBean;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/19 11:35
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface FamilyLevelConfigConvert {

    FamilyLevelConfigConvert I = Mappers.getMapper(FamilyLevelConfigConvert.class);

    @Mappings({
            @Mapping(source = "minExp", target = "minIncome"),
    })
    FamilyLevelConfigVO awardBean2ConfigVO(FamilyLevelConfigAwardBean bean);

    List<FamilyLevelConfigVO> awardBeans2ConfigVOs(List<FamilyLevelConfigAwardBean> beans);

}
