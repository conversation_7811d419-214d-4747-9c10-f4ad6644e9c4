package fm.lizhi.ocean.wave.management.datastore.dao.singer;

import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.ocean.wave.management.util.RedisLock;
import fm.lizhi.ocean.wavecenter.base.constants.TimeConstant;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.rediskey.SingerRedisKey;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class SingerRedisDao {

    @Resource(name = "redis_ocean_wavecenter")
    private RedisClient redisClient;

    /**
     * 获取歌手信息更新锁
     *
     * @param appId 应用ID
     * @param njId  主播ID
     * @return 锁
     */
    public RedisLock tryGetSingerUpdateLock(int appId, long njId) {
        //获取key
        String key = SingerRedisKey.SINGER_INFO_UPDATE_LOCK.getKey(appId, njId);
        int timeout = TimeConstant.ONE_MINUTE * 1000;
        return new RedisLock(redisClient, key, timeout / 3, timeout);
    }

}
