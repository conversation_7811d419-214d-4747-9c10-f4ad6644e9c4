package fm.lizhi.ocean.wave.management.manager.singer;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerDecorateDao;
import fm.lizhi.ocean.wave.management.model.converter.award.singer.SingerDecorateRuleConvert;
import fm.lizhi.ocean.wave.management.model.dto.singer.SingerDecorateConditionDTO;
import fm.lizhi.ocean.wave.management.model.param.award.singer.*;
import fm.lizhi.ocean.wave.management.model.result.award.singer.SingerDecorateFlowResult;
import fm.lizhi.ocean.wave.management.model.result.award.singer.SingerDecorateRuleResult;
import fm.lizhi.ocean.wave.management.model.vo.PageVO;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.remote.service.anchor.singer.SingerConfigServiceRemote;
import fm.lizhi.ocean.wave.management.remote.service.award.singer.SingerDecorateRemote;
import fm.lizhi.ocean.wave.management.remote.service.award.singer.SingerDecorateRuleRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SongStyleBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerEnumerateConfig;
import fm.lizhi.ocean.wavecenter.api.award.singer.response.ResponseSingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateCondition;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateRule;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.constant.SongStyleRangeType;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.bean.SingerDecorateConditionIndexBean;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateConditionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateConditionTypeEnum.MUSIC_STYLE;
import static fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateConditionTypeEnum.ORIGINAL_SINGER;

@Slf4j
@Component
public class SingerDecorateRuleManager {

    @Autowired
    private SingerDecorateRuleRemote singerDecorateRuleRemote;
    @Autowired
    private SingerDecorateDao singerDecorateDao;
    @Autowired
    private SingerDecorateRemote singerDecorateRemote;
    @Autowired
    private SingerConfigServiceRemote singerConfigServiceRemote;

    /**
     * 分页获取歌手装扮规则配置列表
     *
     * @param param 分页参数
     * @param appId 应用ID
     * @return 分页结果
     */
    public ResultVO<PageVO<SingerDecorateRuleResult>> pageSingerDecorateRule(PageSingerDecorateRuleParam param, Integer appId) {
        PageList<SingerDecorateRule> pageDto = singerDecorateDao.pageSingerDecorateRule(appId, param.getPageNo(), param.getPageSize());
        if (CollectionUtils.isEmpty(pageDto)){
            return ResultVO.success(new PageVO<>());
        }
        //组装装扮和条件信息到规则中
        PageBean<SingerDecorateRuleResult> pageBean = PageBean.of(pageDto.getTotal(), pageDto.stream().map(bean -> {
            DecorateInfoBean decorateInfo = singerDecorateRemote.getDecorateInfo(appId, bean.getDecorateId(), PlatformDecorateTypeEnum.getByType(bean.getDecorateType()));
            List<SingerDecorateCondition> conditions = singerDecorateDao.getSingerDecorateCondition(bean.getId());
            return SingerDecorateRuleConvert.INSTANCE.buildResponseSingerDecorateRule(bean, decorateInfo, conditions);
        }).collect(Collectors.toList()));

        return ResultVO.success(PageVO.of(pageBean.getTotal(), pageBean.getList()));
    }

    /**
     * 保存歌手装扮规则配置
     *
     * @param param    保存参数
     * @param appId    应用ID
     * @param operator 操作人
     * @return 保存结果
     */
    public ResultVO<Void> insertSingerDecorateRule(SaveSingerDecorateRuleParam param, Integer appId, String operator) {
        try {
            List<SingerDecorateConditionDTO> conditionDtos = SingerDecorateRuleConvert.INSTANCE.toSingerDecorateConditionDTOs(param.getConditionList());
            //构建快速索引
            ResultVO<String> combineConditionIndex = combineConditionIndex(appId, operator, conditionDtos);
            if(combineConditionIndex.isFailure()) {
                return ResultVO.failure(combineConditionIndex.getMsg());
            }
            log.info("insertSingerDecorateRule combineConditionIndex is success. appId:{}, operator:{}, combineConditionIndex:{}", appId, operator, combineConditionIndex.getData());
            SingerDecorateRule rule = SingerDecorateRuleConvert.INSTANCE.toInsertSingerDecorateRule(param, appId, operator, combineConditionIndex.getData());
            List<SingerDecorateCondition> conditions = SingerDecorateRuleConvert.INSTANCE.toSingerDecorateConditions(param.getConditionList(), appId);
            singerDecorateDao.insertSingerDecorateRule(rule, conditions);
            return ResultVO.success();
        }catch (Exception e) {
            log.warn("saveSingerDecorateRule is fail. appId:{}, operator:{};param={}", appId, operator, param, e);
            return ResultVO.failure("新增装扮规则失败");
        }
    }


    private ResultVO<Void> checkRule(int singerType, int appId, Long decorateId) {
        List<SingerDecorateRule> existDecorateId;
        if(SingerTypeEnum.NEW.getType() == singerType) {
            existDecorateId = singerDecorateDao.findByDecorateId(decorateId, SingerTypeEnum.QUALITY.getType(), appId);
        } else {
            existDecorateId = singerDecorateDao.findByDecorateId(decorateId, SingerTypeEnum.NEW.getType(), appId);
        }
        if(CollectionUtils.isNotEmpty(existDecorateId)) {
            return ResultVO.failure("该装扮已存在配置");
        }
        return ResultVO.success();
    }


    /**
     * 条件内容 -> 规则合并条件索引值
     eg：3个曲风,非原创 -> 1_1_曲风1_曲风2_曲风3,2_false
         任一曲风,原创 -> 1_2,2_true
         全能,原创 -> 1_3,2_true
         仅原创 -> 1_1,2_true
     * @param conditions
     * @return
     */
    private ResultVO<String> combineConditionIndex(int appId, String operator, List<SingerDecorateConditionDTO> conditions) {
        Result<ResponseSingerEnumerateConfig> result = singerConfigServiceRemote.getEnumerateConfig(appId);
        if (result.rCode() != 0) {
            log.warn("saveSingerDecorateRule combineConditionIndex fail; appId:{}, operator:{}", appId, operator);
            return ResultVO.failure("曲风获取失败,新增装扮规则失败");
        }
        List<SongStyleBean> songStyles = result.target().getSongStyle();
        if (CollectionUtils.isEmpty(songStyles)) {
            log.warn("saveSingerDecorateRule combineConditionIndex fail; appId:{}, operator:{}", appId, operator);
            return ResultVO.failure("曲风获取失败,新增装扮规则失败");
        }
        boolean legalCondition = false;

        //构造每个条件
        List<SingerDecorateConditionIndexBean> conditionIndexBeans = Lists.newArrayList();
        for (SingerDecorateConditionDTO condition : conditions) {
            SingerDecorateConditionIndexBean conditionIndex = new SingerDecorateConditionIndexBean();
            SingerDecorateConditionTypeEnum conditionType = SingerDecorateConditionTypeEnum.getByCode(condition.getConditionType());
            conditionIndex.setConditionType(conditionType);

            Integer songStyleType = condition.getSongStyleType();
            List<String> songStyleList = condition.getSongStyleList();
            switch (conditionType) {
                case MUSIC_STYLE:
                    SongStyleRangeType styleRangeType = SongStyleRangeType.get(songStyleType);
                    if(styleRangeType != SongStyleRangeType.NONE) {
                        legalCondition = true;
                    }
                    if(styleRangeType == SongStyleRangeType.FIXED && CollectionUtils.isEmpty(songStyleList)) {
                        return ResultVO.failure("曲风不能为空");
                    }
                    // 根据配置的顺序，将提交的曲风进行排序
                    List<String> sortedSongStyles = songStyles.stream()
                            .filter(songStyle -> songStyleList.contains(songStyle.getName()))
                            .sorted(Comparator.comparingInt(SongStyleBean::getConditionIndex))
                            .map(SongStyleBean::getName)
                            .collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(songStyleList) && sortedSongStyles.isEmpty()) {
                        return ResultVO.failure("不支持的曲风配置");
                    }
                    conditionIndex.setValue(MUSIC_STYLE.buildConditionIndex(styleRangeType, sortedSongStyles));
                    break;
                case ORIGINAL_SINGER:
                    conditionIndex.setValue(ORIGINAL_SINGER.buildConditionIndex(condition.isOriginalSinger()));
                    if(condition.isOriginalSinger()) {
                        legalCondition = true;
                    }
                    break;
                default:
                    return ResultVO.failure("合并装扮规则条件索引失败：存在未知的条件类型;type=" + condition.getConditionType());
            }
            conditionIndexBeans.add(conditionIndex);
        }
        if(!legalCondition) {
            return ResultVO.failure("条件不能为空");
        }
        //合并条件
        return ResultVO.success(SingerDecorateConditionTypeEnum.combineMultipleConditions(conditionIndexBeans));
    }

    /**
     * 更新歌手装扮规则配置
     *
     * @param param    更新参数
     * @param appId    应用ID
     * @param operator 操作人
     * @return 更新结果
     */
    public ResultVO<Void> updateSingerDecorateRule(UpdateSingerDecorateRuleParam param, Integer appId, String operator) {
        try {
            List<SingerDecorateConditionDTO> conditionDtos = SingerDecorateRuleConvert.INSTANCE.toUpdateSingerDecorateConditionDTOs(param.getConditionList());
            //构建快速索引
            ResultVO<String> combineConditionIndex = combineConditionIndex(appId, operator, conditionDtos);
            if(combineConditionIndex.isFailure()) {
                return ResultVO.failure(combineConditionIndex.getMsg());
            }
            log.info("updateSingerDecorateRule combineConditionIndex is success. appId:{}, operator:{}, combineConditionIndex:{}", appId, operator, combineConditionIndex.getData());
            SingerDecorateRule rule = SingerDecorateRuleConvert.INSTANCE.toUpdateSingerDecorateRule(param, operator, combineConditionIndex.getData());
            List<SingerDecorateCondition> conditions = SingerDecorateRuleConvert.INSTANCE.toUpdateSingerDecorateConditions(param.getConditionList());
            singerDecorateDao.updateSingerDecorateRule(rule, conditions);
            return ResultVO.success();
        }catch (Exception e) {
            log.warn("updateSingerDecorateRule is fail. appId:{}, operator:{}", appId, operator, e);
            return ResultVO.failure("更新装扮规则失败");
        }
    }

    /**
     * 分页查询歌手装扮流水
     *
     * @param param 分页参数
     * @param appId 应用ID
     * @return 分页结果
     */
    public ResultVO<PageVO<SingerDecorateFlowResult>> pageSingerDecorateFlow(PageSingerDecorateFlowParam param, Integer appId) {
        Result<PageBean<ResponseSingerDecorateFlow>> result = singerDecorateRuleRemote.pageSingerDecorateFlow(param, appId);
        if (ResultUtils.isFailure(result)) {
            log.warn("pageSingerDecorateFlow is fail. appId:{}", appId);
            return ResultVO.failure(result.rCode(), result.getMessage());
        }
        PageVO<SingerDecorateFlowResult> pageVO = SingerDecorateRuleConvert.INSTANCE.convertSingerDecorateFlowResultList(result.target());
        return ResultVO.success(pageVO);
    }


    public ResultVO<Void> updateEnable(UpdateSingerDecorateRuleStatusParam param, String operator) {
        if(singerDecorateDao.updateEnable(param.getId(), param.getEnabled(), operator)) {
            return ResultVO.success();
        }
        return ResultVO.failure("更新失败");
    }

    public ResultVO<Void> delete(Long id, String operator) {
        if(singerDecorateDao.updateDeleteStatus(id, operator)) {
            return ResultVO.success();
        }
        return ResultVO.failure("删除失败");
    }
}