package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Builder;
import lombok.Data;

/**
 * 活动推荐卡配置
 *
 * <AUTHOR>
 * @date 2024-10-10 06:34:31
 */

@Data
@Builder
public class ActivityRecommendCardConfigResult {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 等级ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long levelId;

    /**
     * 发放张数
     */
    private Integer count;

    /**
     * 有效天数
     */
    private Integer validDay;

    /**
     * 操作者
     */
    private String operator;

}