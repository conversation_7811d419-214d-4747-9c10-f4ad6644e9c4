package fm.lizhi.ocean.wave.management.model.vo.activitycenter;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

/**
 * @description: 用户提报活动简单信息
 * @author: guoyi<PERSON>
 * @create: 2024/10/23 17:24
 */
@Data
public class UserActivitySimpleInfoExportVO {

    /**
     * 活动名称
     */
    @ExcelProperty("活动名称")
    private String name;

    /**
     * 活动状态
     *
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityStatusEnum
     */
    @ExcelProperty("活动状态")
    private String activityStatusStr;

    @ExcelProperty("活动审核状态")
    private String auditStatusStr;

    /**
     * 提报时间
     */
    @ExcelProperty("提报时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 活动报名类型
     */
    @ExcelProperty("活动报名类型")
    private String applyTypeStr;

    /**
     * 审核原因
     */
    @ExcelProperty("审核原因")
    private String auditReason;

    /**
     * 活动开始时间
     */
    @ExcelProperty("直播开始时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ExcelProperty("直播结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 直播时长
     */
    @ExcelProperty("直播时长(分钟)")
    private Integer duration;

    /**
     * 大分类名称
     */
    @ExcelProperty("活动分类名称")
    private String bigClassName;

    /**
     * 活动分类名称
     */
    @ExcelProperty("子分类名称")
    private String className;

    /**
     * 活动等级
     */
    @ExcelProperty("活动等级")
    private String levelName;

    /**
     * 提报家族ID
     */
    @ExcelProperty("报名公会ID")
    private String applyFamilyId;

    /**
     * 提报家族名称
     */
    @ExcelProperty("报名公会名称")
    private String applyFamilyName;

    /**
     * 提报厅厅主ID
     */
    @ExcelProperty("提报厅主ID")
    private String applyHallHostUserId;

    /**
     * 提报厅厅主ID
     */
    @ExcelProperty("报名厅波段号")
    private String applyHallHostBand;

    /**
     * 提报厅厅主名称
     */
    @ExcelProperty("报名厅名称")
    private String applyHallHost;

    @ExcelProperty("提报人ID")
    private String applyUserId;

    /**
     * 提报人ID
     */
    @ExcelProperty("报名人波段号")
    private String applyUserBand;

    /**
     * 提报人名称
     */
    @ExcelProperty("报名人名称")
    private String applyUserName;

    /**
     * 审核操作人
     */
    @ExcelProperty("最后操作人")
    private String auditOperator;
}
