package fm.lizhi.ocean.wave.management.remote.service.permission;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.platform.api.common.bean.PageResult;
import fm.lizhi.ocean.wave.platform.api.permission.bean.StatementBean;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestGetStatementList;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestSaveStatement;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestUpdateStatementStatus;
import fm.lizhi.ocean.wave.platform.api.permission.service.StatementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2025/2/25 16:04
 */
@Component
public class StatementServiceRemote {

    @Autowired
    private StatementService statementService;

    /**
     * 查询策略列表
     * @param request
     * @return
     */
    public Result<PageResult<StatementBean>> getStatementList(RequestGetStatementList request) {
        return statementService.getStatementList(request);
    }

    /**
     * 保存策略
     * @param request
     * @return
     */
    public Result<Void> saveStatement(RequestSaveStatement request) {
        return statementService.saveStatement(request);
    }

    /**
     * 更新策略状态
     * @return
     */
    public Result<Void> updateStatementStatus(RequestUpdateStatementStatus request){
        return statementService.updateStatementStatus(request);
    }

}
