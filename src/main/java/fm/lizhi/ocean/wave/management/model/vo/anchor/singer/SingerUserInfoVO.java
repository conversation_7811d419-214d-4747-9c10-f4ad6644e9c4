package fm.lizhi.ocean.wave.management.model.vo.anchor.singer;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SingerUserInfoVO {

    /**
     * 用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户名
     */
    private String name;

    /**
     * 波段号
     */
    private String band;

    /**
     * 性别
     */
    private Integer gender;
}
