package fm.lizhi.ocean.wave.management.model.result.activitycenter;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wave.management.common.serializer.PercentBigDecimalSerializer;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * 活动报表-汇总表
 *
 * <AUTHOR>
 * @date 2024-10-16 04:43:36
 */
@Data
@Builder
public class ActivityReportDataSummaryResult {
    /**
     * ID
     */
    private String id;

    /**
     * 活动ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 提报厅厅主ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long njId;

    /**
     * 家族ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyId;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 进房人数
     */
    private Long enterRoomUserCnt;

    /**
     * 新人进房人数
     */
    private Long enterRoomNewUserCnt;

    /**
     * 房间逗留人数
     */
    private Long userFullOneMin;

    /**
     * 新人逗留人数
     */
    private Long newUserFullOneMin;

    /**
     * 送礼人数
     */
    private Long giftUserCnt;

    /**
     * 新人送礼人数
     */
    private Long giftNewUserCnt;

    /**
     * 房间新增粉丝数
     */
    private Long newFansUserCnt;

    /**
     * 送礼钻石数
     */
    private Long allIncome;

    /**
     * 送礼魅力值
     */
    private Long allCharm;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 进房停留率
     */
    @JsonSerialize(using = PercentBigDecimalSerializer.class)
    private BigDecimal enterRoomStayRate;

    /**
     * 评论人数
     */
    private Long commentUserCnt;

    /**
     * 停留发言率
     */
    @JsonSerialize(using = PercentBigDecimalSerializer.class)
    private BigDecimal staySpeakRate;

    /**
     * 停留付费率
     */
    @JsonSerialize(using = PercentBigDecimalSerializer.class)
    private BigDecimal stayPayRate;

    /**
     * 新用户进房停留率
     */
    @JsonSerialize(using = PercentBigDecimalSerializer.class)
    private BigDecimal newUserEnterRoomStayRate;

    /**
     * 新用户评论人数
     */
    private Long newUserCommentUserCnt;

    /**
     * 新用户停留发言率
     */
    @JsonSerialize(using = PercentBigDecimalSerializer.class)
    private BigDecimal newUserStaySpeakRate;

    /**
     * 新用户停留付费率
     */
    @JsonSerialize(using = PercentBigDecimalSerializer.class)
    private BigDecimal newUserStayPayRate;
}