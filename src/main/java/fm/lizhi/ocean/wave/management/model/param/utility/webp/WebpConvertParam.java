package fm.lizhi.ocean.wave.management.model.param.utility.webp;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import fm.lizhi.ocean.wave.management.common.validation.EnumValue;
import fm.lizhi.ocean.wave.management.constants.BusinessEvnEnum;
import fm.lizhi.ocean.wave.platform.api.platform.webp.constant.WebpConvertQualityOptionEnum;
import fm.lizhi.ocean.wave.platform.api.platform.webp.constant.WebpConvertRequestTypeEnum;
import fm.lizhi.ocean.wave.platform.api.platform.webp.constant.WebpConvertSourceTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * webp通用转换请求
 */
@Data
public class WebpConvertParam {

    /**
     * 应用id
     */
    @NotNull(message = "应用id不能为空")
    @EnumValue(value = BusinessEvnEnum.class, fieldName = "appId", message = "应用id不合法")
    private Integer appId;

    /**
     * 业务请求id, 由业务方生成并保证唯一
     */
    @NotNull(message = "业务请求id不能为空")
    @Size(min = 1, max = 100, message = "业务请求id长度必须在{min}到{max}之间")
    private String bizRequestId;

    /**
     * 请求类型
     */
    @NotNull(message = "请求类型不能为空")
    @EnumValue(value = WebpConvertRequestTypeEnum.class, message = "请求类型不合法")
    private String requestType;

    /**
     * 业务类型, 由业务方定义, 用于分类
     */
    @NotNull(message = "业务类型不能为空")
    private String bizType;

    /**
     * 源文件类型
     */
    @NotNull(message = "源文件类型不能为空")
    @EnumValue(value = WebpConvertSourceTypeEnum.class, message = "源文件类型不合法")
    private String sourceType;

    /**
     * 源文件路径, 斜杆开头的相对路径
     */
    @NotNull(message = "源文件路径不能为空")
    @Size(min = 1, max = 190, message = "源文件路径长度必须在{min}到{max}之间")
    private String sourcePath;

    /**
     * 转换质量选项
     */
    @NotNull(message = "转换质量选项不能为空")
    @EnumValue(value = WebpConvertQualityOptionEnum.class, message = "转换质量选项不合法")
    @JsonSetter(nulls = Nulls.SKIP)
    private Integer qualityOption;
}
