package fm.lizhi.ocean.wave.management.model.converter.activitycenter;

import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityLevelParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityLevelParam;
import fm.lizhi.ocean.wave.management.model.result.activitycenter.ActivityLevelConfigResult;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityLevel;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityLevel;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityLevelConfigConvert {

    ActivityLevelConfigConvert I = Mappers.getMapper(ActivityLevelConfigConvert.class);

    @Mappings({
            @Mapping(source = "appId", target = "appId"),
            @Mapping(source = "operator", target = "operator"),
            @Mapping(source = "param.levelName", target = "level"),
    })
    RequestSaveActivityLevel toRequestSaveActivityLevel(SaveActivityLevelParam param, Integer appId, String operator);

    @Mappings({
            @Mapping(source = "appId", target = "appId"),
            @Mapping(source = "operator", target = "operator"),
            @Mapping(source = "param.levelName", target = "level"),
            @Mapping(source = "param.id", target = "id"),
    })
    RequestUpdateActivityLevel toRequestUpdateActivityLevel(UpdateActivityLevelParam param, Integer appId, String operator);

    List<ActivityLevelConfigResult> toActivityLevelConfigBeanList(List<fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean> target);

    ActivityLevelConfigResult toActivityLevelConfigResult(ActivityLevelConfigBean target);

}
