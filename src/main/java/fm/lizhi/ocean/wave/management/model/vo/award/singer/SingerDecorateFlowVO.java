package fm.lizhi.ocean.wave.management.model.vo.award.singer;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 歌手装扮流水
 *
 * <AUTHOR>
 * @date 2025-03-27 03:27:23
 */
@Data
@Accessors(chain = true)
public class SingerDecorateFlowVO {
    /**
     * ID
     */
    @JsonSerialize (using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户ID
     */
    @JsonSerialize (using = ToStringSerializer.class)
    private Long userId;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 批次ID
     */
    @JsonSerialize (using = ToStringSerializer.class)
    private Long transactionId;

    /**
     * 装扮类型
     */
    private Integer decorateType;

    /**
     * 装扮ID
     */
    @JsonSerialize (using = ToStringSerializer.class)
    private Long decorateId;


    /**
     * 发放时长，单位秒
     */
    private Long duration;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 发放的规则 ID
     */
    @JsonSerialize (using = ToStringSerializer.class)
    private Long ruleId;

    /**
     * 状态 1: 未处理 2: 成功 3: 失败
     */
    private Integer status;

    /**
     * 操作类型 1: 发放 2: 回收
     */
    private Integer operateType;

    /**
     * 歌手认证等级
     */
    private Integer singerType;


    /**
     * 是否被回收，只有operate_type=发放时生效, 0 未回收,1回收
     */
    private Boolean recycled;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private Long operateTime;

    /**
     * 操作原因
     */
    private String reason;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long modifyTime;
}