package fm.lizhi.ocean.wave.management.common.condition;

import com.ctrip.framework.apollo.core.enums.Env;
import com.lizhi.commons.config.core.util.ConfigUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionMessage;
import org.springframework.boot.autoconfigure.condition.ConditionOutcome;
import org.springframework.boot.autoconfigure.condition.SpringBootCondition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.util.MultiValueMap;

import java.util.List;
import java.util.Objects;

/**
 * 根据部署环境判断是否匹配条件
 */
class OnDeployEnvCondition extends SpringBootCondition {

    private static final String ANNOTATION_ARTICLE = "annotation";
    private static final String VALUE_FIELD = "value";

    @Override
    public ConditionOutcome getMatchOutcome(ConditionContext context, AnnotatedTypeMetadata metadata) {
        MultiValueMap<String, Object> attributes = metadata.getAllAnnotationAttributes(
                ConditionalOnDeployEnv.class.getName());
        if (attributes == null) {
            return ConditionOutcome.noMatch(ConditionMessage.forCondition(ConditionalOnDeployEnv.class)
                    .didNotFind(ANNOTATION_ARTICLE).atAll());
        }
        Env currentEnv = ConfigUtils.getEnv();
        List<Object> allValues = attributes.get(VALUE_FIELD);
        if (CollectionUtils.isNotEmpty(allValues)) {
            for (Object values : allValues) {
                for (Env value : (Env[]) values) {
                    // 因为进程内Env只有一个, 所以采用的是any的逻辑
                    if (Objects.equals(value, currentEnv)) {
                        return ConditionOutcome.match(ConditionMessage.forCondition(ConditionalOnDeployEnv.class)
                                .found(VALUE_FIELD).items(values));
                    }
                }
            }
        }
        return ConditionOutcome.noMatch(ConditionMessage.forCondition(ConditionalOnDeployEnv.class)
                .didNotFind(VALUE_FIELD).items(currentEnv));
    }
}
