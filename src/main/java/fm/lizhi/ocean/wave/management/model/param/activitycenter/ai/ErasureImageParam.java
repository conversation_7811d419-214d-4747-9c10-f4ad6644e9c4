package fm.lizhi.ocean.wave.management.model.param.activitycenter.ai;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 部分擦除参数
 */
@Data
public class ErasureImageParam {

    /**
     * 会话id
     */
    @NotNull(message = "会话id不能为空")
    @Size(max = 64, message = "会话id长度不能超过{max}个字符")
    private String sessionId;

    /**
     * 原图URL
     */
    @NotNull(message = "原图URL不能为空")
    private String originalImageUrl;

    /**
     * 蒙版图URL, 前端需将蒙版图上传至创作者CDN获得URL
     */
    @NotNull(message = "蒙版图URL不能为空")
    private String maskImageUrl;
}
