package fm.lizhi.ocean.wave.management.manager.utility.webp;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.romefs.javasdk.config.RomeFsConfig;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.config.apollo.BusinessConfig;
import fm.lizhi.ocean.wave.management.config.apollo.BusinessProperties;
import fm.lizhi.ocean.wave.management.manager.common.RomeFsManager;
import fm.lizhi.ocean.wave.management.model.param.common.RomeFsPutParam;
import fm.lizhi.ocean.wave.management.model.result.common.RomeFsPutResult;
import fm.lizhi.ocean.wave.management.model.result.utility.webp.CdnDownloadResult;
import fm.lizhi.ocean.wave.management.model.result.utility.webp.CdnUploadResult;
import fm.lizhi.ocean.wave.management.util.PathUtils;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.UrlUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequest;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Base64;

/**
 * WebP CDN管理器
 */
@Slf4j
@Component
public class WebpCdnManager {

    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");

    @Autowired
    private BusinessConfig businessConfig;

    @Autowired
    @Qualifier("webpRestTemplate")
    private RestTemplate restTemplate;

    @Autowired
    private RomeFsManager romeFsManager;

    /**
     * 上传webp文件
     *
     * @param localPath 本地文件路径
     * @param appId     应用id
     * @return 上传结果
     */
    public CdnUploadResult uploadWebp(Path localPath, int appId) {
        try {
            String dateTime = LocalDateTime.now().format(dateTimeFormatter);
            String fileSha = PathUtils.sha256Hex(localPath);
            String fileName = String.format("/manager/%s/%s.webp", dateTime, fileSha);
            BusinessProperties toAppProperties = businessConfig.getPropertiesByAppId(appId);
            RomeFsConfig romeFsConfig = RomeFsConfig.builder()
                    .appId(appId)
                    .hostApp(StringUtils.EMPTY)
                    .deviceId(ConfigUtils.getServiceName())
                    .address(toAppProperties.getRomeInnerAddress())
                    .customFileName(true)
                    .build();
            RomeFsPutParam romeFsPutParam = new RomeFsPutParam();
            romeFsPutParam.setRomeFsConfig(romeFsConfig);
            romeFsPutParam.setAccessModifier(RomeFsPutParam.ACCESS_MODIFIER_PUBLIC);
            romeFsPutParam.setLocalPath(localPath);
            romeFsPutParam.setFileName(fileName);
            Result<RomeFsPutResult> putObjectResult = romeFsManager.putObject(romeFsPutParam);
            if (ResultUtils.isFailure(putObjectResult)) {
                return CdnUploadResult.failure(putObjectResult.getMessage());
            }
            String filePath = putObjectResult.target().getFilePath();
            long fileSize = PathUtils.size(localPath);
            return CdnUploadResult.success(filePath, fileSha, fileSize);
        } catch (RuntimeException e) {
            log.error("Upload webp file error, localPath={}, appId={}", localPath, appId, e);
            return CdnUploadResult.failure("上传文件失败");
        }
    }

    /**
     * 使用base64上传webp文件
     *
     * @param webpBase64 webp文件的base64编码
     * @param appId      应用id
     * @return 上传结果
     */
    public CdnUploadResult uploadWebpByBase64(String webpBase64, int appId) {
        Path localPath;
        try {
            localPath = Files.createTempFile(StringUtils.EMPTY, StringUtils.EMPTY);
            byte[] webpBytes = Base64.getDecoder().decode(webpBase64.getBytes(StandardCharsets.UTF_8));
            Files.write(localPath, webpBytes);
        } catch (IOException | RuntimeException e) {
            log.error("Failed to create temp file", e);
            return CdnUploadResult.failure("生成webp临时文件失败");
        }
        return uploadWebp(localPath, appId);
    }

    /**
     * 使用文件路径下载文件
     *
     * @param path  文件路径
     * @param appId 应用id
     * @return 下载结果
     */
    public CdnDownloadResult downloadByPath(String path, int appId) {
        String url;
        try {
            BusinessProperties businessProperties = businessConfig.getPropertiesByAppId(appId);
            url = UrlUtils.addHostOrEmpty(path, businessProperties.getCdnInnerHost());
        } catch (RuntimeException e) {
            log.info("Failed to get CDN url, path={}, appId={}", path, appId, e);
            return CdnDownloadResult.paramError();
        }
        Path localPath;
        try {
            localPath = Files.createTempFile(StringUtils.EMPTY, StringUtils.EMPTY);
        } catch (IOException | RuntimeException e) {
            log.error("Failed to create temp file, url={}", url, e);
            return CdnDownloadResult.internalError();
        }
        RequestCallback requestCallback = new SetAcceptRequestCallback();
        DownloadResponseExtractor responseExtractor = new DownloadResponseExtractor(url, localPath);
        try {
            URI uri = URI.create(url);
            return restTemplate.execute(uri, HttpMethod.GET, requestCallback, responseExtractor);
        } catch (RestClientResponseException e) {
            int rawStatusCode = e.getRawStatusCode();
            if (rawStatusCode == HttpStatus.NOT_FOUND.value()) {
                log.info("Download file not found, url={}, localPath={}", url, localPath);
                log.trace("Download file not found, url={}, localPath={}", url, localPath, e);
                return CdnDownloadResult.notFound();
            } else {
                log.error("Download file failed, url={}, localPath={}, statusCode={}", url, localPath, rawStatusCode, e);
                return CdnDownloadResult.downloadFailure();
            }
        } catch (RuntimeException e) {
            log.error("Download file error, url={}, localPath={}", url, localPath, e);
            return CdnDownloadResult.downloadFailure();
        }
    }

    private static class SetAcceptRequestCallback implements RequestCallback {

        @Override
        public void doWithRequest(ClientHttpRequest request) throws IOException {
            request.getHeaders().setAccept(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM, MediaType.ALL));
        }
    }

    private static class DownloadResponseExtractor implements ResponseExtractor<CdnDownloadResult> {

        private final String url;

        private final Path localPath;

        public DownloadResponseExtractor(String url, Path localPath) {
            this.url = url;
            this.localPath = localPath;
        }

        @Override
        public CdnDownloadResult extractData(ClientHttpResponse response) throws IOException {
            Files.copy(response.getBody(), localPath, StandardCopyOption.REPLACE_EXISTING);
            log.info("Download file success, url={}, localPath={}", url, localPath);
            return CdnDownloadResult.success(localPath);
        }
    }
}
