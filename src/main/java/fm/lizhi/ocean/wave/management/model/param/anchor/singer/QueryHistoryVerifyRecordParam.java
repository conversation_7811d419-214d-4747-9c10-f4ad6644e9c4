package fm.lizhi.ocean.wave.management.model.param.anchor.singer;

import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class QueryHistoryVerifyRecordParam {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 歌手类型
     */
    @NotNull(message = "歌手类型不能为空")
    private Integer singerType;
}
