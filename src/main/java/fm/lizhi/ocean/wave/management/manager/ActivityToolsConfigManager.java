package fm.lizhi.ocean.wave.management.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.activitycenter.ActivityToolsConfigConvert;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.SaveActivityToolParam;
import fm.lizhi.ocean.wave.management.model.param.activitycenter.UpdateActivityToolParam;
import fm.lizhi.ocean.wave.management.model.vo.ActivityToolsInfoVo;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.remote.service.activitycenter.ActivityToolsConfigServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityToolsInfoBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityToolsConfigManager {

    @Autowired
    private ActivityToolsConfigServiceRemote activityToolsConfigServiceRemote;



    public ResultVO<Void> save(SaveActivityToolParam param, Integer appId, String operator) {
        Result<Boolean> result = activityToolsConfigServiceRemote.save(param, appId, operator);
        if (ResultUtils.isFailure(result) || !result.target()) {
            return ResultVO.failure(result);
        }
        return ResultVO.success();
    }


    public ResultVO<Void> update(UpdateActivityToolParam param, Integer appId, String operator) {
        Result<Boolean> result = activityToolsConfigServiceRemote.update(param, appId, operator);
        if (ResultUtils.isFailure(result) || !result.target()) {
            return ResultVO.failure(result);
        }
        return ResultVO.success();
    }

    public ResultVO<List<ActivityToolsInfoVo>> list(Integer appId) {
        Result<List<ActivityToolsInfoBean>> result = activityToolsConfigServiceRemote.list(appId);
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure(result);
        }
        return ResultVO.success(ActivityToolsConfigConvert.I.convertActivityToolsInfoBeanList(result.target()));
    }
}
