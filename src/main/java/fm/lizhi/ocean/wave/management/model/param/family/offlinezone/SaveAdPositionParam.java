package fm.lizhi.ocean.wave.management.model.param.family.offlinezone;

import fm.lizhi.ocean.wave.server.common.validator.EnumValue;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneAdPositionStatusEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.beans.Transient;
import java.net.MalformedURLException;

/**
 * 保存广告展位参数, 作为新增和更新的基类
 */
@Data
public abstract class SaveAdPositionParam {

    /**
     * 主题
     */
    @NotEmpty(message = "主题不能为空")
    @Size(max = 30, message = "主题长度不能超过{max}个字")
    private String theme;

    /**
     * 横幅图片
     */
    @NotNull(message = "横幅图片不能为空")
    @URL
    private String banner;

    /**
     * 权重，数字越大越靠前
     */
    @NotNull(message = "权重不能为空")
    private Integer weight;

    /**
     * 跳转链接
     */
    private String jumpLink;

    /**
     * 状态
     */
    @NotNull(message = "上架状态不能为空")
    @EnumValue(OfflineZoneAdPositionStatusEnum.class)
    private Integer status;

    @AssertTrue(message = "跳转链接格式不正确")
    @Transient
    protected boolean isJumpLinkValid() {
        if (StringUtils.isEmpty(jumpLink)) {
            return true;
        }
        try {
            new java.net.URL(jumpLink);
        } catch (MalformedURLException e) {
            // URL格式不正确, 不需要打印堆栈信息, 只需要返回false
            return false;
        }
        return true;
    }
}
