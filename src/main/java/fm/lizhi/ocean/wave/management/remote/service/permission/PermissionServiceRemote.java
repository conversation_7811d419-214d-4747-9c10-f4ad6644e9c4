package fm.lizhi.ocean.wave.management.remote.service.permission;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.platform.api.common.bean.PageResult;
import fm.lizhi.ocean.wave.platform.api.permission.bean.PermissionBean;
import fm.lizhi.ocean.wave.platform.api.permission.bean.PermissionListBean;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestGetPermissionList;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestSavePermission;
import fm.lizhi.ocean.wave.platform.api.permission.service.PermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2025/2/24 17:38
 */
@Component
public class PermissionServiceRemote {

    @Autowired
    private PermissionService permissionService;

    public Result<PageResult<PermissionListBean>> getPermissionList(RequestGetPermissionList request){
        return permissionService.getPermissionList(request);
    }

    public Result<Void> savePermission(RequestSavePermission request){
        return permissionService.savePermission(request);
    }

    public Result<List<PermissionBean>> getPermissionListForResource(){
        return permissionService.getPermissionListForResource();
    }

}
