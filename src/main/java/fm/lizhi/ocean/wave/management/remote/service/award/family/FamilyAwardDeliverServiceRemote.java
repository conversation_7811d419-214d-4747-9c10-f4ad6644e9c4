package fm.lizhi.ocean.wave.management.remote.service.award.family;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.AwardDeliverRecordV1Bean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.AwardDeliverRecordV2Bean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.FamilyAwardDeliverItemBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestAwardDeliverListRecordV1;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestAwardDeliverListRecordV2;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestGetListDeliverItem;
import fm.lizhi.ocean.wavecenter.api.award.family.service.FamilyAwardDeliverService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/26 15:25
 */
@Component
public class FamilyAwardDeliverServiceRemote {

    @Autowired
    private FamilyAwardDeliverService familyAwardDeliverService;

    public Result<PageBean<AwardDeliverRecordV2Bean>> listRecordV2(RequestAwardDeliverListRecordV2 request){
        return familyAwardDeliverService.listRecordV2(request);
    }

    public Result<PageBean<AwardDeliverRecordV1Bean>> listRecordV1(RequestAwardDeliverListRecordV1 request){
        return familyAwardDeliverService.listRecordV1(request);
    }

    public Result<List<FamilyAwardDeliverItemBean>> listDeliverItem(RequestGetListDeliverItem request){
        return familyAwardDeliverService.listDeliverItem(request);
    }

}
