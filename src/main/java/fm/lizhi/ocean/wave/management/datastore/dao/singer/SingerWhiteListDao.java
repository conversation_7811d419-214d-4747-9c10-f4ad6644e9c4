package fm.lizhi.ocean.wave.management.datastore.dao.singer;


import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerWhiteListConfig;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerWhiteListConfigExample;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerWhiteListConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class SingerWhiteListDao {

    @Autowired
    private SingerWhiteListConfigMapper singerWhiteListConfigMapper;


    public boolean insertSingerWhiteList(int appId, <PERSON> singerId, Integer singerType) {
        SingerWhiteListConfig singerWhiteListConfig = new SingerWhiteListConfig();
        singerWhiteListConfig.setAppId(appId);
        singerWhiteListConfig.setSingerId(singerId);
        singerWhiteListConfig.setSingerType(singerType);
        singerWhiteListConfig.setDeployEnv(ConfigUtils.getEnvRequired().name());
        List<SingerWhiteListConfig> exist = singerWhiteListConfigMapper.selectMany(singerWhiteListConfig);
        if (CollectionUtils.isNotEmpty(exist)) {
            return true;
        }
        singerWhiteListConfig.setCreateTime(new Date());
        boolean success = singerWhiteListConfigMapper.insert(singerWhiteListConfig) > 0;
        log.info("insertSingerWhiteList;id={};appId={};singerId={};singerType={}", singerWhiteListConfig.getId(), appId, singerId, singerType);
        return success;
    }


    public boolean removeSingerWhiteList(int appId, Long singerId, Integer singerType) {
        SingerWhiteListConfig singerWhiteListConfig = new SingerWhiteListConfig();
        singerWhiteListConfig.setAppId(appId);
        singerWhiteListConfig.setSingerId(singerId);
        singerWhiteListConfig.setSingerType(singerType);
        singerWhiteListConfig.setDeployEnv(ConfigUtils.getEnvRequired().name());
        List<SingerWhiteListConfig> exist = singerWhiteListConfigMapper.selectMany(singerWhiteListConfig);
        if (CollectionUtils.isEmpty(exist)) {
            return true;
        }
        //物理删除
        Long id = exist.get(0).getId();
        log.info("removeSingerWhiteList;id={};appId={};singerId={};singerType={}", id, appId, singerId, singerType);
        SingerWhiteListConfig deleteByPrimaryKey = new SingerWhiteListConfig();
        deleteByPrimaryKey.setId(id);
        return singerWhiteListConfigMapper.deleteByPrimaryKey(deleteByPrimaryKey) > 0;
    }

    /**
     * 根据歌手ID列表查询白名单信息
     *
     * @param singerIds 歌手ID列表
     * @return 白名单信息列表
     */
    public List<SingerWhiteListConfig> getSingerWhiteListConfigBySingerIds(Integer appId, List<Long> singerIds) {
        SingerWhiteListConfigExample example = new SingerWhiteListConfigExample();
        example.createCriteria().andSingerIdIn(singerIds).andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name()).andAppIdEqualTo(appId);
        return singerWhiteListConfigMapper.selectByExample(example);
    }
}
