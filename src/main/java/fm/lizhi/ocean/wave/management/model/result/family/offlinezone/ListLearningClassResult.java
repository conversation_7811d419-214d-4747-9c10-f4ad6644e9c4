package fm.lizhi.ocean.wave.management.model.result.family.offlinezone;

import com.ctrip.framework.apollo.core.enums.Env;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wave.management.common.serializer.ListLongToListStringSerializer;
import fm.lizhi.ocean.wave.management.constants.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneLearningClassStatusEnum;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneLearningClassTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 学习课堂列表结果
 */
@Data
public class ListLearningClassResult {

    /**
     * 资料ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用ID
     *
     * @see BusinessEvnEnum#appId()
     */
    private Integer appId;

    /**
     * 部署环境
     *
     * @see Env#name()
     */
    private String deployEnv;

    /**
     * 标题
     */
    private String title;

    /**
     * 类型
     *
     * @see OfflineZoneLearningClassTypeEnum
     */
    private Integer type;

    /**
     * 文件链接
     */
    private String fileUrl;

    /**
     * 文件封面
     */
    private String fileCover;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 状态
     *
     * @see OfflineZoneLearningClassStatusEnum
     */
    private Integer status;

    /**
     * 标签
     */
    private String label;

    /**
     * 标签颜色
     */
    private String labelColor;

    /**
     * 是否已删除
     */
    private Boolean deleted;

    /**
     * 操作者
     */
    private String operator;

    /**
     * 创建时间, 毫秒时间戳
     */
    private Long createTime;

    /**
     * 修改时间, 毫秒时间戳
     */
    private Long modifyTime;

    /**
     * 白名单ID列表
     */
    @JsonSerialize(using = ListLongToListStringSerializer.class)
    private List<Long> whiteIds;
}
