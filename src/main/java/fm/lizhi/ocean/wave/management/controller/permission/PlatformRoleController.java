package fm.lizhi.ocean.wave.management.controller.permission;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.management.model.converter.permission.PermissionConvert;
import fm.lizhi.ocean.wave.management.model.param.permission.SavePlatformRoleParam;
import fm.lizhi.ocean.wave.management.model.result.permission.PlatformRoleListResult;
import fm.lizhi.ocean.wave.management.model.vo.ResultVO;
import fm.lizhi.ocean.wave.management.remote.service.permission.PlatformRoleServiceRemote;
import fm.lizhi.ocean.wave.management.util.ResultUtils;
import fm.lizhi.ocean.wave.management.util.SessionExtUtils;
import fm.lizhi.ocean.wave.platform.api.permission.bean.PlatformRoleListBean;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestGetPlatformRoleDetailList;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestSavePlatformRole;
import fm.lizhi.ocean.wave.platform.api.permission.service.PlatformRoleService;
import fm.lizhi.sso.client.SessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/20 14:53
 */
@RestController
@RequestMapping("/permission/platformRole")
public class PlatformRoleController {

    @Autowired
    private PlatformRoleServiceRemote platformRoleServiceRemote;

    /**
     * 角色列表
     * @return
     */
    @GetMapping("list")
    public ResultVO<List<PlatformRoleListResult>> list(){
        Integer appId = SessionExtUtils.getAppId();

        Result<List<PlatformRoleListBean>> result = platformRoleServiceRemote.getPlatformRoleDetailList(new RequestGetPlatformRoleDetailList()
                .setAppId(appId));
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure("系统异常");
        }

        return ResultVO.success(PermissionConvert.I.platformRoleListBeans2VOs(result.target()));
    }

    /**
     * 保存角色
     * @return
     */
    @PostMapping("save")
    public ResultVO<Void> save(@RequestBody @Validated SavePlatformRoleParam param){

        RequestSavePlatformRole request = new RequestSavePlatformRole()
                .setId(param.getId())
                .setAppId(SessionExtUtils.getAppId())
                .setName(param.getRoleName())
                .setOperator(SessionUtils.getAccount())
                .setPermissions(param.getPermissions());
        if (param.getStatementDsl() != null) {
            request.setStatementDsl(param.getStatementDsl().toString());
        }

        Result<Void> result = platformRoleServiceRemote.savePlatformRole(request);
        if (result.rCode() == PlatformRoleService.SAVE_PLATFORM_ROLE_ROLE_NOT_EXIST) {
            return ResultVO.failure("保存的角色不存在");
        }
        if (result.rCode() == PlatformRoleService.SAVE_PLATFORM_ROLE_PERMISSION_NOT_EXIST) {
            return ResultVO.failure("保存的权限不存在");
        }
        if (ResultUtils.isFailure(result)) {
            return ResultVO.failure("系统异常");
        }

        return ResultVO.success();
    }

}
