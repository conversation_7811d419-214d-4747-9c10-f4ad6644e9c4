package fm.lizhi.ocean.wave.management.manager.singer;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerInfoDao;
import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerOperateRecordDao;
import fm.lizhi.ocean.wave.management.datastore.dao.singer.SingerVerifyApplyDao;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerInfoConvert;
import fm.lizhi.ocean.wave.management.model.converter.anchor.singer.SingerOperateRecordConvert;
import fm.lizhi.ocean.wave.management.model.dto.singer.*;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerRecordOperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerOperateRecord;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingerVerifyApplyManager {

    @Autowired
    private SingerVerifyApplyDao singerVerifyApplyDao;

    @Autowired
    private SingerInfoDao singerInfoDao;

    @Autowired
    private SingerOperateRecordDao singerOperateRecordDao;

    /**
     * 审核通过歌手认证申请
     *
     * @return 是否成功
     */
    public boolean approveSingerVerifyRecord(SingerAuditPassedDTO dto) {
        // 查询歌手认证记录
        SingerVerifyRecord singerVerifyRecord = dto.getSingerVerifyRecord();
        // 根据认证申请的用户ID和歌手类型查询歌手信息
        SingerInfo currentsingerInfo = dto.getCurrentSingerInfo();
        if (singerVerifyRecord == null) {
            //打印认证记录为空
            log.warn("approveSingerVerifyRecord singerVerifyRecord is null, recordId:{}", dto.getRecordId());
            return false;
        }

        // 构建歌手信息
        SingerInfo newSingerInfo = SingerInfoConvert.INSTANCE.buildSingerInfo(singerVerifyRecord, currentsingerInfo,
                SingerStatusEnum.EFFECTIVE.getStatus(), dto.getPassSingerType(), dto.getOperator(), dto.getPassSongStyle(), dto.isOriginalSinger());

        //构建当前审核的用户状态的操作流水
        SingerInfoDTO newSingerInfoDTO = SingerInfoConvert.INSTANCE.convertSingerInfoDTO(newSingerInfo);
        SingerOperateRecordDTO operateRecordDto = singerOperateRecordDao.buildSingerOperateRecord(new BuildSingerOperateRecordParamDTO()
                .setSingerInfo(newSingerInfoDTO).setOperateType(SingerRecordOperateTypeEnum.PASS).setOperator(dto.getOperator())
        );
        SingerOperateRecord singerOperateRecord = SingerOperateRecordConvert.I.convertOperateRecord(operateRecordDto);
        try {
            // 审核通过操作
            Integer currentSingerStatus = currentsingerInfo == null ? null : currentsingerInfo.getSingerStatus();
            Integer needDeleteSingerType = !singerVerifyRecord.getSingerType().equals(dto.getPassSingerType()) ? singerVerifyRecord.getSingerType() : null;
            boolean success = singerVerifyApplyDao.approveSingerVerifyRecord(dto.getRecordId(), dto.getCurrentAuditStatus(), currentSingerStatus,
                    dto.getOperator(), newSingerInfo, singerOperateRecord, needDeleteSingerType);
            log.info("approve singer verify record res, recordId:{}, currentStatus:{}, newSingerInfo:{},res:{}", dto.getRecordId(), dto.getCurrentAuditStatus(), JsonUtil.dumps(newSingerInfo), success);
            return success;
        } catch (Exception e) {
            log.error("approve singer verify record failed, recordId:{}, currentStatus:{}", dto.getRecordId(), dto.getCurrentAuditStatus(), e);
            return false;
        }
    }

    /**
     * 修改歌手认证记录状态
     *
     * @param param 修改参数
     * @return 是否修改成功
     */
    public boolean updateSingerVerifyRecordStatus(UpdateSingerVerifyStatusParamDTO param) {
        // 查询歌手认证记录
        SingerVerifyRecord singerVerifyRecord = singerVerifyApplyDao.getSingerVerifyRecordById(param.getId());
        if (singerVerifyRecord == null) {
            return false;
        }

        Integer currentSingerStatus = null;
        SingerInfo singerInfo = null;
        SingerOperateRecord singerOperateRecord = null;
        Integer needDeleteSingerType = null;
        if (param.isNeedUpdateSingerInfo()) {
            //歌手状态不为空，构建歌手信息
            SingerInfo currentSingerInfo = singerInfoDao.getSingerInfo(singerVerifyRecord.getAppId(), singerVerifyRecord.getUserId(), singerVerifyRecord.getSingerType(), true);
            // 构建歌手信息
            singerInfo = SingerInfoConvert.INSTANCE.buildSingerInfo(singerVerifyRecord, currentSingerInfo, param.getTargetSingerStatus(),
                    singerVerifyRecord.getSingerType(), param.getOperator(), param.getPassSongStyle(), param.isOriginalSinger());
            singerInfo.setSingerType(!Objects.equals(param.getPassSingerType(), singerVerifyRecord.getSingerType()) ? param.getPassSingerType() : singerVerifyRecord.getSingerType());
            currentSingerStatus = currentSingerInfo == null ? null : currentSingerInfo.getSingerStatus();

            //下面场景不需要淘汰记录
            boolean notNeedEliminateRecord = currentSingerInfo != null && currentSingerInfo.getSingerStatus() == SingerStatusEnum.AUTHENTICATING.getStatus()
                    && param.getTargetSingerStatus() == SingerStatusEnum.ELIMINATED.getStatus();
            // 构建操作流水，
            SingerInfoDTO newSingerInfoDTO = SingerInfoConvert.INSTANCE.convertSingerInfoDTO(singerInfo);
            SingerRecordOperateTypeEnum operateType = SingerInfoConvert.INSTANCE.singerStatusConvertOperateType(param.getTargetSingerStatus());
            needDeleteSingerType = !singerVerifyRecord.getSingerType().equals(param.getPassSingerType()) ? singerVerifyRecord.getSingerType() : null;
            //如果是从选中状态到
            if (notNeedEliminateRecord) {
                //歌手状态从认证中->淘汰，不需要淘汰记录
                log.info("not need eliminate record, recordId:{}, currentStatus:{}, targetStatus:{}", singerVerifyRecord.getId(), currentSingerStatus, param.getTargetSingerStatus());
            } else {
                SingerOperateRecordDTO operateRecordList = singerOperateRecordDao.buildSingerOperateRecord(
                        new BuildSingerOperateRecordParamDTO()
                                .setSingerInfo(newSingerInfoDTO)
                                .setOperateType(operateType)
                                .setOperator(param.getOperator())
                                .setEliminationReason(param.getRejectReason())
                );
                singerOperateRecord = SingerOperateRecordConvert.I.convertOperateRecord(operateRecordList);
            }
        }

        // 修改歌手认证记录状态
        try {
            return singerVerifyApplyDao.updateSingerVerifyRecordStatus(param.getId(), param.getCurrentAuditStatus(),
                    param.getTargetAuditStatus(), currentSingerStatus, singerInfo, param.getOperator(), param.getRejectReason(),
                    singerOperateRecord, param.getPreAuditRejectReason(), needDeleteSingerType);
        } catch (Exception e) {
            log.error("update singer verify record status failed, param:{}", JsonUtils.toJsonString(param), e);
            return false;
        }
    }

}
